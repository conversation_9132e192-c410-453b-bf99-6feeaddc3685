package com.cet.electric.ngapserver.service;

import com.cet.electric.ngapserver.entity.LineCode;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface LineCodeService {

    /**
     * 创建线路代码
     *
     * @param lineCode 线路代码实体
     * @return 创建后的线路代码实体
     */
    LineCode createLineCode(LineCode lineCode);

    /**
     * 分页查询线路代码
     *
     * @param page      页码
     * @param size      每页大小
     * @param sortBy    排序字段
     * @param sortOrder 排序方向
     * @param keyword   搜索关键词(可选)
     * @return 分页结果
     */
    List<LineCode> getLineCodes(Integer page, Integer size, String sortBy, String sortOrder, String keyword);

    /**
     * 统计符合条件的线路代码总数
     *
     * @param keyword 搜索关键词(可选)
     * @return 总数
     */
    Long countLineCodes(String keyword);

    /**
     * 根据ID查询线路代码
     *
     * @param lineCodeId 线路代码ID
     * @return 线路代码实体
     */
    LineCode getLineCodeById(Long lineCodeId);

    /**
     * 更新线路代码
     *
     * @param lineCode 线路代码实体
     * @return 更新后的线路代码实体
     */
    LineCode updateLineCode(LineCode lineCode);

    /**
     * 删除线路代码
     *
     * @param lineCodeId 线路代码ID
     * @return 删除结果
     */
    boolean deleteLineCode(Long lineCodeId);
}

