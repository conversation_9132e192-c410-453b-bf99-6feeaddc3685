<template>
  <el-autocomplete
    v-model="value"
    class="frame-nav-search"
    prefix-icon="el-icon-search"
    :fetch-suggestions="evSearchQuery"
    :placeholder="i18n('搜索...')"
    highlight-first-item
    popper-class="frame-nav-search-popper"
    @select="evSelectClick"
    :trigger-on-focus="false"
    clearable
  >
    <template slot-scope="{ item }">
      <span :title="item.value" v-html="item.label" />
    </template>
  </el-autocomplete>
</template>

<script>
import { i18n } from "@omega/layout/local/index.js";
export default {
  name: "FrameNavSearch",
  data() {
    return {
      value: ""
    };
  },
  props: ["navmenu"],
  methods: {
    evSearchQuery(keyword, cb) {
      let list = this.flattenNavmenu();

      const keywords = keyword.split(/\/|\s+/);
      list = list.filter(item => {
        return keywords.some(
          word => ~item.value.toLowerCase().indexOf(word.toLowerCase())
        );
      });

      if (list.length) {
        const regx = new RegExp(keywords.join("|"), "ig");
        list.forEach(item => {
          item.label = item.value.replace(regx, "<b>$&</b>");
        });
      }
      cb(list);
    },
    evSelectClick(item) {
      this.$router.push(item.location);
    },
    flattenNavmenu() {
      const list = [];
      function loop(items, pathNodes) {
        for (const item of items) {
          const _pathNodes = pathNodes.concat(item);
          if (item.subMenuList) {
            loop(item.subMenuList, _pathNodes);
          } else {
            list.push({
              value: _pathNodes.map(node => node.label).join("/"),
              location: item.location
            });
          }
        }
      }
      loop(this.navmenu, []);

      return list;
    },
    i18n
  }
};
</script>

<style lang="scss">
.frame-nav-search input {
  border-radius: 15px;
  border: none;
  @include background_color(BG);
}
.frame-nav-search-popper {
  width: auto !important;
  max-width: 400px;
}
</style>
