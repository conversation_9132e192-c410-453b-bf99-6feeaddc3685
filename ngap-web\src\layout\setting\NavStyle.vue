<template>
  <div class="nav-style">
    <div
      v-for="item in items"
      class="nav-style-item"
      :class="item.clazz"
      :key="item.id"
      :title="item.desc"
      @click="evClick(item)"
    >
      <transition name="el-fade-in-linear">
        <i v-show="item.active" class="el-icon-success" />
      </transition>
    </div>
  </div>
</template>

<script>
import { localStorageDb } from "@omega/layout/utils/db.js";
import { store } from "@omega/layout";
export default {
  name: "NavStyle",
  data() {
    const activeId = localStorageDb.get("navmenuMixTheme");
    return {
      activeId: _.isEmpty(activeId) ? "unite" : "mix"
    };
  },
  computed: {
    items() {
      const NAV_STYLE_MAP = {
        UNITE: ["unite", $T("统一"), "nav-style-unite"],
        MIX: ["mix", $T("混合"), "nav-style-mix"]
      };

      const items = [];
      for (const key of Object.keys(NAV_STYLE_MAP)) {
        const ret = this.setMap(key, NAV_STYLE_MAP[key]);

        if (ret === false) {
          continue;
        }
        items.push(ret);
      }
      return items;
    }
  },
  methods: {
    setMap(key, [id, desc, clazz] = NAV_STYLE_MAP[key]) {
      return {
        id,
        desc,
        clazz: `${clazz} ${store.state.layoutMode}`,
        active: this.activeId == id
      };
    },
    evClick(item) {
      if (item.active) {
        return;
      }
      let type = item.id == "mix" ? "dark" : "";
      store.setNavmenuMixTheme(type);
      this.activeId = item.id;
    }
  }
};
</script>
<style lang="scss" scoped>
.nav-style {
  display: flex;
  justify-content: center;
  & > *:not(:last-child) {
    @include margin_right(J4);
  }
}
.nav-style-item {
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  width: 64px;
  height: 36px;
  border-radius: 4px;
  cursor: pointer;
  transition-duration: 0.3s;
  border: 1px solid;
  @include border_color(Sta6);
  &:hover {
    opacity: 0.8;
  }
  .el-icon-success {
    @include font_color(ZS);
  }
}

.vertical {
  &.nav-style-unite {
    padding-left: 12px;
    position: relative;
    &::after {
      position: absolute;
      content: "";
      top: 0;
      bottom: 0;
      left: 12px;
      width: 1px;
      border-left: 1px solid;
      @include border_color(Sta6);
    }
  }
  &.nav-style-mix {
    border-left: 12px solid;
    @include border_direction_color(Sta6, left);
  }
}
.horizontal {
  &.nav-style-unite {
    padding-top: 12px;
    position: relative;
    &::after {
      position: absolute;
      content: "";
      bottom: 0;
      top: 8px;
      border-bottom: 1px solid;
      height: 1px;
      width: 100%;
      @include border_color(Sta6);
    }
  }
  &.nav-style-mix {
    border-top: 8px solid;
    @include border_direction_color(Sta6, top);
  }
}
.self-disabled {
  cursor: not-allowed;
}
</style>
