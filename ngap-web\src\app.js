/*
 * @Author: your name
 * @Date: 2022-04-06 16:16:36
 * @LastEditTime: 2025-06-09 11:24:15
 * @LastEditors: soft\luwei <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \omega-repo\template\src\main.js
 */
import omegaApp from "@omega/app";
import "./omega/index";
import "./resources/index.css";
import "./resources/index.scss";
// 项目内公共组件引入
import "@/projects/components/index.js";
import "echarts-liquidfill";
import "./icons/index.js";
import Vue from "vue";
// 秒级曲线表格虚拟滚动
import VXETable from "vxe-table";
import "vxe-table/lib/style.css";
Vue.use(VXETable);

import lodash from "lodash";
import moment from "moment";
import ElementUI from "element-ui";
import routerOption from "./router";
import storeOption from "./store";

import enums from "./config/enums";
import { mhCONST } from "./config/const.js";

import customApi from "./api/custom";
import CetCommon from "cet-common";
import CetChart from "cet-chart";
import config from "@/config/config";

import "@omega/video";

import options from "./config/options.js";
for (let key in options) {
  mhCONST(key, options[key]);
}

Vue.use(CetCommon, {
  api: customApi,
  CetDialog: { isDraggable: true },
  CetDateSelect: { isClosedEnd: false }
});
Vue.use(CetChart, {
  // themeConf: {
  // backgroundColor: "#000"
  // }
});

Vue.config.productionTip = false;

Vue.prototype.$enums = enums;
Vue.prototype.$const = mhCONST;

// 工具库
Vue.prototype._ = lodash;

moment.locale("zh-cn");
Vue.prototype.$moment = moment;

// ElementUI
Vue.use(ElementUI, { size: "small" });

//引入趋势曲线组件
import OmegaTend from "@omega/trend";
Vue.use(OmegaTend);

// 语言切换后强制刷新，清除缓存后强制刷新
window.addEventListener("storage", function (event) {
  if (event.key === "omega_language" || !event.key) {
    location.reload();
  }
});

// 启动
omegaApp.createApp({
  el: "#app",
  config: { navmenu: config.navmenu },
  routerOption,
  storeOption
});
