import os
import json
import logging
from pathlib import Path

from flask import Flask, request, jsonify
import opendssdirect as dss

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 配置项
CONFIG = {
    'STRATEGY_PATHS': {
        'REACTIVE_POWER_VOLTAGE_CONTROL': Path('voltage_control.py'),
        'ACTIVE_POWER_VOLTAGE_CONTROL': Path('storage_voltage_control.py')
    }
}

app = Flask(__name__)

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({
        'status': 'healthy',
        'service': 'NGAP Engine API',
        'version': '1.0.0'
    }), 200

@app.route('/run-dss-with-strategy', methods=['POST'])
def run_dss_with_strategy():
    """执行DSS文件并可选择应用控制策略

    请求参数:
        dssFilePath: DSS文件的路径（必需）
        json_data: 包含策略参数的JSON字符串（可选）

    返回:
        执行结果的JSON响应
    """
    try:
        # 获取请求中的 DSS 文件路径和策略参数
        dss_file_path = request.json.get('dssFilePath')
        json_data = request.json.get('json_data')

        # 验证输入参数
        if not dss_file_path:
            return jsonify({'error': 'DSS file path is required'}), 400

        # 标准化文件路径，处理双斜杠开头的路径问题
        if dss_file_path.startswith('//'):
            dss_file_path = dss_file_path[1:]  # 移除开头的一个斜杠
            logger.info(f"Normalized path from '//' to '/': {dss_file_path}")

        # 使用 os.path.normpath 进一步标准化路径
        dss_file_path = os.path.normpath(dss_file_path)
        logger.info(f"Final normalized path: {dss_file_path}")

        # 检查 DSS 文件路径是否存在
        if not os.path.exists(dss_file_path):
            return jsonify({'error': f'DSS file not found: {dss_file_path}'}), 400

        # 清空之前的 OpenDSS 模型
        dss.Basic.ClearAll()

        # 使用 compile 命令执行 DSS 文件
        compile_command = f"compile {dss_file_path}"
        logger.info(f"Compiling DSS file: {dss_file_path}")
        for i in range(2):  # 执行两次编译
            dss.Text.Command(compile_command)

        # 准备基础返回结果
        result = {
            'status': 'success',
            'message': 'DSS script executed successfully'
        }

        # 检查是否需要执行策略
        should_execute_strategy = False
        strategy_type = None

        if json_data and json_data.strip():
            strategy_type = get_strategy_type(json_data)
            if strategy_type:
                should_execute_strategy = True
            else:
                logger.info(f"No valid strategy type found in JSON data, treating as no strategy: {json_data}")

        if should_execute_strategy:
            # 执行策略
            strategy_file_path = CONFIG['STRATEGY_PATHS'].get(strategy_type, "")
            strategy_file_path_str = str(strategy_file_path)

            if strategy_file_path and os.path.exists(strategy_file_path_str):
                logger.info(f"Executing strategy: {strategy_type} using {strategy_file_path_str}")
                strategy_result = execute_strategy(strategy_file_path_str, json_data, dss_file_path)
                result.update({
                    'strategy_type': strategy_type,
                    'strategy_result': strategy_result,
                    'message': 'DSS script executed successfully with strategy'
                })
            else:
                error_msg = f"Strategy file not found: {strategy_file_path_str}"
                logger.error(error_msg)
                return jsonify({'error': error_msg}), 400
        else:
            logger.info("No valid strategy parameters provided, executing DSS file only")
            result['message'] = 'DSS script executed successfully without strategy'

        return jsonify(result), 200

    except Exception as e:
        logger.exception(f"Error executing DSS: {str(e)}")
        return jsonify({'error': str(e)}), 500
        

def get_strategy_type(json_data):
    """从JSON数据中确定策略类型
    
    Args:
        json_data: 包含策略参数的JSON字符串
        
    Returns:
        策略类型字符串，如果没有找到有效的策略类型则返回None
    """
    try:
        if "REACTIVE_POWER_VOLTAGE_CONTROL" in json_data:
            return "REACTIVE_POWER_VOLTAGE_CONTROL"
        elif "ACTIVE_POWER_VOLTAGE_CONTROL" in json_data:
            return "ACTIVE_POWER_VOLTAGE_CONTROL"
        return None
    except Exception as e:
        logger.error(f"Error determining strategy type: {str(e)}")
        return None

def execute_strategy(strategy_file_path, json_data, dss_file_path):
    """执行指定的策略文件
    
    Args:
        strategy_file_path: 策略文件的路径
        json_data: 包含策略参数的JSON字符串
        dss_file_path: DSS文件的路径
        
    Returns:
        策略执行的结果信息
    """
    try:
        # 解析 JSON 数据
        parsed_data = json.loads(json_data)
        strategy_type = get_strategy_type(json_data)
        
        if not strategy_type or strategy_type not in parsed_data:
            raise ValueError(f"Invalid strategy type: {strategy_type}")
            
        # 获取参数列表
        params_list = parsed_data[strategy_type]
        var_name = "params_list"

        # 设置输出目录 - 使用pathlib处理路径，更安全
        output_dir = Path(dss_file_path).parent
        output_dir_str = str(output_dir)
        
        # 处理路径中的反斜杠，确保它们被正确转义
        output_dir_escaped = output_dir_str.replace('\\', '\\\\')

        # 生成参数赋值代码
        params_code = f'{var_name}={json.dumps(params_list, indent=4)}'
        dir_code = f'output_dir="{output_dir_escaped}"'

        # 读取策略文件内容
        try:
            with open(strategy_file_path, 'r', encoding='utf-8') as file:
                strategy_script = file.read()
        except Exception as e:
            logger.error(f"Error reading strategy file: {str(e)}")
            raise IOError(f"Cannot read strategy file: {str(e)}")

        # 将参数赋值和输出目录赋值放在第一行
        strategy_script = params_code + '\n' + dir_code + '\n' + strategy_script

        # 在全局作用域中执行策略文件内容
        logger.info(f"Executing strategy script with {len(params_list)} parameter sets")
        exec(strategy_script, globals())

        # 返回策略执行成功的信息
        return "Strategy executed successfully"

    except json.JSONDecodeError as e:
        error_msg = f"Invalid JSON data: {str(e)}"
        logger.error(error_msg)
        return error_msg
    except ValueError as e:
        error_msg = f"Value error: {str(e)}"
        logger.error(error_msg) 
        return error_msg
    except Exception as e:
        error_msg = f"Error executing strategy: {str(e)}"
        logger.exception(error_msg)
        return error_msg

if __name__ == '__main__':
    # 检查配置的策略文件是否存在
    for strategy_type, path in CONFIG['STRATEGY_PATHS'].items():
        if not os.path.exists(path):
            logger.warning(f"Strategy file for {strategy_type} not found at {path}")
    
    # 启动Flask应用
    logger.info("Starting NGAP Engine API server")
    app.run(debug=True, host='0.0.0.0', port=5000)