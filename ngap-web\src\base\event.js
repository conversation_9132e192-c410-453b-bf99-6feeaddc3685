/**
 * @file event
 */

class EventManager {
  constructor() {
    this._cache = {};
  }

  createEvent(type, cb, option = {}) {
    const event = {
      type: type,
      cb: cb,
      ctx: option.ctx || null
    };
    return event;
  }

  getEventsMod(type) {
    if (this._cache[type]) {
      return this._cache[type];
    }

    return (this._cache[type] = {
      events: new Set(),
      isonly: false
    });
  }

  getEvents(type) {
    const eventsMod = this.getEventsMod(type);
    return eventsMod.events;
  }

  setEventsModOption(type, { isonly = false } = {}) {
    Object.assign(this.getEventsMod(type), {
      isonly: isonly
    });
  }

  removeEventsMod(type) {
    delete this._cache[type];
  }

  addEvent(type, cb, option) {
    const eventsMod = this.getEventsMod(type);
    if (eventsMod.isonly) {
      throw Error(`The events includes same name; ${type} has register`);
    }
    this.setEventsModOption(type, option);
    eventsMod.events.add(this.createEvent(type, cb, option));
  }

  removeEvent(type, cb) {
    if (!cb) {
      this.removeEventsMod(type);
      return;
    }
    const events = this.getEvents(type);
    for (const event of events) {
      if (event.cb === cb) {
        events.delete(event);
        if (!events.size) {
          this.removeEventsMod(type);
        }
        break;
      }
    }
  }
}

export class Event {
  constructor() {
    this.eventManager = new EventManager();
  }

  /**
   * 绑定事件
   *
   * @param {String} type [必须] 事件类型
   * @param {Function} cb [必须] 事件回调
   * @param {Object} option [非必须] 配置项
   * @param {Boolean} option.isonly 该事件在事件列表中是否唯一。即有且仅有一处监听
   */
  on(type, cb, option) {
    this.eventManager.addEvent(type, cb, option);
    return this;
  }

  /**
   * 仅绑定触发一次事件即解绑
   *
   * @param {String} type [必须] 事件类型
   * @param {Function} cb [必须] 事件回调
   * @param {Object} option [非必须] 配置项
   * @param {Boolean} option.isonly 该事件在事件列表中是否唯一。即有且仅有一处监听
   */
  once(type, cb, option) {
    const _this = this;
    const cbFn = function () {
      _this.off(type, cbFn);
      return cb.apply(this, arguments);
    };
    this.eventManager.addEvent(type, cbFn, option);
  }

  /**
   * 解除绑定事件
   *
   * @param {String} type [必须] 事件类型
   * @param {Function} cb 事件原始的监听函数，如果没有会删除所有该类型事件的监听
   */
  off(type, cb) {
    this.eventManager.removeEvent(type, cb);
    return this;
  }

  /**
   * 触发监听事件
   *
   * @param {String} type 事件类型
   * @param {Any} payload 要传递的数据
   */
  emit(type, payload) {
    const events = this.eventManager.getEvents(type);
    events.forEach(event => {
      const { cb, ctx } = event;
      cb.call(ctx, payload);
    });
    return this;
  }

  /**
   * 触发监听事件-所有监听事件只被触发一次就解绑
   * @param {String} type 事件类型
   * @param {Any} payload 要传递的数据
   */
  emitOnce(type, payload) {
    this.emit(type, payload);
    this.off(type);
  }

  /**
   * 触发第一个监听的事件，一般结合监听时的 isonly 来使用
   *
   * @param {String} type 事件类型
   * @param {Any} payload 要传递的数据
   */
  send(type, payload) {
    const events = this.eventManager.getEvents(type);
    if (events.size) {
      const [{ cb, ctx }] = events;
      return cb && cb.call(ctx, payload);
    }
  }
}

export const mhEvent = new Event();
