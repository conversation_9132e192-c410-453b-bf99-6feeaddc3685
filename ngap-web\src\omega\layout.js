import { OmegaLayoutPlugin, SettingItem } from "@omega/layout";
import omegaApp from "@omega/app";
import VframeMenuFooter from "./component/VframeMenuFooter";
import User from "@/layout/user/index.vue";
import NavStyle from "@/layout/setting/NavStyle";
import OmegaTheme from "@omega/theme";
import { mhCONST } from "@/config/const";
import Notice from "@/layout/notice/index.vue";

omegaApp.plugin.register(OmegaLayoutPlugin, {
  // isSidebarCollapse: false,
  // layoutMode: "vertical",
  isShowFavorites: false,
  isShowSetting: false,
  // isShowSettingTheme: true,
  isShowSearch: false,
  isShowSettingLanguage: false,
  settingThemeList: [
    ["blue", "蓝色", "#7C88E6"],
    ["darkblue", "深蓝色", "#1F3087"],
    ["dark", "暗色", "#2c3143"],
    ["black", "黑色", "#000"]
  ],
  renderHeaderRightTools(h) {
    return [<Notice />, <User />];
  },
  // renderSettingItems(h) {
  //   return [
  //     OmegaTheme.theme !== "dark"
  //       ? h(
  //           SettingItem,
  //           {
  //             props: {
  //               title: $T("导航风格"),
  //               symbolId: "layout-nav-style-lin"
  //             }
  //           },
  //           [h(NavStyle)]
  //         )
  //       : h()
  //   ];
  // },
  renderNavFooter(h) {
    return [<VframeMenuFooter />];
  }
});
