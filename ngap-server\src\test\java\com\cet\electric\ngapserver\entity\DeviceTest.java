package com.cet.electric.ngapserver.entity;

import org.junit.Test;

import static org.junit.Assert.*;

public class DeviceTest {

    @Test
    public void testDeviceBuilder() {
        // 准备测试数据
        Long parameterId = 1L;
        String deviceType = "LINE";
        String parameterCode = "TEST_PARAM_001";
        String version = "1.0";

        // 使用Builder创建Device对象
        Device device = Device.builder()
                .parameterId(parameterId)
                .deviceType(deviceType)
                .parameterCode(parameterCode)
                .version(version)
                .build();

        // 验证结果
        assertNotNull(device);
        assertEquals(parameterId, device.getParameterId());
        assertEquals(deviceType, device.getDeviceType());
        assertEquals(parameterCode, device.getParameterCode());
        assertEquals(version, device.getVersion());
    }

    @Test
    public void testDeviceNoArgsConstructor() {
        // 使用无参构造函数创建Device对象
        Device device = new Device();

        // 验证结果
        assertNotNull(device);
        assertNull(device.getParameterId());
        assertNull(device.getDeviceType());
        assertNull(device.getParameterCode());
        assertNull(device.getVersion());
    }

    @Test
    public void testDeviceAllArgsConstructor() {
        // 准备测试数据
        Long parameterId = 1L;
        String deviceType = "LINE";
        String parameterCode = "TEST_PARAM_001";
        String version = "1.0";

        // 使用全参构造函数创建Device对象
        Device device = new Device(parameterId, deviceType, parameterCode, version);

        // 验证结果
        assertNotNull(device);
        assertEquals(parameterId, device.getParameterId());
        assertEquals(deviceType, device.getDeviceType());
        assertEquals(parameterCode, device.getParameterCode());
        assertEquals(version, device.getVersion());
    }

    @Test
    public void testDeviceSettersAndGetters() {
        // 创建Device对象
        Device device = new Device();

        // 准备测试数据
        Long parameterId = 1L;
        String deviceType = "TRANSFORMER";
        String parameterCode = "TEST_PARAM_002";
        String version = "2.0";

        // 使用Setter设置值
        device.setParameterId(parameterId);
        device.setDeviceType(deviceType);
        device.setParameterCode(parameterCode);
        device.setVersion(version);

        // 使用Getter验证值
        assertEquals(parameterId, device.getParameterId());
        assertEquals(deviceType, device.getDeviceType());
        assertEquals(parameterCode, device.getParameterCode());
        assertEquals(version, device.getVersion());
    }

    @Test
    public void testDeviceEqualsAndHashCode() {
        // 创建两个相同的Device对象
        Device device1 = Device.builder()
                .parameterId(1L)
                .deviceType("LINE")
                .parameterCode("TEST_PARAM_001")
                .version("1.0")
                .build();

        Device device2 = Device.builder()
                .parameterId(1L)
                .deviceType("LINE")
                .parameterCode("TEST_PARAM_001")
                .version("1.0")
                .build();

        // 验证equals和hashCode
        assertEquals(device1, device2);
        assertEquals(device1.hashCode(), device2.hashCode());
    }

    @Test
    public void testDeviceToString() {
        // 创建Device对象
        Device device = Device.builder()
                .parameterId(1L)
                .deviceType("LINE")
                .parameterCode("TEST_PARAM_001")
                .version("1.0")
                .build();

        // 验证toString方法
        String toString = device.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("Device"));
        assertTrue(toString.contains("parameterId=1"));
        assertTrue(toString.contains("deviceType=LINE"));
        assertTrue(toString.contains("parameterCode=TEST_PARAM_001"));
        assertTrue(toString.contains("version=1.0"));
    }

    @Test
    public void testDeviceWithNullValues() {
        // 创建包含null值的Device对象
        Device device = Device.builder()
                .parameterId(null)
                .deviceType(null)
                .parameterCode(null)
                .version(null)
                .build();

        // 验证null值处理
        assertNotNull(device);
        assertNull(device.getParameterId());
        assertNull(device.getDeviceType());
        assertNull(device.getParameterCode());
        assertNull(device.getVersion());
    }

    @Test
    public void testDeviceWithEmptyStrings() {
        // 创建包含空字符串的Device对象
        Device device = Device.builder()
                .parameterId(1L)
                .deviceType("")
                .parameterCode("")
                .version("")
                .build();

        // 验证空字符串处理
        assertNotNull(device);
        assertEquals(Long.valueOf(1L), device.getParameterId());
        assertEquals("", device.getDeviceType());
        assertEquals("", device.getParameterCode());
        assertEquals("", device.getVersion());
    }

    @Test
    public void testDeviceWithSpecialCharacters() {
        // 创建包含特殊字符的Device对象
        Device device = Device.builder()
                .parameterId(1L)
                .deviceType("LINE-TYPE_001")
                .parameterCode("PARAM@CODE#001")
                .version("1.0-BETA")
                .build();

        // 验证特殊字符处理
        assertNotNull(device);
        assertEquals("LINE-TYPE_001", device.getDeviceType());
        assertEquals("PARAM@CODE#001", device.getParameterCode());
        assertEquals("1.0-BETA", device.getVersion());
    }
}
