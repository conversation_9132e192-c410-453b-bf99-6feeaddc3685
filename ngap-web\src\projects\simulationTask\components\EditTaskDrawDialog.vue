<template>
  <div style="position: relative" class="drawDialog">
    <CetDialog v-bind="CetDialog_pagedialog" v-on="CetDialog_pagedialog.event">
      <template v-slot:footer>
        <div class="footBtn">
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- back按钮组件 -->
          <!-- <CetButton
            v-if="active !== 1"
            v-bind="CetButton_back"
            v-on="CetButton_back.event"
          ></CetButton> -->
          <!-- next按钮组件 -->
          <!-- <CetButton
            v-if="active !== 2"
            v-bind="CetButton_next"
            v-on="CetButton_next.event"
          ></CetButton> -->
        </div>
      </template>
      <div class="fullheight">
        <keep-alive>
          <VisualizationTool
            v-if="showTool"
            :inputData_in="inputData_in"
            style="height: calc(100% - 25px)"
          ></VisualizationTool>
        </keep-alive>
        <!-- <el-card class="mt8 mb8 step fullheight">
          <div
            style="display: flex; justify-content: center; height: 30px"
            class="mb5"
          >
            <el-steps
              style="width: 35%"
              :active="active"
              finish-status="success"
              simple
            >
              <el-step title="配置页面"></el-step>
              <el-step title="运行结果"></el-step>
            </el-steps>
          </div>
          <keep-alive>
            <VisualizationTool
              v-if="showTool"
              :inputData_in="inputData_in"
              style="height: calc(100% - 25px)"
              v-show="active == 1"
            ></VisualizationTool>
          </keep-alive>
          <ViewTaskResult
            :inputData_in="inputData_in"
            style="height: calc(100% - 25px)"
            v-if="active == 2"
          ></ViewTaskResult>
        </el-card> -->
      </div>
    </CetDialog>
  </div>
</template>
<script>
import common from "@/utils/common";
import customApi from "@/api/custom";
import { mhCONST } from "@/config/const";
import VisualizationTool from "./VisualizationTool.vue";
// import ViewTaskResult from "./ViewTaskResult.vue";
export default {
  name: "EditTaskDrawDialog",
  components: { VisualizationTool /* ViewTaskResult */ },
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      active: 1,
      showTool: false,
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        fullscreen: true,
        showClose: true,
        // destroyOnClose: true,
        title: "编辑配置",
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },

      // // back组件
      // CetButton_back: {
      //   visible_in: true,
      //   disable_in: false,
      //   title: "上一步",
      //   type: "primary",
      //   plain: true,
      //   event: {
      //     statusTrigger_out: this.CetButton_back_statusTrigger_out
      //   }
      // },
      // // next组件
      // CetButton_next: {
      //   visible_in: true,
      //   disable_in: false,
      //   title: "下一步",
      //   type: "primary",
      //   plain: true,
      //   event: {
      //     statusTrigger_out: this.CetButton_next_statusTrigger_out
      //   }
      // },
      // back组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "运行",
        plain: true,
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.active = 1;
      this.showTool = true;
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {},
    inputData_in(val) {}
  },
  methods: {
    CetDialog_pagedialog_openTrigger_out(val) {
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.showTool = false;
      this.$emit("closeTrigger_out", val);
    },
    // // 上一步
    // CetButton_back_statusTrigger_out(val) {
    //   this.active--;
    // },
    // // 下一步
    // CetButton_next_statusTrigger_out(val) {
    //   customApi["runNextStep"](this.inputData_in?.simulationId).then(res => {
    //     if (res && res.code === 0) {
    //       this.active++;
    //     }
    //   });
    // },
    CetButton_cancel_statusTrigger_out(val) {
      customApi["runNextStep"](this.inputData_in?.simulationId, true).then(
        res => {
          if (!_.isEmpty(res) && res.code != 0) {
            this.$message.warning({
              showClose: true,
              message: res.msg,
              duration: 5000
            });
          } else {
            this.$message({
              showClose: true,
              message: "正在后台运行，请稍后刷新查看结果",
              duration: 5000
            });
            this.showTool = false;
            this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
          }
        }
      );
    },
    no() {}
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.drawDialog {
  .footBtn {
    position: absolute;
    bottom: 10px;
    right: 10px;
  }
  ::v-deep .el-dialog__header {
    padding: 10px 20px 15px;
  }
  ::v-deep .el-dialog__body {
    padding: 0px 15px;
    height: calc(100% - 100px);
  }

  ::v-deep .el-dialog__footer {
    padding: 0px;
  }

  .step::v-deep .el-card__body {
    padding: 0px;
    height: calc(100% - 10px);
  }
}
</style>
