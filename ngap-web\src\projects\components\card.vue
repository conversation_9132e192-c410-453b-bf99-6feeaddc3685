<template>
  <div class="card">
    <div class="card-title" v-if="$slots.title || title">
      <slot name="title">
        <div class="card-title-sign card-title-body" :title="title">
          {{ title }}
        </div>
        <omega-icon
          class="card-title-icon"
          @click="iconClick"
          v-if="titleIcon"
          :symbolId="titleIcon"
        />
        <i
          class="card-title-icon"
          @click="iconClick"
          v-if="elementIcon"
          :title="iconTitle"
          :class="elementIcon"
        />
      </slot>
    </div>
    <div class="card-body" :style="bodyStyle">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "card",
  components: {},
  props: {
    bodyStyle: {},
    title: {
      type: String,
      default: ""
    },
    titleIcon: {
      type: String,
      default: ""
    },
    elementIcon: {
      type: String,
      default: ""
    },
    iconTitle: {
      type: String,
      default: ""
    }
  },
  methods: {
    iconClick() {
      this.$emit("iconClick");
    }
  }
};
</script>

<style lang="scss" scoped>
.card {
  display: flex;
  position: relative;
  width: 100%;
  height: 100%;
  flex-direction: column;
  min-width: 0;
  @include background_color(BG1);
  background-clip: border-box;
  border-radius: 2px;
  box-sizing: border-box;
  // box-shadow: $box-shdow;
  &-title {
    width: 100%;
    @include font_size(H3);
    @include font_color(T1);
    line-height: 20px;
    display: flex;
    @include padding(16px 16px 0);
    box-sizing: border-box;
    align-items: flex-start;

    &-sign {
      position: relative;
      padding-left: 12px;
      &::before {
        position: absolute;
        left: 0px;
        top: 2px;
        content: "";
        width: 4px;
        height: 16px;
        @include background_color(ZS);
      }
    }
    &-body {
      flex: 1;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      padding-right: 10px;
    }
    &-icon {
      // @include font_color(T1);
      color: #989898;
      font-size: 18px;
      cursor: pointer;
    }
  }
  &-body {
    flex: 1 1 auto;
    min-height: 1px;
    padding: 16px;
    font-size: 14px;
  }
}
</style>
