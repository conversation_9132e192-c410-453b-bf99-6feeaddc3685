package com.cet.electric.ngapserver.service.impl;

import com.cet.electric.ngapserver.dao.ProjectDao;
import com.cet.electric.ngapserver.dao.SimulationDao;
import com.cet.electric.ngapserver.dto.ErrorMsg;
import com.cet.electric.ngapserver.entity.Project;
import com.cet.electric.ngapserver.entity.Simulation;
import com.cet.electric.ngapserver.enums.RunStatus;
import com.cet.electric.ngapserver.service.ProjectService;
import com.cet.electric.ngapserver.util.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.cet.electric.ngapserver.util.FileUtils.downloadFile;
import static com.cet.electric.ngapserver.util.JsonUtils.convertJsonFileToMap;
import static com.cet.electric.ngapserver.util.JsonUtils.convertObjectToJsonString;

/**
 * <AUTHOR>
 */
@Service
public class ProjectServiceImpl implements ProjectService {
    private static final Logger log = LoggerFactory.getLogger(ProjectServiceImpl.class);

    private final Object projectLock = new Object();

    @Autowired
    private ProjectDao projectDao;

    @Autowired
    private SimulationDao simulationDao;

    /**
     *
     * 创建项目
     *
     * @param project 待创建的项目对象，必须包含项目名称
     * @return 创建成功的项目对象，包含生成的项目ID
     */
    @Override
    public Project createProject(Project project) {

        // 检查项目名称是否为空
        if (project.getProjectName() == null || project.getProjectName().trim().isEmpty()) {
            log.error("项目创建失败：项目名称不能为空");
            throw new ErrorMsg(-1, "项目创建失败：项目名称不能为空.");
        }

        // 检查项目名称是否已存在
        String projectName = project.getProjectName().trim();
        Project existingProject = projectDao.findByProjectName(projectName);
        if (existingProject != null && existingProject.getIsDeleted() == 0) {
            log.error("项目创建失败：项目名称已存在: {}", projectName);
            throw new ErrorMsg(-1, "项目创建失败：项目名称已存在.");
        }

        // 设置项目名称为去除空格后的值
        project.setProjectName(projectName);

        synchronized (projectLock) {
            // 保存项目
            int result = projectDao.createProject(project);
            if (result <= 0) {
                log.error("项目创建失败: {}", project);
                throw new ErrorMsg(-1, "数据库中项目创建失败.");
            }
        }

        log.info("项目创建成功: {}", project);

        // 返回创建的项目
        return project;
    }

    /**
     * 获取项目场景列表
     *
     * @return 项目场景列表，key为英文，value为中文
     */
    @Override
    public Map<String, String> getProjectScenarios() {
        log.info("开始获取项目场景列表");

        // 创建返回的场景映射
        Map<String, String> scenarios = new LinkedHashMap<>();

        // 添加预定义的场景类型
        scenarios.put("General_scenario", "通用场景");
        scenarios.put("Voltage_qualification_scenario", "电压合格率场景");

        log.info("项目场景列表获取成功，总数量: {}", scenarios.size());
        return scenarios;
    }


    /**
     * 分页查询项目
     *
     * @param page        页码
     * @param size        每页大小
     * @param sortBy      排序字段
     * @param sortOrder   排序方向
     * @param projectType 项目类型(可选)
     * @param keyword     搜索关键词(可选)
     * @param startTime   起始时间(时间戳-毫秒)
     * @param endTime     结束时间(时间戳-毫秒)
     * @return 分页结果
     */
    @Override
    public List<Project> getProjects(Integer page, Integer size, String sortBy, String sortOrder, String projectType, String keyword, Long startTime, Long endTime) {
        log.info("开始查询项目列表, page: {}, size: {}, sortBy: {}, sortOrder: {}, projectType: {}, keyword: {}",
                page, size, sortBy, sortOrder, projectType, keyword);

        // 参数校验
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }

        // 验证排序字段
        List<String> validSortFields = Arrays.asList("created_at", "updated_at", "project_type");
        if (sortBy == null || !validSortFields.contains(sortBy)) {
            log.warn("无效的排序字段: {}，使用默认排序字段 created_at", sortBy);
            sortBy = "created_at";
        }

        // 验证排序方向
        if ((!"asc".equalsIgnoreCase(sortOrder) && !"desc".equalsIgnoreCase(sortOrder))) {
            log.warn("无效的排序方向: {}，使用默认排序方向 desc", sortOrder);
            sortOrder = "desc";
        }

        // 验证时间范围
        if (startTime != null && endTime != null && startTime > endTime) {
            log.warn("起始时间大于结束时间，交换时间范围");
            Long temp = startTime;
            startTime = endTime;
            endTime = temp;
        }

        // 计算分页参数
        int offset = (page - 1) * size;

        // 查询数据列表
        List<Project> projects = projectDao.getProjects(offset, size, sortBy, sortOrder, projectType, keyword, startTime, endTime);

        log.info("查询项目列表成功，查询到 {} 条数据", projects.size());

        return projects;
    }

    /**
     * 查询项目总数
     *
     * @param projectType 项目类型(可选)
     * @param keyword     搜索关键词(可选)
     * @param startTime   开始时间(可选)
     * @param endTime     结束时间(可选)
     * @return 项目总数
     */
    @Override
    public Long countProjects(String projectType, String keyword, Long startTime, Long endTime) {
        log.info("开始查询项目总数, projectType: {}, keyword: {}, startTime: {}, endTime: {}",
                projectType, keyword, startTime, endTime);
        // 调用DAO层查询总数
        Long count = projectDao.countProjects(projectType, keyword, startTime, endTime);
        log.info("查询项目总数完成，共 {} 个项目", count);
        return count;
    }

    /**
     * 复制项目
     *
     * @param projectId 原项目ID
     * @return 复制后的新项目
     */
    @Override
    public Project copyProject(Long projectId) {
        log.info("开始复制项目, 原项目ID: {}", projectId);

        // 获取原项目信息
        Project originalProject = projectDao.findById(projectId);
        if (originalProject == null || originalProject.getIsDeleted() == 1) {
            log.error("项目复制失败：找不到原项目, 项目ID: {}", projectId);
            throw new ErrorMsg(-1, "项目复制失败：找不到原项目.");
        }

        // 生成副本名称（添加"副本"后缀）
        String copyName = originalProject.getProjectName() + "副本";

        // 检查副本名称是否已存在，如果存在则添加序号
        int copyIndex = 1;
        String finalCopyName = copyName;
        Project existingProject = projectDao.findByProjectName(finalCopyName);

        while (existingProject != null && existingProject.getIsDeleted() == 0) {
            copyIndex++;
            finalCopyName = copyName + copyIndex;
            existingProject = projectDao.findByProjectName(finalCopyName);
        }

        // 创建新的项目对象
        Project newProject = Project.builder()
                .projectName(finalCopyName)
                .projectType(originalProject.getProjectType())
                .createdAt(System.currentTimeMillis())
                .updatedAt(System.currentTimeMillis())
                .isDeleted(0)
                .build();

        // 保存新项目
        int result = projectDao.createProject(newProject);
        if (result <= 0) {
            log.error("项目复制失败: {}", newProject);
            throw new ErrorMsg(-1, "数据库中项目复制失败.");
        }

        List<Simulation> simulations = simulationDao.getSimulations(0, 100, "created_at", "asc", projectId, "", null, null);


        for (Simulation simulation : simulations) {
            Simulation newSimulation = Simulation.builder()
                    .simulationName(simulation.getSimulationName())
                    .simulationModel(simulation.getSimulationModel())
                    .simulationDraft(simulation.getSimulationDraft())
                    .inputData(simulation.getInputData())
                    .outputData(simulation.getOutputData())
                    .measuredData(simulation.getMeasuredData())
                    .simulationScript(simulation.getSimulationScript())
                    .simulationDraft(simulation.getSimulationDraft())
                    .nodes(simulation.getNodes())
                    .controlStrategy(simulation.getControlStrategy())
                    .runStatus(RunStatus.READY)
                    .isDeleted(simulation.getIsDeleted())
                    .projectId(newProject.getProjectId())
                    .projectName(newProject.getProjectName())
                    .createdAt(System.currentTimeMillis())
                    .updatedAt(System.currentTimeMillis())
                    .build();

            int simulationResult = simulationDao.createSimulation(newSimulation);
            if (simulationResult <= 0) {
                log.error("仿真任务复制失败: {}", newSimulation);
            } else {
                log.info("仿真任务复制成功: {}", newSimulation);
                copySimulationRelatedData(simulation, newSimulation);
            }
        }

        log.info("项目复制成功: 原项目ID: {}, 新项目ID: {}", projectId, newProject.getProjectId());

        return newProject;
    }

    /**
     * 复制仿真任务相关数据
     *
     * @param sourceSimulation 源仿真任务
     * @param targetSimulation 目标仿真任务
     */
    private void copySimulationRelatedData(Simulation sourceSimulation, Simulation targetSimulation) {
        if (sourceSimulation == null || targetSimulation == null) {
            log.error("复制仿真任务数据失败：源仿真任务或目标仿真任务为空");
            throw new ErrorMsg(-1, "复制仿真任务数据失败：源仿真任务或目标仿真任务为空");
        }

        try {
            // 构建源文件夹路径
            String sourceFolder = FileUtils.getCurrentPath() + "/data/" +
                    sourceSimulation.getProjectId() + "/" +
                    sourceSimulation.getSimulationId();

            // 构建目标文件夹路径
            String targetFolder = FileUtils.getCurrentPath() + "/data/" +
                    targetSimulation.getProjectId() + "/" +
                    targetSimulation.getSimulationId();

            // 确保目标文件夹存在，如果不存在则创建
            File targetFolderFile = new File(targetFolder);
            if (!targetFolderFile.exists()) {
                boolean created = targetFolderFile.mkdirs();
                if (!created) {
                    log.error("创建目标仿真任务文件夹失败: {}", targetFolder);
                    throw new ErrorMsg(-1, "创建目标仿真任务文件夹失败");
                }
            }

            // 检查源文件夹是否存在
            File sourceFolderFile = new File(sourceFolder);
            if (!sourceFolderFile.exists()) {
                log.warn("源仿真任务文件夹不存在，无需复制: {}", sourceFolder);
                return ;
            }

            // 复制文件夹下的所有内容
            FileUtils.copyDirectory(sourceFolderFile, targetFolderFile);

            log.info("成功复制仿真任务数据，从 {} 到 {}", sourceFolder, targetFolder);
        } catch (IOException e) {
            log.error("复制仿真任务文件夹时发生错误", e);
            throw new ErrorMsg(-1, "复制仿真任务数据失败：" + e.getMessage());
        }
    }

    /**
     * 重命名项目
     *
     * @param projectId 项目ID
     * @param newName 新的项目名称
     * @return 更新后的项目信息
     */
    @Override
    public Project renameProject(Long projectId, String newName) {
        log.info("开始重命名项目, 项目ID: {}, 新名称: {}", projectId, newName);

        // 参数校验
        if (newName == null || newName.trim().isEmpty()) {
            log.error("项目重命名失败：新项目名称不能为空");
            throw new ErrorMsg(-1, "项目重命名失败：新项目名称不能为空.");
        }

        String trimmedNewName = newName.trim();


        // 获取原项目信息
        Project project = projectDao.findById(projectId);
        if (project == null || project.getIsDeleted() == 1) {
            log.error("项目重命名失败：找不到项目, 项目ID: {}", projectId);
            throw new ErrorMsg(-1, "项目重命名失败：找不到项目.");
        }

        // 如果新名称与旧名称相同，直接返回
        if (trimmedNewName.equals(project.getProjectName())) {
            log.info("新名称与原名称相同，无需更新: {}", trimmedNewName);
            return project;
        }

        // 检查新名称是否已被其他项目使用
        Project existingProject = projectDao.findByProjectName(trimmedNewName);
        if (existingProject != null && existingProject.getIsDeleted() == 0) {
            log.error("项目重命名失败：项目名称已存在: {}", trimmedNewName);
            throw new ErrorMsg(-1, "项目重命名失败：项目名称已存在.");
        }

        // 更新项目名称
        project.setProjectName(trimmedNewName);
        project.setUpdatedAt(System.currentTimeMillis());

        synchronized (projectLock) {
            // 保存更新
            int result = projectDao.updateProject(project);
            if (result <= 0) {
                log.error("项目重命名失败: {}", project);
                throw new ErrorMsg(-1, "数据库中项目重命名失败.");
            }
        }

        log.info("项目重命名成功: 项目ID: {}, 新名称: {}", projectId, trimmedNewName);

        return project;
    }

    /**
     * 删除项目
     *
     * @param projectId 项目ID
     */
    @Override
    public void deleteProject(Long projectId) {
        log.info("开始删除项目, 项目ID: {}", projectId);


        // 获取原项目信息
        Project project = projectDao.findById(projectId);
        if (project == null || project.getIsDeleted() == 1) {
            log.error("项目删除失败：找不到项目, 项目ID: {}", projectId);
            throw new ErrorMsg(-1, "项目删除失败：找不到项目.");
        }

        // 逻辑删除项目
        project.setIsDeleted(1);
        project.setUpdatedAt(System.currentTimeMillis());

        synchronized (projectLock) {
            // 保存更新
            int result = projectDao.updateProject(project);
            if (result <= 0) {
                log.error("项目删除失败: 项目ID: {}", projectId);
                throw new ErrorMsg(-1, "数据库中项目删除失败.");
            }
        }

        List<Simulation> simulations = simulationDao.getSimulations(0, 100, "created_at", "asc", projectId, "", null, null);

        for (Simulation simulation : simulations) {
            simulation.setIsDeleted(1);
            int simulationResult = simulationDao.updateSimulation(simulation);

            if (simulationResult <= 0) {
                log.error("仿真任务删除失败: {}", simulation);
            } else {
                log.info("仿真任务删除成功: {}", simulation);
            }
        }
        log.info("项目删除成功: 项目ID: {}", projectId);
    }

    /**
     * 导出项目配置文件
     *
     * @param projectId 项目ID
     * @param response  HTTP响应对象，用于写入文件流
     */
    @Override
    public void exportProject(Long projectId, HttpServletResponse response) {
        File tempFile = null;
        try {
            log.info("开始导出项目配置文件，项目ID: {}", projectId);

            // 验证项目是否存在
            Project project = projectDao.findById(projectId);
            if (project == null || project.getIsDeleted() == 1) {
                log.error("项目不存在或已删除: projectId={}", projectId);
                throw new ErrorMsg(-1, "项目不存在或已删除");
            }

            // 生成临时文件
            tempFile = File.createTempFile("project_config_", ".json");
            log.info("临时文件已创建: {}", tempFile.getAbsolutePath());

            // 创建配置数据
            Map<String, Object> config = new HashMap<>();
            config.put("projectId", project.getProjectId());
            config.put("projectName", project.getProjectName());
            config.put("projectType", project.getProjectType());
            config.put("exportTime", System.currentTimeMillis());

            // 这里可以添加更多项目相关配置数据
            // 例如从其他表中查询项目的详细配置信息

            // 将配置转换为JSON
            String jsonContent = convertObjectToJsonString(config);
            log.info("成功生成项目配置JSON内容");

            // 将JSON数据写入文件
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(tempFile))) {
                writer.write(jsonContent);
                log.info("成功将配置数据写入临时文件: {}", tempFile.getAbsolutePath());
            } catch (IOException e) {
                log.error("写入临时文件时发生错误: {}", e.getMessage(), e);
                throw new ErrorMsg(-1, "写入配置文件失败: " + e.getMessage());
            }

            // 进行文件下载
            downloadFile(tempFile.getAbsolutePath(), response);
            log.info("配置文件导出并准备好下载: {}", tempFile.getAbsolutePath());

        } catch (IOException e) {
            log.error("导出配置文件失败: {}", e.getMessage(), e);
            throw new ErrorMsg(-1, "导出配置文件失败: " + e.getMessage());
        } finally {
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                if (tempFile.delete()) {
                    log.info("临时文件已成功删除: {}", tempFile.getAbsolutePath());
                } else {
                    log.warn("临时文件删除失败: {}", tempFile.getAbsolutePath());
                }
            }
        }
    }

    /**
     * 导入项目配置文件
     *
     * @param file 上传的项目配置文件
     * @return 导入成功的项目信息
     */
    @Override
    public Project importProject(MultipartFile file) {
        File tempFile = null;
        Map<String, Object> projectConfig;

        try {
            // 检查上传的文件是否有效
            if (file == null || file.isEmpty()) {
                log.warn("导入失败: 文件无效或为空");
                throw new ErrorMsg(-1, "导入失败: 文件无效或为空");
            }

            log.info("开始处理上传的项目配置文件");

            // 创建临时文件
            tempFile = File.createTempFile("project_import_", ".json");

            // 确保父目录存在
            File parentDir = tempFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                if (!parentDir.mkdirs()) {
                    log.error("导入失败: 无法创建临时文件目录");
                    throw new ErrorMsg(-1, "导入失败: 无法创建临时文件目录");
                }
            }

            // 将上传的文件保存为临时文件
            file.transferTo(tempFile);
            log.info("上传的文件已成功保存为临时文件: {}", tempFile.getAbsolutePath());

            // 解析JSON配置文件
            projectConfig = convertJsonFileToMap(tempFile);
            log.info("项目配置文件解析成功");

        } catch (IOException e) {
            log.error("处理上传文件时发生IO异常: {}", e.getMessage(), e);
            throw new ErrorMsg(-1, "导入失败: 文件处理异常: " + e.getMessage());
        } finally {
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                if (tempFile.delete()) {
                    log.info("临时文件已成功删除: {}", tempFile.getAbsolutePath());
                } else {
                    log.warn("删除临时文件失败: {}", tempFile.getAbsolutePath());
                }
            }
        }

        // 验证项目配置
        if (projectConfig == null) {
            log.error("项目配置验证失败：配置内容为空");
            throw new ErrorMsg(-1, "项目配置验证失败：配置内容为空");
        }

        if (!projectConfig.containsKey("projectName") || projectConfig.get("projectName") == null
                || ((String) projectConfig.get("projectName")).trim().isEmpty()) {
            log.error("项目配置验证失败：缺少项目名称");
            throw new ErrorMsg(-1, "项目配置验证失败：缺少项目名称");
        }

        // 从配置中提取项目信息
        String projectName = (String) projectConfig.get("projectName");
        String projectType = (String) projectConfig.get("projectType");

        // 检查项目名称是否已存在
        Project existingProject = projectDao.findByProjectName(projectName);
        if (existingProject != null && existingProject.getIsDeleted() == 0) {
            log.warn("项目名称已存在: {}，准备尝试重命名", projectName);

            // 生成新的项目名称 (原名称 + 时间戳)
            String timestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            String newProjectName = projectName + "_" + timestamp;

            log.info("项目重命名为: {}", newProjectName);
            projectName = newProjectName;
        }

        // 创建新项目
        long currentTime = System.currentTimeMillis();
        Project project = Project.builder()
                .projectName(projectName.trim())
                .projectType(projectType != null ? projectType.trim() : "")
                .createdAt(currentTime)
                .updatedAt(currentTime)
                .isDeleted(0)
                .build();

        // 确保数据库操作线程安全
        synchronized (projectLock) {
            // 保存项目
            int result = projectDao.createProject(project);
            if (result <= 0) {
                log.error("项目导入失败: 数据库操作失败");
                throw new ErrorMsg(-1, "项目导入失败：数据库操作失败");
            }
        }

        log.info("项目基本信息导入成功，项目ID: {}, 名称: {}", project.getProjectId(), project.getProjectName());

        return project;
    }
}
