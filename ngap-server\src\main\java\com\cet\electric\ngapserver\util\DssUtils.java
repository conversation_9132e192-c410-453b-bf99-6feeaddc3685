package com.cet.electric.ngapserver.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class DssUtils {

    private final RestTemplate restTemplate;

    public DssUtils(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
        // 设置自定义错误处理器，让4xx/5xx错误不抛异常
        this.restTemplate.setErrorHandler(new DssResponseErrorHandler());
        log.info("DssUtils初始化完成，已设置自定义错误处理器");
    }

    /**
     * 调用DSS策略运行接口
     *
     * @param dssFilePath      DSS文件路径
     * @param dssUrl           DSS服务地址
     * @param jsonData         策略文件内容
     * @return 接口返回的结果
     */
    public ResponseEntity<Map> runDssWithStrategy(String dssFilePath, String jsonData, String dssUrl) {
        log.info("开始调用DSS服务，URL: {}, DSS文件路径: {}", dssUrl, dssFilePath);

        // 构建请求URL
        String url = dssUrl + "/run-dss-with-strategy";

        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 构建请求体
        Map<String, Object> payload = new HashMap<>();
        payload.put("dssFilePath", dssFilePath);
        payload.put("json_data", jsonData);

        // 创建请求实体
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(payload, headers);

        // 发送请求并获取响应
        ResponseEntity<Map> response = restTemplate.postForEntity(url, requestEntity, Map.class);

        // 处理响应，确保错误响应包含必要的字段
        response = processResponse(response);

        log.info("DSS服务调用完成，状态码: {}", response.getStatusCode());
        return response;
    }

    /**
     * 处理DSS服务响应，确保错误响应包含必要的字段
     *
     * @param response 原始响应
     * @return 处理后的响应
     */
    private ResponseEntity<Map> processResponse(ResponseEntity<Map> response) {
        Map<String, Object> body = response.getBody();

        // 如果是错误响应且响应体为空，创建默认错误响应体
        if (!response.getStatusCode().is2xxSuccessful() && (body == null || body.isEmpty())) {
            body = new HashMap<>();
            body.put("error", "DSS服务返回错误，状态码: " + response.getStatusCode());
            body.put("output_data", "DSS服务执行失败");

            log.warn("DSS服务返回空的错误响应，已创建默认错误信息");
            return new ResponseEntity<>(body, response.getHeaders(), response.getStatusCode());
        }

        // 如果是错误响应但没有output_data字段，添加该字段
        if (!response.getStatusCode().is2xxSuccessful() && body != null && !body.containsKey("output_data")) {
            // 尝试从error字段获取错误信息作为output_data
            Object errorInfo = body.get("error");
            if (errorInfo != null) {
                body.put("output_data", errorInfo.toString());
            } else {
                body.put("output_data", "DSS服务执行失败，状态码: " + response.getStatusCode());
            }

            log.info("为DSS错误响应添加output_data字段");
        }

        return response;
    }
}
