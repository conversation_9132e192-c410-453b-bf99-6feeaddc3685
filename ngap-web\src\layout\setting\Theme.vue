<template>
  <div class="layout-theme">
    <div
      v-for="item in items"
      class="layout-theme-item"
      :style="{ 'background-color': item.color }"
      :key="item.id"
      :data-testid="'theme_switch_' + item.id"
      @click="evClick(item)"
    >
      <transition name="el-fade-in-linear">
        <i v-show="item.active" class="el-icon-success active-color" />
      </transition>
    </div>
  </div>
</template>

<script>
import theme from "@omega/layout/utils/theme";
import enums from "@/config/enums";

export default {
  name: "Theme",
  data() {
    const activeId = theme.get();
    // 默认亮色
    const items = enums.each("THEME_MAP", (key, [id, label, color]) => {
      return {
        id,
        label,
        color,
        active: activeId === id
      };
    });

    return {
      value: activeId,
      items
    };
  },
  methods: {
    evClick(item) {
      if (item.id === this.value) {
        return;
      }
      theme.set(item.id);
    }
  }
};
</script>
<style lang="scss" scoped>
.layout-theme {
  display: flex;
  justify-content: center;
  & > *:not(:last-child) {
    @include margin_right(J1);
  }
}
.layout-theme-item {
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  width: 30px;
  height: 30px;
  border-radius: 4px;
  border: 4px solid;
  cursor: pointer;
  transition-duration: 0.3s;
  &:hover {
    opacity: 0.8;
  }
  @include border_color(B2);
  @include font_color(ZS);
}
.active-color {
  color: #fff;
}
</style>
