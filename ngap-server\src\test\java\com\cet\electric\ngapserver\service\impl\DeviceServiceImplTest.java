package com.cet.electric.ngapserver.service.impl;

import com.cet.electric.ngapserver.dao.DeviceDao;
import com.cet.electric.ngapserver.dto.ErrorMsg;
import com.cet.electric.ngapserver.entity.Device;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DeviceServiceImplTest {

    @Mock
    private DeviceDao deviceDao;

    @InjectMocks
    private DeviceServiceImpl deviceService;

    private Device testDevice;

    @Before
    public void setUp() {
        testDevice = Device.builder()
                .parameterId(1L)
                .deviceType("LINE")
                .parameterCode("TEST_PARAM_001")
                .version("1.0")
                .build();
    }

    @Test
    public void testCreateDevice_Success() {
        // 准备测试数据 - 不设置版本，由系统自动设置
        Device newDevice = Device.builder()
                .deviceType("LINE")
                .parameterCode("NEW_PARAM_001")
                .build();

        // Mock DAO 行为
        when(deviceDao.findByParameterCode("NEW_PARAM_001")).thenReturn(null);
        when(deviceDao.createDevice(any(Device.class))).thenReturn(1);

        // 执行测试
        Device result = deviceService.createDevice(newDevice);

        // 验证结果
        assertNotNull(result);
        assertEquals("LINE", result.getDeviceType());
        assertEquals("NEW_PARAM_001", result.getParameterCode());
        assertEquals("1.0", result.getVersion()); // 系统自动设置为1.0

        // 验证方法调用
        verify(deviceDao).findByParameterCode("NEW_PARAM_001");
        verify(deviceDao).createDevice(argThat(device -> "1.0".equals(device.getVersion())));
    }

    @Test(expected = ErrorMsg.class)
    public void testCreateDevice_DuplicateParameterCode() {
        // 准备测试数据
        Device newDevice = Device.builder()
                .deviceType("LINE")
                .parameterCode("EXISTING_PARAM")
                .version("1.0")
                .build();

        // Mock DAO 行为 - 参数代码已存在
        when(deviceDao.findByParameterCode("EXISTING_PARAM")).thenReturn(testDevice);

        // 执行测试 - 应该抛出异常
        deviceService.createDevice(newDevice);
    }

    @Test(expected = ErrorMsg.class)
    public void testCreateDevice_NullDeviceType() {
        // 准备测试数据 - 设备类型为空
        Device newDevice = Device.builder()
                .deviceType(null)
                .parameterCode("TEST_PARAM")
                .version("1.0")
                .build();

        // 执行测试 - 应该抛出异常
        deviceService.createDevice(newDevice);
    }

    @Test(expected = ErrorMsg.class)
    public void testCreateDevice_EmptyParameterCode() {
        // 准备测试数据 - 参数代码为空
        Device newDevice = Device.builder()
                .deviceType("LINE")
                .parameterCode("")
                .build();

        // 执行测试 - 应该抛出异常
        deviceService.createDevice(newDevice);
    }

    @Test
    public void testGetDevices_Success() {
        // 准备测试数据
        List<Device> mockDevices = Arrays.asList(testDevice);

        // Mock DAO 行为
        when(deviceDao.getDevices(0, 10, "parameterId", "desc", null))
                .thenReturn(mockDevices);

        // 执行测试
        List<Device> result = deviceService.getDevices(1, 10, "parameterId", "desc", null);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testDevice, result.get(0));

        // 验证方法调用
        verify(deviceDao).getDevices(0, 10, "parameterId", "desc", null);
    }

    @Test
    public void testGetDevices_WithInvalidSortField() {
        // 准备测试数据
        List<Device> mockDevices = Arrays.asList(testDevice);

        // Mock DAO 行为 - 使用默认排序字段
        when(deviceDao.getDevices(0, 10, "parameterId", "desc", null))
                .thenReturn(mockDevices);

        // 执行测试 - 使用无效的排序字段
        List<Device> result = deviceService.getDevices(1, 10, "invalidField", "desc", null);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());

        // 验证使用了默认排序字段
        verify(deviceDao).getDevices(0, 10, "parameterId", "desc", null);
    }

    @Test
    public void testCountDevices_Success() {
        // Mock DAO 行为
        when(deviceDao.countDevices(null)).thenReturn(5L);

        // 执行测试
        Long result = deviceService.countDevices(null);

        // 验证结果
        assertNotNull(result);
        assertEquals(Long.valueOf(5L), result);

        // 验证方法调用
        verify(deviceDao).countDevices(null);
    }

    @Test
    public void testGetDeviceById_Success() {
        // Mock DAO 行为
        when(deviceDao.findById(1L)).thenReturn(testDevice);

        // 执行测试
        Device result = deviceService.getDeviceById(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(testDevice, result);

        // 验证方法调用
        verify(deviceDao).findById(1L);
    }

    @Test(expected = ErrorMsg.class)
    public void testGetDeviceById_NotFound() {
        // Mock DAO 行为 - 设备不存在
        when(deviceDao.findById(999L)).thenReturn(null);

        // 执行测试 - 应该抛出异常
        deviceService.getDeviceById(999L);
    }

    @Test(expected = ErrorMsg.class)
    public void testGetDeviceById_NullId() {
        // 执行测试 - 应该抛出异常
        deviceService.getDeviceById(null);
    }

    @Test
    public void testUpdateDevice_Success() {
        // 准备测试数据
        Device updateDevice = Device.builder()
                .parameterId(1L)
                .deviceType("TRANSFORMER")
                .parameterCode("UPDATED_PARAM")
                .version("2.0")
                .build();

        // Mock DAO 行为
        when(deviceDao.findById(1L)).thenReturn(testDevice);
        when(deviceDao.findByParameterCode("UPDATED_PARAM")).thenReturn(null); // 无冲突
        when(deviceDao.updateDevice(updateDevice)).thenReturn(1);

        // 执行测试
        Device result = deviceService.updateDevice(updateDevice);

        // 验证结果
        assertNotNull(result);
        assertEquals("TRANSFORMER", result.getDeviceType());
        assertEquals("UPDATED_PARAM", result.getParameterCode());
        assertEquals("2.0", result.getVersion());

        // 验证方法调用
        verify(deviceDao).findById(1L);
        verify(deviceDao).findByParameterCode("UPDATED_PARAM");
        verify(deviceDao).updateDevice(updateDevice);
    }

    @Test(expected = ErrorMsg.class)
    public void testUpdateDevice_NotFound() {
        // 准备测试数据
        Device updateDevice = Device.builder()
                .parameterId(999L)
                .deviceType("LINE")
                .parameterCode("TEST_PARAM")
                .version("1.0")
                .build();

        // Mock DAO 行为 - 设备不存在
        when(deviceDao.findById(999L)).thenReturn(null);

        // 执行测试 - 应该抛出异常
        deviceService.updateDevice(updateDevice);
    }

    @Test(expected = ErrorMsg.class)
    public void testUpdateDevice_ParameterCodeConflict() {
        // 准备测试数据
        Device updateDevice = Device.builder()
                .parameterId(1L)
                .deviceType("LINE")
                .parameterCode("CONFLICT_PARAM")
                .version("1.0")
                .build();

        Device conflictDevice = Device.builder()
                .parameterId(2L)
                .deviceType("LINE")
                .parameterCode("CONFLICT_PARAM")
                .version("1.0")
                .build();

        // Mock DAO 行为 - 参数代码冲突
        when(deviceDao.findById(1L)).thenReturn(testDevice);
        when(deviceDao.findByParameterCode("CONFLICT_PARAM")).thenReturn(conflictDevice);

        // 执行测试 - 应该抛出异常
        deviceService.updateDevice(updateDevice);
    }

    @Test
    public void testDeleteDevice_Success() {
        // Mock DAO 行为
        when(deviceDao.findById(1L)).thenReturn(testDevice);
        when(deviceDao.deleteDevice(1L)).thenReturn(1);

        // 执行测试
        boolean result = deviceService.deleteDevice(1L);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        verify(deviceDao).findById(1L);
        verify(deviceDao).deleteDevice(1L);
    }

    @Test(expected = ErrorMsg.class)
    public void testDeleteDevice_NotFound() {
        // Mock DAO 行为 - 设备不存在
        when(deviceDao.findById(999L)).thenReturn(null);

        // 执行测试 - 应该抛出异常
        deviceService.deleteDevice(999L);
    }

    @Test(expected = ErrorMsg.class)
    public void testDeleteDevice_NullId() {
        // 执行测试 - 应该抛出异常
        deviceService.deleteDevice(null);
    }
}
