package com.cet.electric.ngapserver.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 指标实体类
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Metric {
    /**
     * 指标Id
     */
    private String metricId;

    /**
     * 指标名称
     */
    private String metricName;
    /**
     * 子指标
     */
    private List<Metric> children;

    /**
     * 判断是否为叶节点
     * @return 如果是叶节点返回true，否则返回false
     */
    public Boolean getIsMetric() {
        return children == null || children.isEmpty();
    }
}
