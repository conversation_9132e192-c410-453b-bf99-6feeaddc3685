<template>
  <div class="page">
    <div style="height: 50px; display: flex; align-items: center" class="pl10">
      <div class="pointer back" @click="backProjectManage">
        <i class="el-icon-arrow-left fs18 fwb"></i>
        <span style="font-weight: bold" class="fwb" title="返回">
          {{ get(detailData, "projectName") }}
        </span>
      </div>
    </div>
    <el-divider class="mt0 mb20"></el-divider>
    <el-container style="height: calc(100% - 70px)">
      <el-header style="height: 32px">
        <ElInput
          class="fl mr8"
          v-model="ElInput_keyword.value"
          v-bind="ElInput_keyword"
          v-on="ElInput_keyword.event"
        ></ElInput>

        <div class="fr fullheight">
          <el-upload
            class="mr10"
            style="display: inline-block"
            ref="uploadRef"
            :auto-upload="true"
            :action="`/ngap-server/api/simulations/import?projectId=${detailData.projectId}`"
            :headers="{}"
            :show-file-list="false"
            accept=".zip"
            :disabled="uploadDisable"
            :on-success="handleSuccess"
            :on-error="handleError"
            :before-upload="beforeUploadFile"
          >
            <el-button
              size="mini"
              icon="el-icon-upload2"
              :disabled="uploadDisable"
              :loading="uploadLoading"
            >
              导入
            </el-button>
          </el-upload>

          <el-button size="mini" type="primary" @click="newTask">
            新增
          </el-button>
          <el-button size="mini" type="primary" @click="refreshList">
            刷新
          </el-button>
        </div>
      </el-header>
      <el-main style="height: calc(100% - 72px)">
        <el-table
          height="100%"
          tooltip-effect="light"
          :data="tableData"
          style="width: 100%"
          :header-cell-style="headerCellStyle"
          @sort-change="sortChange"
        >
          <template v-for="(item, index) in visibleColumns">
            <el-table-column
              v-if="item.prop === 'simulationName'"
              v-bind="item"
              :key="item.prop + index"
            >
              <template #default="{ row, $index }">
                <div v-if="editIndex === row.simulationId">
                  <el-input
                    ref="inputRef"
                    v-model="editValue"
                    size="mini"
                    @blur="onEditBlur($index)"
                    @keydown.enter.native="onEditBlur($index)"
                    @input="handleInput"
                  />
                </div>
                <div
                  v-else
                  @dblclick="onEditStart($index, row)"
                  class="text-ellipsis"
                >
                  {{ row.simulationName }}
                </div>
              </template>
            </el-table-column>

            <el-table-column
              v-else-if="item.prop === 'runStatus'"
              v-bind="item"
              :key="item.prop + 'runStatus' + index"
            >
              <template #default="{ row, $index }">
                <div :class="row.runStatus">
                  {{ formatState(row.runStatus) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              v-else
              v-bind="item"
              :key="item.prop + 'other' + index"
            />
          </template>

          <el-table-column :label="$T('操作')" width="180" align="left">
            <template slot-scope="{ row }">
              <el-button type="text" size="mini" @click="editTask(row)">
                编辑配置
              </el-button>
              <el-button
                class="mr16 ml16"
                type="text"
                size="mini"
                @click="viewRunResult(row)"
              >
                运行结果
              </el-button>
              <el-dropdown
                placement="bottom"
                trigger="click"
                @command="handleCommand"
              >
                <i class="el-icon-more pointer" />
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    v-if="
                      ['Voltage_qualification_scenario'].includes(
                        detailData.projectType
                      )
                    "
                    :command="{ action: 'uploadMeasured', ...row }"
                  >
                    <span class="edit fs12">上传实测文件</span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    :command="{ action: 'viewReport', ...row }"
                    v-if="detailData?.projectType !== 'General_scenario'"
                  >
                    <span class="edit fs12">电压合格率报表</span>
                  </el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'copy', ...row }">
                    <span class="edit fs12">复制</span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    :disabled="downLoadDisable"
                    :loading="downLoadLoading"
                    :command="{ action: 'download', ...row }"
                  >
                    <span class="edit fs12">导出</span>
                  </el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'delete', ...row }">
                    <span class="delete fs12">删除</span>
                  </el-dropdown-item>

                  <!-- <el-dropdown-item>
                    <el-button type="text" size="mini" @click="uploadFile(row)">
                      上传
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="text" size="mini" @click="copyTask(row)">
                      复制
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button
                      type="text"
                      size="mini"
                      class="delete"
                      @click="deleteTask(row)"
                    >
                      删除
                    </el-button>
                  </el-dropdown-item> -->
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer height="40px" class="p0">
        <el-pagination
          class="fr"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          :page-sizes="pageSizes"
          :page-size.sync="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </el-footer>
    </el-container>
    <!-- 导入 -->
    <Export v-bind="Export" v-on="Export.event" />
    <EditTaskDrawDialog
      v-bind="EditTaskDrawDialog"
      v-on="EditTaskDrawDialog.event"
    ></EditTaskDrawDialog>
    <ViewTaskResultDialog
      v-bind="ViewTaskResultDialog"
      v-on="ViewTaskResultDialog.event"
    ></ViewTaskResultDialog>
    <NewSimulationTaskDialog
      v-bind="NewSimulationTaskDialog"
      v-on="NewSimulationTaskDialog.event"
    ></NewSimulationTaskDialog>
    <viewPassRateReport
      v-bind="viewPassRateReport"
      v-on="viewPassRateReport.event"
    ></viewPassRateReport>
  </div>
</template>

<script>
import common from "@/utils/common";
import customApi from "@/api/custom";
import Export from "./components/Export";
import EditTaskDrawDialog from "./components/EditTaskDrawDialog";
import ViewTaskResultDialog from "./components/ViewTaskResultDialog";
import NewSimulationTaskDialog from "./components/NewSimulationTaskDialog";
import viewPassRateReport from "./components/viewPassRateReport";
export default {
  name: "simulationTask",
  components: {
    Export,
    EditTaskDrawDialog,
    ViewTaskResultDialog,
    NewSimulationTaskDialog,
    viewPassRateReport
  },
  props: {
    detailData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    visibleColumns() {
      return this.detailData?.projectType == "General_scenario"
        ? this.tableColumns.filter(col => col.prop !== "measuredData")
        : this.tableColumns;
    }
  },
  data() {
    return {
      downLoadLoading: false,
      downLoadDisable: false,
      uploadLoading: false,
      uploadDisable: false,
      editIndex: null,
      editValue: "",
      tableData: [],
      currentPage: 1,
      pageSizes: [10, 20, 50, 100],
      pageSize: 50,
      total: 0,
      tableColumns: [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 80,
          label: $T("序号"),
          sortable: false,
          headerAlign: "left",
          //   index: this.indexMethod,
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          prop: "simulationName",
          minWidth: 190,
          width: "",
          label: $T("仿真名称"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.getColumnValue
        },
        {
          prop: "measuredData",
          minWidth: 190,
          width: "",
          label: $T("实测文件名称"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.getColumnValue
        },
        {
          prop: "controlStrategy",
          minWidth: 190,
          width: "",
          label: $T("策略名称"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.getColumnValue
        },
        {
          prop: "runStatus",
          minWidth: 120,
          width: "",
          label: $T("运行状态"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.getColumnValue
        },
        {
          prop: "updatedAt",
          width: "210",
          label: $T("修改时间"),
          sortable: "custom",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: (row, column, cellValue) =>
            common.formatDate(cellValue, "YYYY-MM-DD HH:mm:ss")
        },
        {
          prop: "createdAt",
          width: "210",
          label: $T("创建时间"),
          sortable: "custom",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: (row, column, cellValue) =>
            common.formatDate(cellValue, "YYYY-MM-DD HH:mm:ss")
        }
      ],

      // 关键字检索
      ElInput_keyword: {
        value: "",
        placeholder: "请输入关键字检索",
        prefixIcon: "el-icon-search",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElInput_keyword_change_out
        }
      },
      //   导出
      Export: {
        openTrigger_in: new Date().getTime(),
        rowInfo: null,
        uploadUrl: "", //请求路径
        accept: "", //文件格式限制
        tip: "", //提示语
        fileSize: 10, //文件大小限制,单位M
        event: {
          importSuccess: this.querySimulationTaskList
        }
      },
      // EditTaskDrawDialog弹窗页面组件
      EditTaskDrawDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        event: {
          saveData_out: this.EditTaskDrawDialog_saveData_out
        }
      },
      // ViewTaskResultDialog弹窗页面组件
      ViewTaskResultDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        event: {}
      },
      // NewSimulationTaskDialog弹窗页面组件
      NewSimulationTaskDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        event: {
          saveData_out: this.querySimulationTaskList
        }
      },
      // viewPassRateReport弹窗页面组件
      viewPassRateReport: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        event: {}
      }
    };
  },
  watch: {},
  methods: {
    get(...arg) {
      return common.get(...arg);
    },
    headerCellStyle({ column, rowIndex }) {
      return {
        background: "#f5f7fa"
      };
    },
    // 返回项目列表
    backProjectManage() {
      this.$emit("back");
    },
    //关键字搜索
    ElInput_keyword_change_out(val) {
      this.currentPage = 1;
      this.querySimulationTaskList();
    },
    // 运行状态
    formatState(val) {
      if (val === "RUNNING") {
        return "运行中";
      } else if (val === "SUCCESS") {
        return "成功";
      } else if (val === "FAILED") {
        return "失败";
      } else if (val === "READY") {
        return "未运行";
      } else {
        return "--";
      }
    },
    // 获取任务列表
    querySimulationTaskList(sortBy = "created_at", sortOrder = "desc") {
      let params = {
        keyword: this.ElInput_keyword.value,
        page: this.currentPage,
        projectId: this.detailData.projectId,
        size: this.pageSize,
        sortBy,
        sortOrder
      };
      customApi["querySimulationTaskList"](params).then(res => {
        this.tableData = common.get(res, "data", []);
        this.total = common.get(res, "total", 0);
      });
    },
    // 每页条数改变时触发
    handleSizeChange(val) {
      this.currentPage = 1;
      this.querySimulationTaskList();
    },
    // 当前页改变时触发
    handleCurrentChange(val) {
      this.querySimulationTaskList();
    },
    // 表格排序
    sortChange(val) {
      console.log(val, "sortChange");
      let sortOrder = val.order.includes("desc") ? "desc" : "asc";
      if (val.prop == "updatedAt") {
        this.querySimulationTaskList("updated_at", sortOrder);
      } else if (val.prop == "createdAt") {
        this.querySimulationTaskList("created_at", sortOrder);
      }
    },
    // 编辑输入框
    onEditStart(index, row) {
      this.editIndex = row.simulationId;
      this.editValue = row.simulationName;
      this.$nextTick(() => {
        this.$refs["inputRef"][0]?.focus();
      });
    },
    onEditBlur(index) {
      if (this.tableData[index].simulationName !== this.editValue.trim()) {
        customApi["putSimulationTaskRename"]({
          simulationId: this.editIndex,
          taskName: this.editValue.trim()
        }).then(res => {
          if (res && res.code == 0) {
            this.$message.success($T("保存成功"));
          }
        });
      }
      if (this.editValue.trim()) {
        this.tableData[index].simulationName = this.editValue.trim();
      }
      this.editIndex = null;
    },
    // 自定义输入处理函数
    handleInput(val) {
      // 允许的字符为中英文、数字、空格，可以根据需要调整
      const cleanVal = val.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, "");
      if (cleanVal !== val) {
        this.editValue = cleanVal;
      }
    },
    // 导入
    uploadTask() {},
    handleSuccess(res, file, fileList) {
      this.uploadDisable = false;
      this.uploadLoading = false;

      if (res.code === 0) {
        this.$message.success($T("导入成功"));
        this.currentPage = 1;
        this.querySimulationTaskList();
      } else {
        this.$message.warning(res.msg);
        this.$refs.uploadRef.clearFiles();
      }
    },
    beforeUploadFile() {
      this.uploadDisable = true;
      this.uploadLoading = true;
    },
    handleError(err, file, fileList) {
      this.uploadDisable = false;
      this.uploadLoading = false;

      this.$message.error(_.get(JSON.parse(err.message), "message"));
      this.$refs.uploadRef.clearFiles();
    },
    // 导出
    downloadTask(val) {
      this.downLoadDisable = true;
      this.downLoadLoading = true;

      customApi["simulationsExport"](val?.simulationId, true)
        .then(common.downloadFileWithName)
        .finally(() => {
          this.downLoadDisable = false;
          this.downLoadLoading = false;
        });
    },
    // 新增
    newTask() {
      this.NewSimulationTaskDialog.inputData_in = this.detailData;
      this.NewSimulationTaskDialog.openTrigger_in = Date.now();
    },
    // 刷新表格
    refreshList() {
      this.currentPage = 1;
      this.querySimulationTaskList();
    },
    // 查看运行结果
    viewRunResult(row) {
      this.ViewTaskResultDialog.inputData_in = _.cloneDeep({
        ...row,
        projectType: this.detailData.projectType
      });
      this.ViewTaskResultDialog.openTrigger_in = Date.now();
      // customApi["runNextStep"](row?.simulationId).then(res => {
      //   if (res && res.code === 0) {
      //     this.ViewTaskResultDialog.inputData_in = _.cloneDeep({
      //       ...row,
      //       projectType: this.detailData.projectType
      //     });
      //     this.ViewTaskResultDialog.openTrigger_in = Date.now();
      //   }
      // });
    },
    // 编辑任务
    editTask(row) {
      this.EditTaskDrawDialog.inputData_in = _.cloneDeep({
        ...row,
        projectType: this.detailData.projectType
      });
      this.EditTaskDrawDialog.openTrigger_in = Date.now();
    },
    // 下拉选择
    handleCommand(val) {
      console.log(val, "row");

      if (val.action.includes("upload")) {
        this.uploadFile(val);
      } else if (val.action == "viewReport") {
        this.viewReport(val);
      } else if (val.action == "copy") {
        this.copyTask(val);
      } else if (val.action == "download") {
        this.downloadTask(val);
      } else {
        this.deleteTask(val);
      }
    },
    // 上传.csv文件
    uploadFile(row) {
      if (row.action == "uploadMeasured") {
        this.Export.accept = ".xlsx,.xls";
        this.Export.tip =
          "只能上传一个Excel文件，表头和文件名不能有特殊字符，且不超过10M";
        this.Export.uploadUrl = `/ngap-server/api/simulations/${row.simulationId}/upload?type=measured_data`;
      }
      this.Export.fileSize = 10;
      this.Export.rowInfo = row;
      this.Export.openTrigger_in = Date.now();
    },
    viewReport(row) {
      if (_.isEmpty(row.measuredData))
        return this.$message.warning($T("请先上传实测文件"));
      this.viewPassRateReport.inputData_in = _.cloneDeep(row);
      this.viewPassRateReport.openTrigger_in = Date.now();
    },
    // 复制任务
    copyTask(row) {
      customApi["copySimulationTask"](row?.simulationId).then(res => {
        if (res && res.code == 0) {
          this.$message.success($T("复制成功"));
          this.currentPage = 1;
          this.querySimulationTaskList();
        }
      });
    },
    // 删除任务
    deleteTask(row) {
      this.$confirm("确定要删除所选任务吗？", "删除任务确认", {
        type: "warning",
        distinguishCancelAndClose: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消"
      })
        .then(() => {
          customApi["deleteSimulationTask"](row?.simulationId).then(res => {
            if (res && res.code == 0) {
              this.$message.success($T("删除成功"));
              this.currentPage = 1;
              this.querySimulationTaskList();
            }
          });
        })
        .catch(() => {});
    },
    // 修改任务
    EditTaskDrawDialog_saveData_out(val) {}
  },
  mounted() {
    this.currentPage = 1;
    this.querySimulationTaskList();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  @include background_color(BG1);
}
.back:hover {
  @include font_color(Sta4);
}
.fwb {
  font-weight: bold;
}
.el-dropdown {
  i:hover {
    @include font_color(ZS);
  }
}
::v-deep .delete.el-button--text {
  @include font_color(Sta3, !important);
}
.edit {
  @include font_color(ZS, !important);
}

.delete {
  @include font_color(Sta3, !important);
}

.SUCCESS {
  @include font_color(Sta1, !important);
}
.FAILED {
  @include font_color(Sta3, !important);
}
.RUNNING {
  @include font_color(Sta2, !important);
}
</style>
