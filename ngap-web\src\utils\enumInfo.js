import _ from "lodash";
// let allEnum = JSON.parse(sessionStorage.getItem("enumerations") || "{}");

/**
 * 
 * @param {枚举名称} name 
 * @param {要排除的选项，默认为空} excludes 
 * @param {根据某一个属性过滤选项，默认为id} key 
 * 
 * 例子：
  原始枚举值 [
    {"id":1,"text":"未处理","propertyLabel":"unconfirm"},
    {"id":2,"text":"处理中","propertyLabel":"confirming"},
    {"id":3,"text":"已处理","propertyLabel":"confirmed"}
  ]

  要过滤 “处理中” 这个选项
 方式一： common.getEnumByName("confirmeventstatus", [2]) 
  输出结果  [
    {"id":1,"text":"未处理","propertyLabel":"unconfirm"},
    {"id":3,"text":"已处理","propertyLabel":"confirmed"}
  ]

  方式二：
  common.getEnumByName("confirmeventstatus", ["confirming"],"propertyLabel") 
  输出结果  [
    {"id":1,"text":"未处理","propertyLabel":"unconfirm"},
    {"id":3,"text":"已处理","propertyLabel":"confirmed"}
  ]
 * 
 */

export function getEnumByName(name, excludes = [], key = "id") {
  let enumerations = JSON.parse(sessionStorage.getItem("enumerations") || "{}");

  let enums = _.get(enumerations, name, []);
  if (_.isEmpty(excludes)) return enums;
  return enums.filter(item => !excludes.includes(item[key]));
}

export function getEnumTextById(modelLabel, id, key = "id") {
  let enumerations = JSON.parse(sessionStorage.getItem("enumerations") || "{}");
  let enums = _.get(enumerations, modelLabel, []);
  return _.get(
    enums.find(item => item[key] === id),
    "text",
    "--"
  );
}
