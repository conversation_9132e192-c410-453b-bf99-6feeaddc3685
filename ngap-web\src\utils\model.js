import _ from "lodash";

class BaseModel {
  constructor(data) {
    this._data = data;
  }

  /**
   * 获取数据-如果数据是对象类型默认进行深拷贝处理后返回
   *
   * @param {String} path "navmenu.xxx.val"
   * @param {Boolean} isReadOnly 只是进行只读操作,不会对数据进行深拷贝
   */
  get(path, isReadOnly) {
    const value = _.get(this._data, path);

    if (isReadOnly || !_.isObject(value)) {
      return value;
    }

    return _.cloneDeep(value);
  }

  /**
   * 设置数据-默认是
   *
   * @param {String} path "navmenu.xxx.val"
   * @param {Any} value 待设置的value值
   * @param {Boolean} isCover 是否直接赋值覆盖
   */
  set(path, value) {
    _.set(this._data, path, value);
  }

  /**
   * 根据参数个数的不同进行不同的操作处理
   */
  conf(...argv) {
    if (!argv.length) {
      return this;
    }
    if (argv.length === 1) {
      return this.get(...argv);
    }
    if (argv.length === 2) {
      return this.set(...argv);
    }
    if (argv.length > 2) {
      throw new Error(`
        arguments can't > 2。
      `);
    }
  }
}

export class DataModel extends BaseModel {
  constructor(data) {
    super(data);
    return this.conf.bind(this);
  }
}
