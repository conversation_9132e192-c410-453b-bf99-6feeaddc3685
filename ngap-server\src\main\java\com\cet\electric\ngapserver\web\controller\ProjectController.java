package com.cet.electric.ngapserver.web.controller;


import com.cet.electric.ngapserver.dto.ProjectQueryDTO;
import com.cet.electric.ngapserver.dto.*;
import com.cet.electric.ngapserver.entity.Project;
import com.cet.electric.ngapserver.service.ProjectService;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@RestController
@Api(value = "ProjectController API", tags = "Project")
@RequestMapping("/ngap-server/api/projects")
public class ProjectController {

    private static final Logger log = LoggerFactory.getLogger(ProjectController.class);

    @Autowired
    private ProjectService projectService;
    @PostMapping
    @ApiOperation(value = "创建项目")
    public ResponseDTO<Project> createProject(
            @RequestParam(value = "projectName") String projectName,
            @RequestParam(value = "projectType") String projectType) {

        log.info("接收到创建项目请求: projectName={}, projectType={}", projectName, projectType);

        // 创建项目实体
        Project project = Project.builder()
                .projectName(projectName.trim())
                .projectType(projectType != null ? projectType.trim() : "")
                .createdAt(System.currentTimeMillis())
                .updatedAt(System.currentTimeMillis())
                .isDeleted(0)
                .build();

        Project result = projectService.createProject(project);

        log.info("项目创建成功，ID: {}", result.getProjectId());
        return new ResponseDTO<>(0, "项目创建成功", result);
    }

    @GetMapping("/scenarios")
    @ApiOperation(value = "获取项目场景列表")
    public ResponseDTO<Map<String, String>> getProjectScenarios() {

        log.info("接收到获取项目场景列表请求");

        // 调用服务层获取项目场景
        Map<String, String> scenarios = projectService.getProjectScenarios();

        log.info("项目场景列表获取成功，总数量: {}", scenarios.size());
        return new ResponseDTO<>(0, "获取成功", scenarios, (long) scenarios.size());
    }

    @PostMapping("/query")
    @ApiOperation(value = "分页查询项目列表")
    public ResponseDTO<List<Project>> getProjects(@RequestBody ProjectQueryDTO queryDTO) {
        log.info("接收到分页查询项目请求: {}", queryDTO);
        // 从DTO中提取参数
        Integer page = queryDTO.getPage();
        Integer size = queryDTO.getSize();
        String sortBy = queryDTO.getSortBy();
        String sortOrder = queryDTO.getSortOrder();
        String projectType = queryDTO.getProjectType();
        String keyword = queryDTO.getKeyword();
        Long startTime = queryDTO.getStartTime();
        Long endTime = queryDTO.getEndTime();
        List<Project> result = projectService.getProjects(page, size, sortBy, sortOrder, projectType, keyword, startTime, endTime);
        Long total = projectService.countProjects(projectType, keyword, startTime, endTime);
        log.info("项目查询成功，总数量: {}", total);
        return new ResponseDTO<>(0, "查询成功", result, total);
    }

    @PostMapping("/{projectId}/copy")
    @ApiOperation(value = "复制项目")
    public ResponseDTO<Project> copyProject(@PathVariable("projectId") Long projectId) {

        log.info("接收到复制项目请求: projectId={}", projectId);

        // 调用服务层复制项目
        Project copiedProject = projectService.copyProject(projectId);

        log.info("项目复制成功，原项目ID: {}, 新项目ID: {}", projectId, copiedProject.getProjectId());
        return new ResponseDTO<>(0, "项目复制成功", copiedProject);
    }

    @PutMapping("/{projectId}/rename")
    @ApiOperation(value = "重命名项目")
    public ResponseDTO<Project> renameProject(
            @PathVariable("projectId") Long projectId,
            @RequestParam("newName") String newName) {

        log.info("接收到重命名项目请求: projectId={}, newName={}", projectId, newName);

        // 调用服务层重命名项目
        Project renamedProject = projectService.renameProject(projectId, newName);

        log.info("项目重命名成功，项目ID: {}, 新名称: {}", projectId, renamedProject.getProjectName());
        return new ResponseDTO<>(0, "项目重命名成功", renamedProject);
    }

    @DeleteMapping("/{projectId}")
    @ApiOperation(value = "删除项目")
    public ResponseDTO<Void> deleteProject(@PathVariable("projectId") Long projectId) {

        log.info("接收到删除项目请求: projectId={}", projectId);

        // 调用服务层删除项目
        projectService.deleteProject(projectId);

        log.info("项目删除成功，项目ID: {}", projectId);
        return new ResponseDTO<>(0, "项目删除成功", null);
    }

    @GetMapping("/{projectId}/export")
    @ApiOperation(value = "导出项目配置文件")
    public void exportProject(
            @PathVariable("projectId") Long projectId,
            HttpServletResponse response) {

        log.info("接收到导出项目配置文件请求: projectId={}", projectId);
        projectService.exportProject(projectId, response);
    }

    @PostMapping("/import")
    @ApiOperation(value = "导入项目配置文件")
    public ResponseDTO<Project> importProject(
            @RequestParam("file") MultipartFile file) {
        log.info("接收到导入项目配置文件请求，文件名：{}, 文件大小：{}",
                file.getOriginalFilename(), file.getSize());
        // 直接调用服务层处理
        Project result = projectService.importProject(file);
        log.info("项目配置文件导入成功，项目ID：{}", result.getProjectId());
        return new ResponseDTO<>(0, "项目导入成功", result);
    }
}

