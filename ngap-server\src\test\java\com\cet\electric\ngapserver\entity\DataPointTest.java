package com.cet.electric.ngapserver.entity;

import org.junit.Test;

import static org.junit.Assert.*;

public class DataPointTest {

    @Test
    public void testDataPointBuilder() {
        // 准备测试数据
        String timestamp = "2023-01-01 12:00";
        Double value = 220.5;

        // 使用Builder创建DataPoint对象
        DataPoint dataPoint = DataPoint.builder()
                .timestamp(timestamp)
                .value(value)
                .build();

        // 验证结果
        assertNotNull(dataPoint);
        assertEquals(timestamp, dataPoint.getTimestamp());
        assertEquals(value, dataPoint.getValue());
    }

    @Test
    public void testDataPointNoArgsConstructor() {
        // 使用无参构造函数创建DataPoint对象
        DataPoint dataPoint = new DataPoint();

        // 验证结果
        assertNotNull(dataPoint);
        assertNull(dataPoint.getTimestamp());
        assertNull(dataPoint.getValue());
    }

    @Test
    public void testDataPointAllArgsConstructor() {
        // 准备测试数据
        String timestamp = "2023-01-01 12:00";
        Double value = 220.5;

        // 使用全参构造函数创建DataPoint对象
        DataPoint dataPoint = new DataPoint(timestamp, value);

        // 验证结果
        assertNotNull(dataPoint);
        assertEquals(timestamp, dataPoint.getTimestamp());
        assertEquals(value, dataPoint.getValue());
    }

    @Test
    public void testDataPointSettersAndGetters() {
        // 创建DataPoint对象
        DataPoint dataPoint = new DataPoint();

        // 准备测试数据
        String timestamp = "2023-01-01 12:00";
        Double value = 220.5;

        // 使用Setter设置值
        dataPoint.setTimestamp(timestamp);
        dataPoint.setValue(value);

        // 使用Getter验证值
        assertEquals(timestamp, dataPoint.getTimestamp());
        assertEquals(value, dataPoint.getValue());
    }

    @Test
    public void testDataPointEqualsAndHashCode() {
        // 创建两个相同的DataPoint对象
        DataPoint dataPoint1 = DataPoint.builder()
                .timestamp("2023-01-01 12:00")
                .value(220.5)
                .build();

        DataPoint dataPoint2 = DataPoint.builder()
                .timestamp("2023-01-01 12:00")
                .value(220.5)
                .build();

        // 验证equals方法
        assertEquals(dataPoint1, dataPoint2);
        assertEquals(dataPoint2, dataPoint1);
        assertEquals(dataPoint1, dataPoint1);

        // 验证hashCode方法
        assertEquals(dataPoint1.hashCode(), dataPoint2.hashCode());
    }

    @Test
    public void testDataPointNotEquals() {
        // 创建两个不同的DataPoint对象
        DataPoint dataPoint1 = DataPoint.builder()
                .timestamp("2023-01-01 12:00")
                .value(220.5)
                .build();

        DataPoint dataPoint2 = DataPoint.builder()
                .timestamp("2023-01-01 13:00")
                .value(230.5)
                .build();

        // 验证不相等
        assertNotEquals(dataPoint1, dataPoint2);
        assertNotEquals(dataPoint2, dataPoint1);
        assertNotEquals(dataPoint1, null);
        assertNotEquals(dataPoint1, "not a data point");
    }

    @Test
    public void testDataPointToString() {
        // 创建DataPoint对象
        DataPoint dataPoint = DataPoint.builder()
                .timestamp("2023-01-01 12:00")
                .value(220.5)
                .build();

        // 验证toString方法
        String toString = dataPoint.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("DataPoint"));
        assertTrue(toString.contains("timestamp=2023-01-01 12:00"));
        assertTrue(toString.contains("value=220.5"));
    }

    @Test
    public void testDataPointWithNullValues() {
        // 创建包含null值的DataPoint对象
        DataPoint dataPoint = DataPoint.builder()
                .timestamp(null)
                .value(null)
                .build();

        // 验证null值处理
        assertNotNull(dataPoint);
        assertNull(dataPoint.getTimestamp());
        assertNull(dataPoint.getValue());
    }

    @Test
    public void testDataPointWithEmptyTimestamp() {
        // 创建包含空时间戳的DataPoint对象
        DataPoint dataPoint = DataPoint.builder()
                .timestamp("")
                .value(100.0)
                .build();

        // 验证空时间戳处理
        assertNotNull(dataPoint);
        assertEquals("", dataPoint.getTimestamp());
        assertEquals(Double.valueOf(100.0), dataPoint.getValue());
    }

    @Test
    public void testDataPointFieldModification() {
        // 创建DataPoint对象
        DataPoint dataPoint = DataPoint.builder()
                .timestamp("2023-01-01 12:00")
                .value(220.5)
                .build();

        // 修改字段值
        dataPoint.setTimestamp("2023-01-01 13:00");
        dataPoint.setValue(230.5);

        // 验证修改结果
        assertEquals("2023-01-01 13:00", dataPoint.getTimestamp());
        assertEquals(Double.valueOf(230.5), dataPoint.getValue());
    }

    @Test
    public void testDataPointTimestampFormats() {
        // 测试不同的时间戳格式
        String[] timestampFormats = {
                "2023-01-01 12:00",
                "2023-01-01 12:00:00",
                "2023-01-01T12:00:00",
                "2023-01-01T12:00:00.000Z",
                "1672574400000", // Unix timestamp
                "12:00:00"
        };

        for (String timestamp : timestampFormats) {
            DataPoint dataPoint = DataPoint.builder()
                    .timestamp(timestamp)
                    .value(100.0)
                    .build();

            assertEquals(timestamp, dataPoint.getTimestamp());
            assertEquals(Double.valueOf(100.0), dataPoint.getValue());
        }
    }

    @Test
    public void testDataPointValueTypes() {
        // 测试不同类型的数值
        Double[] values = {
                0.0,
                -100.5,
                100.5,
                Double.MAX_VALUE,
                Double.MIN_VALUE,
                Double.POSITIVE_INFINITY,
                Double.NEGATIVE_INFINITY,
                Double.NaN
        };

        for (Double value : values) {
            DataPoint dataPoint = DataPoint.builder()
                    .timestamp("2023-01-01 12:00")
                    .value(value)
                    .build();

            assertEquals("2023-01-01 12:00", dataPoint.getTimestamp());
            assertEquals(value, dataPoint.getValue());
        }
    }

    @Test
    public void testDataPointCopy() {
        // 创建原始DataPoint对象
        DataPoint original = DataPoint.builder()
                .timestamp("2023-01-01 12:00")
                .value(220.5)
                .build();

        // 创建副本
        DataPoint copy = DataPoint.builder()
                .timestamp(original.getTimestamp())
                .value(original.getValue())
                .build();

        // 验证副本与原始对象相等
        assertEquals(original, copy);
        assertEquals(original.hashCode(), copy.hashCode());
    }

    @Test
    public void testDataPointImmutabilityAfterCreation() {
        // 创建DataPoint对象
        String originalTimestamp = "2023-01-01 12:00";
        Double originalValue = 220.5;
        
        DataPoint dataPoint = DataPoint.builder()
                .timestamp(originalTimestamp)
                .value(originalValue)
                .build();

        // 验证初始值
        assertEquals(originalTimestamp, dataPoint.getTimestamp());
        assertEquals(originalValue, dataPoint.getValue());

        // 修改引用的变量不应影响DataPoint对象
        String modifiedTimestamp = "2023-01-01 13:00";
        Double modifiedValue = 230.5;

        // DataPoint对象的值应该保持不变
        assertEquals(originalTimestamp, dataPoint.getTimestamp());
        assertEquals(originalValue, dataPoint.getValue());
    }
}
