<template>
  <div class="page" v-loading="loading">
    <iframe
      ref="iframe"
      width="100%"
      height="100%"
      :src="iframeSrc"
      @load="loaded"
      frameborder="0"
    ></iframe>
  </div>
</template>

<script>
import common from "@/utils/common";

export default {
  name: "VisualizationTool",
  components: {},
  props: {
    inputData_in: {
      type: Object
    }
  },
  computed: {
    systemCfg() {
      return this.$store.state.systemConfig.systemCfg;
    }
  },
  data() {
    return {
      loading: false,
      iframeSrc: ""
    };
  },
  methods: {
    loadIframe() {
      this.loading = true;
      this.iframeSrc = "/presim/index.html";
    },
    loaded() {
      this.loading = false;
    },
    sendGreetingToIframe() {
      if (this.$refs?.iframe?.contentWindow) {
        this.$refs.iframe.contentWindow.postMessage(
          { type: "Repaly", simulationId: this.inputData_in.simulationId },
          window.location.origin
        );
      }
    },
    // 监听iframe传过来的消息
    receiveMessageFromIframe(event) {
      // console.log("receiveMessageFromIframe -> 接收到iframe发来的消息", event);
      if (event.data?.type === "init") {
        //  iframe准备好，发送消息
        this.sendGreetingToIframe();
      } else {
        // console.log("收到 iframe 消息：", event.data);
      }
    }
  },

  activated() {
    // console.log("activated");
    this.loadIframe();
    window.addEventListener("message", this.receiveMessageFromIframe);
  },
  mounted() {
    // console.log("mounted");
    window.addEventListener("message", this.receiveMessageFromIframe);
  },
  deactivated() {
    // console.log("deactivated");
    window.removeEventListener("message", this.receiveMessageFromIframe);
  },
  beforeDestroy() {
    // console.log("destroy");
    window.removeEventListener("message", this.receiveMessageFromIframe);
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
