{
  // Place your V2.x 工作区 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  // "Print to console": {
  // 	"scope": "javascript,typescript",
  // 	"prefix": "log",
  // 	"body": [
  // 		"console.log('$1');",
  // 		"$2"
  // 	],
  // 	"description": "Log output to console"
  // }

  //弹窗页面模板
  //cet-pagedialog的代码片段
  "cet-pagedialog": {
    "prefix": "cet-pagedialog",
    "body": [
      "<template>                                                                             ",
      "  <div>                                                                             ",
      "    <!-- 弹窗组件 -->                                                                             ",
      "    <CetDialog v-bind=\"CetDialog_pagedialog\" v-on=\"CetDialog_pagedialog.event\">                                                                             ",
      "      <template v-slot:footer>                                                                             ",
      "        <span>                                                                             ",
      "          <!-- preserve按钮组件 -->                                                                             ",
      "          <CetButton v-bind=\"CetButton_preserve\" v-on=\"CetButton_preserve.event\"></CetButton>                                                                             ",
      "          <!-- cancel按钮组件 -->                                                                             ",
      "          <CetButton v-bind=\"CetButton_cancel\" v-on=\"CetButton_cancel.event\"></CetButton>                                                                             ",
      "        </span>                                                                             ",
      "      </template>                                                                             ",
      "      <CetForm :data.sync=\"CetForm_pagedialog.data\" v-bind=\"CetForm_pagedialog\" v-on=\"CetForm_pagedialog.event\"> </CetForm>                                                                             ",
      "    </CetDialog>                                                                             ",
      "  </div>                                                                             ",
      "</template>                                                                             ",
      "<script>                                                                             ",
      "import common from \"@/utils/common\";                                                                             ",
      "export default {                                                                             ",
      "  name: \"${1:设置组件唯一识别字段}\",                                                                             ",
      "  components: {},                                                                             ",
      "  computed: {                                                                             ",
      "    token() {                                                                             ",
      "      return this.$$store.state.token;                                                                             ",
      "    }                                                                             ",
      "  },                                                                             ",
      "  props: {                                                                             ",
      "    openTrigger_in: {                                                                             ",
      "      type: Number                                                                             ",
      "    },                                                                             ",
      "    closeTrigger_in: {                                                                             ",
      "      type: Number                                                                             ",
      "    },                                                                             ",
      "    //查询数据的id入参                                                                             ",
      "    queryId_in: {                                                                             ",
      "      type: Number,                                                                             ",
      "      default: -1                                                                             ",
      "    },                                                                             ",
      "    inputData_in: {                                                                             ",
      "      type: Object                                                                             ",
      "    }                                                                             ",
      "  },                                                                             ",
      "  data() {                                                                             ",
      "    return {                                                                             ",
      "      CetDialog_pagedialog: {                                                                             ",
      "        openTrigger_in: new Date().getTime(),                                                                             ",
      "        closeTrigger_in: new Date().getTime(),                                                                             ",
      "        title: \"弹窗表单\",                                                                             ",
      "        event: {                                                                             ",
      "          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,                                                                             ",
      "          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out                                                                             ",
      "        }                                                                             ",
      "      },                                                                             ",
      "      // pagedialog表单组件                                                                             ",
      "      CetForm_pagedialog: {                                                                             ",
      "        dataMode: \"static\", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static                                                                              ",
      "        queryMode: \"trigger\", // 查询按钮触发trigger，或者查询条件变化立即查询diff                                                                             ",
      "        //组件数据绑定设置项                                                                             ",
      "        dataConfig: {                                                                             ",
      "          queryFunc: \"\",                                                                             ",
      "          writeFunc: \"\",                                                                             ",
      "          modelLabel: \"\",                                                                             ",
      "          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2                                                                             ",
      "          modelList: [],                                                                             ",
      "          groups: [] //例子     {   name: \"treenode\",   models: [\"floor\", \"building\", \"sectionarea\"] }                                                                             ",
      "        },                                                                             ",
      "        //组件输入项                                                                             ",
      "        inputData_in: {},                                                                             ",
      "        data: {},                                                                             ",
      "        queryId_in: -1,                                                                             ",
      "        queryTrigger_in: new Date().getTime(),                                                                             ",
      "        saveTrigger_in: new Date().getTime(),                                                                             ",
      "        localSaveTrigger_in: new Date().getTime(),                                                                             ",
      "        resetTrigger_in: new Date().getTime(),                                                        ",
      "        size: \"small\",                                                                             ",
      "        labelWidth: \"80px\",                                                                             ",
      "        rules: {},                                                                             ",
      "        event: {                                                                             ",
      "          currentData_out: this.CetForm_pagedialog_currentData_out,                                                                             ",
      "          saveData_out: this.CetForm_pagedialog_saveData_out,                                                                             ",
      "          finishData_out: this.CetForm_pagedialog_finishData_out,                                                                             ",
      "          finishTrigger_out: this.CetForm_pagedialog_finishTrigger_out,                                                                             ",
      "          msg_out: this.CetForm_pagedialog_msg_out                                                                             ",
      "        }                                                                             ",
      "      },                                                                             ",
      "      // preserve组件                                                                             ",
      "      CetButton_preserve: {                                                                             ",
      "        visible_in: true,                                                                             ",
      "        disable_in: false,                                                                             ",
      "        title: \"确定\",                                                                             ",
      "        type: \"primary\",                                                                             ",
      "        plain: true,                                                                             ",
      "        event: {                                                                             ",
      "          statusTrigger_out: this.CetButton_preserve_statusTrigger_out                                                                             ",
      "        }                                                                             ",
      "      },                                                                             ",
      "      // preserve组件                                                                             ",
      "      CetButton_cancel: {                                                                             ",
      "        visible_in: true,                                                                             ",
      "        disable_in: false,                                                                             ",
      "        title: \"取消\",                                                                             ",
      "        type: \"primary\",                                                                             ",
      "        plain: true,                                                                             ",
      "        event: {                                                                             ",
      "          statusTrigger_out: this.CetButton_cancel_statusTrigger_out                                                                             ",
      "        }                                                                             ",
      "      }                                                                             ",
      "    };                                                                             ",
      "  },                                                                             ",
      "  watch: {                                                                             ",
      "    //弹窗页面默认和弹窗的交互                                                                             ",
      "    openTrigger_in(val) {                                                                             ",
      "      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);                                                                             ",
      "    },                                                                             ",
      "    closeTrigger_in(val) {                                                                             ",
      "      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);                                                                             ",
      "    },                                                                             ",
      "    queryId_in(val) {                                                                             ",
      "      this.CetForm_pagedialog.queryId_in = this._.cloneDeep(val);                                                                             ",
      "    },                                                                             ",
      "    inputData_in(val) {                                                                             ",
      "      this.CetForm_pagedialog.inputData_in = this._.cloneDeep(val);                                                                             ",
      "    }                                                                             ",
      "  },                                                                             ",
      "  methods: {                                                                             ",
      "    CetForm_pagedialog_currentData_out(val) {                                                                             ",
      "      this.$$emit(\"currentData_out\", this._.cloneDeep(val));                                                                             ",
      "    },                                                                             ",
      "    CetForm_pagedialog_saveData_out(val) {                                                                             ",
      "      this.$$emit(\"saveData_out\", this._.cloneDeep(val));                                                                             ",
      "    },                                                                             ",
      "    CetForm_pagedialog_finishData_out(val) {                                                                             ",
      "      this.$$emit(\"finishData_out\", this._.cloneDeep(val));                                                                             ",
      "    },                                                                             ",
      "    CetForm_pagedialog_finishTrigger_out(val) {                                                                             ",
      "      this.$$emit(\"finishTrigger_out\", val);                                                                             ",
      "      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);                                                                             ",
      "    },                                                                             ",
      "    CetForm_pagedialog_msg_out(val) {                                                                             ",
      "      this.$$emit(\"msg_out\", val);                                                                             ",
      "    },                                                                             ",
      "    CetDialog_pagedialog_openTrigger_out(val) {                                                                             ",
      "      this.CetForm_pagedialog.queryTrigger_in = this._.cloneDeep(val);                                                                             ",
      "      this.$$emit(\"openTrigger_out\", val);                                                                             ",
      "    },                                                                             ",
      "    CetDialog_pagedialog_closeTrigger_out(val) {                                                                             ",
      "      this.$$emit(\"closeTrigger_out\", val);                                                                             ",
      "    },                                                                             ",
      "    CetButton_preserve_statusTrigger_out(val) {                                                                             ",
      "      this.CetForm_pagedialog.saveTrigger_in = this._.cloneDeep(val);                                                                             ",
      "    },                                                                             ",
      "    CetButton_cancel_statusTrigger_out(val) {                                                                             ",
      "      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);                                                                             ",
      "    },                                                                             ",
      "    no() {}                                                                             ",
      "  },                                                                             ",
      "  created: function() {}                                                                             ",
      "};                                                                             ",
      "</script>                                                                             ",
      "<style lang=\"scss\" scoped>                                                                             ",
      "</style>                                                                             "
    ],
    "description": ""
  },

  //弹窗页面被引入时模板
  //cet-pagedialog-template的代码片段
  "cet-pagedialog-template": {
    "prefix": "cet-pagedialog-template",
    "body": [
      " <$1",
      "   v-bind=\"$1\"                                              ",
      "   v-on=\"${1:请输入组件唯一识别字符串}.event\"                   ",
      " ></$1>                                                        "
    ],
    "description": ""
  },
  //cet-pagedialog-data的代码片段
  "cet-pagedialog-data": {
    "prefix": "cet-pagedialog-data",
    "body": [
      "// ${1:设置组件唯一识别字段}弹窗页面组件                                          ",
      " $1: {                                                       ",
      "   openTrigger_in: new Date().getTime(),                                   ",
      "   closeTrigger_in: new Date().getTime(),                                   ",
      "   queryId_in: 0,                                   ",
      "   inputData_in: null,                                   ",
      "   event: {                                                              ",
      "      currentData_out:this.$1_currentData_out,                                                ",
      "      saveData_out: this.$1_saveData_out,                                        ",
      "      finishData_out: this.$1_finishData_out,                                        ",
      "      finishTrigger_out: this.$1_finishTrigger_out,                                        ",
      "      openTrigger_out: this.$1_openTrigger_out,                                        ",
      "      closeTrigger_out: this.$1_closeTrigger_out                                        ",
      "   }                                                                           ",
      " },                                                                             "
    ],
    "description": ""
  },
  //cet-pagedialog-method的代码片段
  "cet-pagedialog-method": {
    "prefix": "cet-pagedialog-method",
    "body": [
      "// ${1:设置组件唯一识别字段}弹窗页面输出                           ",
      "    $1_currentData_out(val) {                                  ",
      "    },                                                          ",
      "    $1_saveData_out(val) {                                    ",
      "    },                                                  ",
      "    $1_finishData_out(val) {                               ",
      "    },                                                ",
      "    $1_finishTrigger_out(val) {                               ",
      "    },                                                ",
      "    $1_openTrigger_out(val) {                               ",
      "    },                                                ",
      "    $1_closeTrigger_out(val) {                          ",
      "    },                                                       "
    ],
    "description": ""
  }
}
