import fetch from "@/utils/fetch";

import { flattenParams } from "@/utils/fetch";
//模型版本
let version = "v1";

function processResponse(response) {
  return response;
}

function processRequest(data) {
  return data;
}

export function getWaveData(data, hideNotice) {
  return fetch({
    url: `device-data/api/wave/${version}/data/moment/${data.deviceId}?waveTime=${data.waveTime}`,
    method: "GET",
    headers: { hideNotice },
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
    // timeout: 60000,
    // data
  });
}
function transConditions(data) {
  if (!_.has(data, "rootCondition")) return data;
  let conditions = _.get(data, "rootCondition.filter.expressions", []);
  let id = _.get(data, "rootCondition.treeNode.id");
  let modelLabel = _.get(data, "rootCondition.treeNode.modelLabel");
  let page = _.get(data, "rootCondition.page");
  let result = {};
  conditions.forEach(item => {
    result[item.prop] = item.limit;
  });
  result.page = page;
  result.id = id;
  result.modelLabel = modelLabel;
  return result;
}

// export function deleteModelInstence(data) {
//   return fetch({
//     url: `/model/${modelVersion}/${data.modelLabel}`,
//     method: "DELETE",
//     data: data.idRange
//   });
// }

// export function writeModel(data) {
//   return fetch({
//     url: `/model/${modelVersion}/write/hierachy`,
//     method: "POST",
//     data: processRequest(data)
//   });
// }

// export function queryEnum(data) {
//   return fetch({
//     url: `/model/${modelVersion}/enumerations/${data.rootLabel}`,
//     method: "GET",
//     data
//   });
// }
