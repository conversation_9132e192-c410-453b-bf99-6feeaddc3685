<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cet.electric.ngapserver.dao.ProjectDao">
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.cet.electric.ngapserver.entity.Project">
        <id column="project_id" property="projectId"/>
        <result column="project_name" property="projectName"/>
        <result column="project_type" property="projectType"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        project_id, project_name, project_type, created_at, updated_at, is_deleted
    </sql>

    <!-- 插入新项目 -->
    <insert id="createProject" parameterType="com.cet.electric.ngapserver.entity.Project" useGeneratedKeys="true" keyProperty="projectId">
        INSERT INTO project (
            project_name,
            project_type,
            created_at,
            updated_at,
            is_deleted
        ) VALUES (
                     #{projectName},
                     #{projectType},
                     #{createdAt},
                     #{updatedAt},
                     #{isDeleted}
                 )
    </insert>

    <sql id="Project_Where_Clause">
        WHERE is_deleted = 0
        <if test="projectType != null and projectType != ''">
            AND project_type = #{projectType}
        </if>
        <if test="keyword != null and keyword != ''">
            AND project_name LIKE '%' || #{keyword} || '%'
        </if>
        <if test="startTime != null">
            AND created_at >= #{startTime}
        </if>
        <if test="endTime != null">
            AND created_at &lt; #{endTime}
        </if>
    </sql>

    <select id="getProjects" resultMap="BaseResultMap">
        SELECT project_id, project_name, project_type, created_at, updated_at
        FROM project
        <include refid="Project_Where_Clause"/>
        ORDER BY
        <choose>
            <when test="sortBy == 'project_type'">project_type</when>
            <when test="sortBy == 'updated_at'">updated_at</when>
            <otherwise>created_at</otherwise>
        </choose>
        <choose>
            <when test="sortOrder.equalsIgnoreCase('asc')">ASC</when>
            <otherwise>DESC</otherwise>
        </choose>
        LIMIT #{size} OFFSET #{offset}
    </select>

    <select id="countProjects" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM project
        <include refid="Project_Where_Clause"/>
    </select>

    <select id="findByProjectName" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        project
        WHERE
        project_name = #{projectName}
        AND is_deleted = 0
        LIMIT 1
    </select>

    <select id="findById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        project
        WHERE
        project_id = #{projectId}
        LIMIT 1
    </select>

    <update id="updateProject" parameterType="com.cet.electric.ngapserver.entity.Project">
        UPDATE project
        SET
            project_name = #{projectName},
            updated_at = #{updatedAt},
            is_deleted = #{isDeleted}
        WHERE
            project_id = #{projectId}
          AND is_deleted = 0
    </update>

    <!-- 查询所有标记为已删除的项目 -->
    <select id="findAllDeleted" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        project
        WHERE
        is_deleted = 1
    </select>

    <!-- 删除所有标记为已删除的项目（物理删除） -->
    <delete id="deleteAllMarkedAsDeleted">
        DELETE FROM project
        WHERE is_deleted = 1
    </delete>
</mapper>
