package com.cet.electric.ngapserver.util.voltage;

import java.util.Arrays;

/**
 * 紧凑的电压记录数据结构
 * 使用原始数组替代ArrayList，减少内存占用和提升访问性能
 * 
 * <AUTHOR>
 */
public class CompactVoltageRecord {
    
    public final String userName;
    private final double[] voltageValues;
    private final boolean[] validFlags;
    private final int validCount;
    
    /**
     * 构造函数
     * 
     * @param userName 用户名称
     * @param voltageValues 电压值数组（null值会被标记为无效）
     */
    public CompactVoltageRecord(String userName, java.util.List<Double> voltageValues) {
        this.userName = userName;
        
        if (voltageValues == null || voltageValues.isEmpty()) {
            this.voltageValues = new double[0];
            this.validFlags = new boolean[0];
            this.validCount = 0;
            return;
        }
        
        int size = voltageValues.size();
        this.voltageValues = new double[size];
        this.validFlags = new boolean[size];
        
        int count = 0;
        for (int i = 0; i < size; i++) {
            Double value = voltageValues.get(i);
            if (value != null) {
                this.voltageValues[i] = value;
                this.validFlags[i] = true;
                count++;
            } else {
                this.voltageValues[i] = 0.0; // 默认值
                this.validFlags[i] = false;
            }
        }
        
        this.validCount = count;
    }
    
    /**
     * 直接从数组构造（性能更好）
     */
    public CompactVoltageRecord(String userName, double[] voltageValues, boolean[] validFlags) {
        this.userName = userName;
        this.voltageValues = Arrays.copyOf(voltageValues, voltageValues.length);
        this.validFlags = Arrays.copyOf(validFlags, validFlags.length);
        
        int count = 0;
        for (boolean valid : validFlags) {
            if (valid) count++;
        }
        this.validCount = count;
    }
    
    /**
     * 获取电压值数量
     */
    public int size() {
        return voltageValues.length;
    }
    
    /**
     * 获取有效电压值数量
     */
    public int getValidCount() {
        return validCount;
    }
    
    /**
     * 检查指定索引的电压值是否有效
     */
    public boolean isValid(int index) {
        return index >= 0 && index < validFlags.length && validFlags[index];
    }
    
    /**
     * 获取指定索引的电压值
     */
    public double getValue(int index) {
        if (index < 0 || index >= voltageValues.length) {
            throw new IndexOutOfBoundsException("Index: " + index + ", Size: " + voltageValues.length);
        }
        return voltageValues[index];
    }
    
    /**
     * 获取指定索引的电压值（如果无效则返回null）
     */
    public Double getValueOrNull(int index) {
        if (!isValid(index)) {
            return null;
        }
        return voltageValues[index];
    }
    
    /**
     * 遍历所有有效的电压值
     */
    public void forEachValidValue(VoltageValueConsumer consumer) {
        for (int i = 0; i < voltageValues.length; i++) {
            if (validFlags[i]) {
                consumer.accept(i, voltageValues[i]);
            }
        }
    }
    
    /**
     * 获取所有有效电压值的数组副本
     */
    public double[] getValidValues() {
        double[] result = new double[validCount];
        int index = 0;
        
        for (int i = 0; i < voltageValues.length; i++) {
            if (validFlags[i]) {
                result[index++] = voltageValues[i];
            }
        }
        
        return result;
    }
    
    /**
     * 转换为List格式（兼容性方法）
     */
    public java.util.List<Double> toList() {
        java.util.List<Double> result = new java.util.ArrayList<>(voltageValues.length);
        
        for (int i = 0; i < voltageValues.length; i++) {
            if (validFlags[i]) {
                result.add(voltageValues[i]);
            } else {
                result.add(null);
            }
        }
        
        return result;
    }
    
    /**
     * 计算基本统计信息
     */
    public VoltageStatistics calculateStatistics() {
        if (validCount == 0) {
            return new VoltageStatistics(0, 0, 0, 0, 0.0, 0.0);
        }
        
        int qualified = 0;
        int aboveMax = 0;
        int belowMin = 0;
        double max = Double.MIN_VALUE;
        double min = Double.MAX_VALUE;
        
        for (int i = 0; i < voltageValues.length; i++) {
            if (!validFlags[i]) continue;
            
            double voltage = voltageValues[i];
            
            // 分类统计
            if (voltage >= 198.0 && voltage <= 242.0) {
                qualified++;
            } else if (voltage > 242.0) {
                aboveMax++;
            } else if (voltage < 198.0) {
                belowMin++;
            }
            
            // 更新最值
            if (voltage > max) max = voltage;
            if (voltage < min) min = voltage;
        }
        
        return new VoltageStatistics(validCount, qualified, aboveMax, belowMin, max, min);
    }
    
    /**
     * 获取内存使用估算（字节）
     */
    public long getMemoryUsage() {
        // 对象头 + 字符串引用 + 两个数组引用 + int字段
        long objectOverhead = 32;
        
        // 字符串内存（估算）
        long stringMemory = userName != null ? userName.length() * 2 + 32 : 0;
        
        // double数组内存
        long doubleArrayMemory = voltageValues.length * 8 + 16;
        
        // boolean数组内存
        long booleanArrayMemory = validFlags.length + 16;
        
        return objectOverhead + stringMemory + doubleArrayMemory + booleanArrayMemory;
    }
    
    @Override
    public String toString() {
        return String.format("CompactVoltageRecord{userName='%s', size=%d, validCount=%d, memoryUsage=%d bytes}",
                userName, size(), validCount, getMemoryUsage());
    }
    
    /**
     * 电压值消费者接口
     */
    @FunctionalInterface
    public interface VoltageValueConsumer {
        void accept(int index, double value);
    }
    
    /**
     * 电压统计信息
     */
    public static class VoltageStatistics {
        public final int totalReadings;
        public final int qualifiedReadings;
        public final int aboveMaxReadings;
        public final int belowMinReadings;
        public final double maxVoltage;
        public final double minVoltage;
        
        public VoltageStatistics(int totalReadings, int qualifiedReadings, int aboveMaxReadings,
                               int belowMinReadings, double maxVoltage, double minVoltage) {
            this.totalReadings = totalReadings;
            this.qualifiedReadings = qualifiedReadings;
            this.aboveMaxReadings = aboveMaxReadings;
            this.belowMinReadings = belowMinReadings;
            this.maxVoltage = maxVoltage;
            this.minVoltage = minVoltage;
        }
        
        public double getQualifiedRate() {
            return totalReadings > 0 ? (double) qualifiedReadings / totalReadings * 100 : 0;
        }
        
        public double getAboveMaxRate() {
            return totalReadings > 0 ? (double) aboveMaxReadings / totalReadings * 100 : 0;
        }
        
        public double getBelowMinRate() {
            return totalReadings > 0 ? (double) belowMinReadings / totalReadings * 100 : 0;
        }
        
        @Override
        public String toString() {
            return String.format("VoltageStatistics{total=%d, qualified=%d(%.2f%%), aboveMax=%d(%.2f%%), belowMin=%d(%.2f%%), max=%.2f, min=%.2f}",
                    totalReadings, qualifiedReadings, getQualifiedRate(),
                    aboveMaxReadings, getAboveMaxRate(),
                    belowMinReadings, getBelowMinRate(),
                    maxVoltage, minVoltage);
        }
    }
}
