# 设计文档

## 概述

登录页面是基于Vue 2框架开发的单页面应用组件，使用Element UI组件库提供UI组件，Tailwind CSS进行样式定制，集成Omega框架的认证模块。页面采用响应式设计，支持多种屏幕尺寸，提供安全的用户身份验证功能。

## 架构

### 技术栈
- **前端框架**: Vue 2.7.8
- **UI组件库**: Element UI 2.15.6
- **CSS框架**: Tailwind CSS 3.1.6
- **认证框架**: @omega/auth 1.13.2
- **HTTP客户端**: @omega/http 1.7.0
- **路由管理**: Vue Router 3.5.3
- **状态管理**: Vuex 3.5.1

### 架构模式
采用MVVM架构模式：
- **View**: Vue组件模板，负责用户界面展示
- **ViewModel**: Vue组件实例，处理用户交互和数据绑定
- **Model**: 通过Omega认证模块和HTTP服务与后端API交互

## 组件和接口

### 主要组件结构

```
src/projects/login/
├── index.vue                 # 登录页面主组件
├── components/
│   ├── LoginForm.vue        # 登录表单组件
│   ├── LoginHeader.vue      # 页面头部组件
│   └── LoginFooter.vue      # 页面底部组件
```

### 组件接口设计

#### LoginForm.vue
```javascript
// Props
{
  loading: Boolean,        // 登录加载状态
  errorMessage: String     // 错误消息
}

// Events
{
  'submit': { username, password, rememberMe }, // 表单提交事件
  'clear-error': void                           // 清除错误事件
}

// Methods
{
  validateForm(): Boolean,    // 表单验证
  resetForm(): void,         // 重置表单
  focusUsername(): void      // 聚焦用户名输入框
}
```

#### 主页面组件 (index.vue)
```javascript
// Data
{
  loginForm: {
    username: '',
    password: '',
    rememberMe: false
  },
  loading: false,
  errorMessage: '',
  loginRules: Object
}

// Methods
{
  handleLogin(): Promise<void>,      // 处理登录逻辑
  handleLoginSuccess(): void,        // 登录成功处理
  handleLoginError(error): void,     // 登录失败处理
  validateInput(): Boolean           // 输入验证
}
```

### API接口

#### 认证接口
```javascript
// 登录接口
POST /api/auth/login
Request: {
  username: string,
  password: string,
  rememberMe: boolean
}
Response: {
  success: boolean,
  data: {
    token: string,
    user: {
      id: string,
      username: string,
      name: string,
      roles: string[]
    }
  },
  message: string
}

// 登录状态验证接口
GET /api/auth/verify
Headers: {
  Authorization: 'Bearer <token>'
}
Response: {
  success: boolean,
  data: {
    valid: boolean,
    user: Object
  }
}
```

## 数据模型

### 用户登录表单模型
```javascript
const LoginFormModel = {
  username: {
    type: String,
    required: true,
    minLength: 3,
    maxLength: 50,
    pattern: /^[a-zA-Z0-9_]+$/
  },
  password: {
    type: String,
    required: true,
    minLength: 6,
    maxLength: 128
  },
  rememberMe: {
    type: Boolean,
    default: false
  }
}
```

### 用户信息模型
```javascript
const UserModel = {
  id: String,
  username: String,
  name: String,
  email: String,
  roles: Array<String>,
  lastLoginTime: Date,
  isActive: Boolean
}
```

### 认证状态模型
```javascript
const AuthStateModel = {
  isAuthenticated: Boolean,
  token: String,
  user: UserModel,
  loginTime: Date,
  expiresAt: Date
}
```

## 错误处理

### 客户端验证错误
- **空字段验证**: 用户名或密码为空时显示相应提示
- **格式验证**: 用户名格式不正确时显示格式要求
- **长度验证**: 输入长度超出限制时显示长度要求

### 服务端错误处理
- **401 未授权**: 用户名或密码错误
- **403 禁止访问**: 账户被锁定或禁用
- **429 请求过多**: 登录尝试次数过多
- **500 服务器错误**: 服务器内部错误
- **网络错误**: 网络连接失败

### 错误显示策略
```javascript
const ErrorHandling = {
  // 显示用户友好的错误消息
  displayError(error) {
    const errorMap = {
      'INVALID_CREDENTIALS': '用户名或密码错误',
      'ACCOUNT_LOCKED': '账户已被锁定，请联系管理员',
      'ACCOUNT_DISABLED': '账户已被禁用',
      'TOO_MANY_ATTEMPTS': '登录尝试次数过多，请稍后再试',
      'NETWORK_ERROR': '网络连接失败，请检查网络设置',
      'SERVER_ERROR': '服务器暂时不可用，请稍后再试'
    };
    return errorMap[error.code] || '登录失败，请重试';
  },
  
  // 错误恢复策略
  handleError(error) {
    this.loading = false;
    this.errorMessage = this.displayError(error);
    
    // 自动清除错误消息
    setTimeout(() => {
      this.errorMessage = '';
    }, 5000);
  }
}
```

## 测试策略

### 单元测试
- **组件渲染测试**: 验证组件正确渲染
- **表单验证测试**: 测试各种输入验证场景
- **事件处理测试**: 测试用户交互事件
- **API调用测试**: 模拟API调用和响应

### 集成测试
- **登录流程测试**: 端到端登录流程验证
- **路由跳转测试**: 登录成功后的页面跳转
- **状态管理测试**: Vuex状态更新验证
- **本地存储测试**: 记住登录状态功能

### 用户体验测试
- **响应式测试**: 不同屏幕尺寸下的显示效果
- **无障碍测试**: 键盘导航和屏幕阅读器支持
- **性能测试**: 页面加载和交互响应时间
- **浏览器兼容性测试**: 主流浏览器兼容性验证

### 安全测试
- **输入安全测试**: XSS和注入攻击防护
- **HTTPS传输测试**: 确保敏感数据加密传输
- **会话管理测试**: 登录状态和会话过期处理
- **暴力破解防护测试**: 多次失败登录的限制机制

## UI/UX设计规范

### 视觉设计
- **主色调**: 使用Omega主题色彩方案
- **字体**: 系统默认字体栈，确保跨平台一致性
- **间距**: 遵循8px网格系统
- **圆角**: 统一使用4px圆角

### 交互设计
- **焦点管理**: 页面加载时自动聚焦用户名输入框
- **键盘导航**: 支持Tab键在表单元素间切换
- **回车提交**: 在密码框按回车键直接提交表单
- **加载状态**: 提交时显示加载指示器并禁用表单

### 响应式设计
```css
/* 移动端 (< 768px) */
.login-container {
  @apply px-4 py-8;
}

/* 平板端 (768px - 1024px) */
@media (min-width: 768px) {
  .login-container {
    @apply px-8 py-12;
  }
}

/* 桌面端 (> 1024px) */
@media (min-width: 1024px) {
  .login-container {
    @apply px-12 py-16;
  }
}
```

### 无障碍设计
- **语义化HTML**: 使用适当的HTML标签
- **ARIA标签**: 为交互元素添加ARIA属性
- **颜色对比度**: 确保文本和背景有足够的对比度
- **键盘可访问性**: 所有交互元素都可通过键盘操作