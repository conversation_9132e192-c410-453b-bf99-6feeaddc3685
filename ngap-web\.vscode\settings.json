{"files.autoSave": "off", "editor.formatOnSave": true, "vetur.validation.template": false, "vetur.format.enable": false, "editor.defaultFormatter": "esbenp.prettier-vscode", "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "javascript.preferences.importModuleSpecifierEnding": "js", "[ignore]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "[shellscript]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "[dockerfile]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "[hosts]": {"editor.defaultFormatter": "foxundermoon.shell-format"}}