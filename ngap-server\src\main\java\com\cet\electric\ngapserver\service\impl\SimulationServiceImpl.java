package com.cet.electric.ngapserver.service.impl;

import com.cet.electric.ngapserver.dao.ProjectDao;
import com.cet.electric.ngapserver.dao.SimulationDao;
import com.cet.electric.ngapserver.dto.ErrorMsg;
import com.cet.electric.ngapserver.entity.*;
import com.cet.electric.ngapserver.enums.RunStatus;
import com.cet.electric.ngapserver.service.SimulationService;
import com.cet.electric.ngapserver.util.*;
import com.cet.electric.ngapserver.util.voltage.VoltageStatisticsCalculator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.cet.electric.ngapserver.util.ExcelUtils.generateMetricExcel;
import static com.cet.electric.ngapserver.util.FileUtils.*;
import static com.cet.electric.ngapserver.util.JsonUtils.convertJsonFileToMap;
import static com.cet.electric.ngapserver.util.JsonUtils.convertObjectToJsonString;

/**
 * <AUTHOR>
 */
@Service
public class SimulationServiceImpl implements SimulationService {

    private static final Logger log = LoggerFactory.getLogger(SimulationServiceImpl.class);

    private final Object simulationConfigLock = new Object();

    private static final ExecutorService DSS_TASK_EXECUTOR = Executors.newSingleThreadExecutor();

    private static final ExecutorService REPORT_EXECUTOR = Executors.newSingleThreadExecutor();

    @Value("${dss.url:http://localhost:5000}")
    private String dssUrl;

    @Autowired
    private SimulationDao simulationDao;

    @Autowired
    private DssUtils dssUtils;

    @Autowired
    private ProjectDao projectDao;



    /**
     * 分页查询仿真任务列表
     *
     * @param page      页码
     * @param size      每页大小
     * @param sortBy    排序字段
     * @param sortOrder 排序方向
     * @param projectId 项目ID（必填）
     * @param keyword   搜索关键词(可选)
     * @param startTime 起始时间(时间戳-毫秒)
     * @param endTime   结束时间(时间戳-毫秒)
     * @return 分页结果
     */
    @Override
    public List<Simulation> getSimulations(Integer page, Integer size, String sortBy, String sortOrder,
                                           Long projectId, String keyword, Long startTime, Long endTime) {
        log.info("开始查询仿真任务列表, page: {}, size: {}, sortBy: {}, sortOrder: {}, projectId: {}, keyword: {}, startTime: {}, endTime: {}",
                page, size, sortBy, sortOrder, projectId, keyword, startTime, endTime);
        // 参数校验
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }

        // 项目ID校验
        if (projectId == null || projectId <= 0) {
            log.error("仿真任务查询失败：项目ID不能为空");
            throw new ErrorMsg(-1, "仿真任务查询失败：项目ID不能为空");
        }
        // 验证排序字段
        List<String> validSortFields = Arrays.asList("created_at", "updated_at");
        if (sortBy == null || !validSortFields.contains(sortBy)) {
            log.warn("无效的排序字段: {}，使用默认排序字段 created_at", sortBy);
            sortBy = "created_at";
        }
        // 验证排序方向
        if ((!"asc".equalsIgnoreCase(sortOrder) && !"desc".equalsIgnoreCase(sortOrder))) {
            log.warn("无效的排序方向: {}，使用默认排序方向 desc", sortOrder);
            sortOrder = "desc";
        }

        // 验证时间范围
        if (startTime != null && endTime != null && startTime > endTime) {
            log.warn("起始时间大于结束时间，交换时间范围");
            Long temp = startTime;
            startTime = endTime;
            endTime = temp;
        }

        // 计算分页参数
        int offset = (page - 1) * size;
        // 查询数据列表
        List<Simulation> simulations = simulationDao.getSimulations(offset, size, sortBy, sortOrder, projectId, keyword, startTime, endTime);
        log.info("查询仿真任务列表成功，查询到 {} 条数据", simulations.size());

        for (Simulation simulation : simulations) {
            String controlStrategyLabel = "";
            // 检查simulation对象及其controlStrategy是否为null
            if (simulation != null && simulation.getControlStrategy() != null) {
                String strategy = simulation.getControlStrategy().trim();
                // 检查是否为非空字符串
                if (!strategy.isEmpty()) {
                    if (strategy.contains("REACTIVE_POWER_VOLTAGE")) {
                        controlStrategyLabel += "无功-电压 ";
                    }else if (strategy.contains("ACTIVE_POWER_VOLTAGE_CONTROL")) {
                        controlStrategyLabel += "有功-电压 ";
                    }
                    simulation.setControlStrategy(controlStrategyLabel);
                }
            }
            if (simulation != null && simulation.getOutputData() != null) {
                controlStrategyLabel += simulation.getOutputData();
                simulation.setControlStrategy(controlStrategyLabel);
            }
        }

        return simulations;
    }

    /**
     * 查询仿真任务总数
     *
     * @param projectId 项目ID（必填）
     * @param keyword   搜索关键词(可选)
     * @param startTime   开始时间(可选)
     * @param endTime     结束时间(可选)
     * @return 总数
     */
    @Override
    public Long countSimulations(Long projectId, String keyword, Long startTime, Long endTime) {
        // 参数校验
        if (projectId == null || projectId <= 0) {
            log.error("仿真任务统计失败：项目ID不能为空");
            throw new ErrorMsg(-1, "仿真任务统计失败：项目ID不能为空");
        }

        return simulationDao.countSimulations(projectId, keyword, startTime, endTime);
    }

    /**
     * 创建仿真任务
     *
     * @param simulation 仿真任务实体
     * @return 创建后的仿真任务
     */
    @Override
    public Simulation createSimulation(Simulation simulation) {
        // 检查项目ID是否有效
        if (simulation.getProjectId() == null || simulation.getProjectId() <= 0) {
            log.error("仿真任务创建失败：项目ID不能为空");
            throw new ErrorMsg(-1, "仿真任务创建失败：项目ID不能为空");
        }

        // 检查项目是否存在
        Project project = projectDao.findById(simulation.getProjectId());
        if (project == null || project.getIsDeleted() == 1) {
            log.error("仿真任务创建失败：项目不存在，projectId: {}", simulation.getProjectId());
            throw new ErrorMsg(-1, "仿真任务创建失败：项目不存在");
        }

        simulation.setProjectName(project.getProjectName());

        // 检查仿真名称是否为空
        if (simulation.getSimulationName() == null || simulation.getSimulationName().trim().isEmpty()) {
            log.error("仿真任务创建失败：仿真名称不能为空");
            throw new ErrorMsg(-1, "仿真任务创建失败：仿真名称不能为空");
        }

        // 检查该项目下是否已有同名仿真任务
        String simulationName = simulation.getSimulationName().trim();
        Simulation existingSimulation = simulationDao.findByProjectIdAndName(
                simulation.getProjectId(), simulationName);

        if (existingSimulation != null && existingSimulation.getIsDeleted() == 0) {
            log.error("仿真任务创建失败：该项目下已存在同名仿真任务: {}", simulationName);
            throw new ErrorMsg(-1, "仿真任务创建失败：该项目下已存在同名仿真任务");
        }

        // 设置仿真名称为去除空格后的值
        simulation.setSimulationName(simulationName);
        simulation.setRunStatus(RunStatus.READY);

        synchronized (simulationConfigLock) {
            // 保存仿真任务
            int result = simulationDao.createSimulation(simulation);

            if (result <= 0) {
                log.error("仿真任务创建失败: {}", simulation);
                throw new ErrorMsg(-1, "数据库中仿真任务创建失败");
            }
        }

        log.info("仿真任务创建成功: {}", simulation);

        String fileFolderPath = getCurrentPath() + "/data/" + simulation.getProjectId() + "/" + simulation.getSimulationId();

        // 创建File对象
        File fileFolder = new File(fileFolderPath);
        // 创建文件夹（包括所有必需但不存在的父文件夹）
        if (!fileFolder.exists()) {
            boolean created = fileFolder.mkdirs();
            if (created) {
                log.info("文件夹创建成功: {}", fileFolderPath);
            } else {
                log.error("文件夹创建失败: {}", fileFolderPath);
            }
        } else {
            log.info("文件夹已存在: {}", fileFolderPath);
        }

        // 返回创建的仿真任务
        return simulation;
    }

    /**
     * 重命名仿真任务
     *
     * @param simulationId 仿真任务ID
     * @param newName 新的仿真名称
     * @return 更新后的仿真任务
     */
    @Override
    public Simulation renameSimulation(Long simulationId, String newName) {
        // 检查参数
        if (simulationId == null || simulationId <= 0) {
            log.error("仿真任务重命名失败：仿真ID不能为空");
            throw new ErrorMsg(-1, "仿真任务重命名失败：仿真ID不能为空");
        }

        if (newName == null || newName.trim().isEmpty()) {
            log.error("仿真任务重命名失败：新名称不能为空");
            throw new ErrorMsg(-1, "仿真任务重命名失败：新名称不能为空");
        }

        // 去除空格
        String trimmedNewName = newName.trim();

        // 检查仿真任务是否存在
        Simulation simulation = simulationDao.findById(simulationId);
        if (simulation == null || simulation.getIsDeleted() == 1) {
            log.error("仿真任务重命名失败：仿真任务不存在，simulationId: {}", simulationId);
            throw new ErrorMsg(-1, "仿真任务重命名失败：仿真任务不存在");
        }

        // 检查新名称是否与当前名称相同
        if (trimmedNewName.equals(simulation.getSimulationName())) {
            log.info("仿真任务新名称与原名称相同，无需更新: {}", trimmedNewName);
            return simulation;
        }

        // 检查同一项目下是否已有同名仿真任务
        Simulation existingSimulation = simulationDao.findByProjectIdAndName(
                simulation.getProjectId(), trimmedNewName);

        if (existingSimulation != null && existingSimulation.getIsDeleted() == 0
                && !existingSimulation.getSimulationId().equals(simulationId)) {
            log.error("仿真任务重命名失败：该项目下已存在同名仿真任务: {}", trimmedNewName);
            throw new ErrorMsg(-1, "仿真任务重命名失败：该项目下已存在同名仿真任务");
        }

        // 更新仿真任务名称
        simulation.setSimulationName(trimmedNewName);
        simulation.setUpdatedAt(System.currentTimeMillis());

        synchronized (simulationConfigLock) {
            int result = simulationDao.updateSimulation(simulation);
            if (result <= 0) {
                log.error("仿真任务重命名失败: {}", simulation);
                throw new ErrorMsg(-1, "数据库中仿真任务重命名失败");
            }
        }
        log.info("仿真任务重命名成功: ID={}, 新名称={}", simulationId, trimmedNewName);

        return simulation;
    }

    /**
     * 更新仿真任务
     *
     * @param simulationId     仿真任务ID
     * @param simulationModel  仿真模型
     * @param simulationDraft  仿真模型草稿
     * @param simulationScript 仿真脚本
     * @param nodes            并网点
     * @param controlStrategy  控制策略参数
     * @return 更新后的仿真任务
     */
    @Override
    public Simulation updateSimulation(Long simulationId, String simulationModel, String simulationDraft, String simulationScript, String nodes, String controlStrategy) {
        // 检查参数
        if (simulationId == null || simulationId <= 0) {
            log.error("更新仿真任务失败：仿真ID不能为空");
            throw new ErrorMsg(-1, "更新仿真任务失败：仿真ID不能为空");
        }
        // 检查仿真任务是否存在
        Simulation simulation = simulationDao.findById(simulationId);
        if (simulation == null || simulation.getIsDeleted() == 1) {
            log.error("更新仿真任务失败：仿真任务不存在，simulationId: {}", simulationId);
            throw new ErrorMsg(-1, "更新仿真任务失败：仿真任务不存在");
        }
        // 检查是否有修改
        boolean hasChanges = false;

        if (simulationModel != null && !simulationModel.trim().isEmpty() && !simulationModel.equals(simulation.getSimulationModel())) {
            log.info("仿真任务model更新: {}", simulationId);
            simulation.setSimulationModel(simulationModel);
            hasChanges = true;
        }

        if (simulationDraft != null && !simulationDraft.trim().isEmpty() && !simulationDraft.equals(simulation.getSimulationDraft())) {
            log.info("仿真任务draft更新: {}", simulationId);
            simulation.setSimulationDraft(simulationDraft);
            hasChanges = true;
        }

        if (simulationScript != null && !simulationScript.trim().isEmpty() && !simulationScript.equals(simulation.getSimulationScript())) {
            log.info("仿真任务script更新: {}", simulationId);
            simulation.setSimulationScript(simulationScript);
            updateSimulationScriptFile(simulation);
            hasChanges = true;
        }

        if (nodes != null && !nodes.trim().isEmpty() && !nodes.equals(simulation.getNodes())) {
            log.info("仿真任务nodes更新: {}", simulationId);
            simulation.setNodes(nodes);
            updateNodesFile(simulation);
            hasChanges = true;
        }

        if (controlStrategy != null && !controlStrategy.trim().isEmpty() && !controlStrategy.equals(simulation.getControlStrategy())) {
            log.info("仿真任务controlStrategy更新: {}", simulationId);
            simulation.setControlStrategy(controlStrategy);
            updateControlStrategyFile(simulation);
            hasChanges = true;
        }

        // 如果没有修改，直接返回
        if (!hasChanges) {
            log.info("仿真任务没有变更，无需更新: {}", simulationId);
            return simulation;
        }

        // 更新时间戳
        simulation.setRunStatus(RunStatus.READY);
        simulation.setUpdatedAt(System.currentTimeMillis());
        // 使用同步锁，确保线程安全
        synchronized (simulationConfigLock) {
            int result = simulationDao.updateSimulation(simulation);
            if (result <= 0) {
                log.error("更新仿真任务失败: {}", simulation);
                throw new ErrorMsg(-1, "数据库中更新仿真任务失败");
            }
        }

        log.info("仿真任务更新成功: ID={}", simulationId);
        return simulation;
    }

    /**
     * 更新并网点文件
     *
     * @param simulation 仿真任务对象
     */
    private void updateNodesFile(Simulation simulation) {
        if (simulation == null || simulation.getNodes() == null) {
            log.warn("并网点文件内容为空，不更新并网点文件");
            return;
        }

        String nodesfile = getCurrentPath() + "/data/" +
                simulation.getProjectId() + "/" +
                simulation.getSimulationId() + "/" +
                "simulation_script" + "/" +
                 "nodes.txt";

        log.info("准备更新并网点文件: {}", nodesfile);

        try {
            // 创建文件目录
            File scriptFile = new File(nodesfile);
            File scriptDir = scriptFile.getParentFile();

            // 如果目录不存在，则创建
            if (!scriptDir.exists()) {
                boolean created = scriptDir.mkdirs();
                if (created) {
                    log.info("成功创建并网点目录: {}", scriptDir.getAbsolutePath());
                } else {
                    log.error("无法创建并网点目录: {}", scriptDir.getAbsolutePath());
                    throw new IOException("无法创建并网点目录");
                }
            }

            // 写入脚本内容到文件
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(scriptFile))) {
                writer.write(simulation.getNodes());
                log.info("并网点文件更新成功: {}", nodesfile);
            }

        } catch (IOException e) {
            log.error("更新并网点文件时发生错误: {}", nodesfile, e);
            throw new ErrorMsg(-1, "更新并网点文件失败: " + e.getMessage());
        }
    }

    /**
     * 更新仿真脚本文件
     *
     * @param simulation 仿真任务对象
     */
    private void updateSimulationScriptFile(Simulation simulation) {
        if (simulation == null || simulation.getSimulationScript() == null) {
            log.warn("仿真脚本内容为空，不更新脚本文件");
            return;
        }

        String simulationScript = getCurrentPath() + "/data/" +
                simulation.getProjectId() + "/" +
                simulation.getSimulationId() + "/" +
                "simulation_script" + "/" +
                "simulationScript.dss";

        log.info("准备更新仿真脚本文件: {}", simulationScript);

        try {
            // 创建文件目录
            File scriptFile = new File(simulationScript);
            File scriptDir = scriptFile.getParentFile();

            // 如果目录不存在，则创建
            if (!scriptDir.exists()) {
                boolean created = scriptDir.mkdirs();
                if (created) {
                    log.info("成功创建仿真脚本目录: {}", scriptDir.getAbsolutePath());
                } else {
                    log.error("无法创建仿真脚本目录: {}", scriptDir.getAbsolutePath());
                    throw new IOException("无法创建仿真脚本目录");
                }
            }

            // 写入脚本内容到文件
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(scriptFile))) {
                writer.write(simulation.getSimulationScript());
                log.info("仿真脚本文件更新成功: {}", simulationScript);
            }

        } catch (IOException e) {
            log.error("更新仿真脚本文件时发生错误: {}", simulationScript, e);
            throw new ErrorMsg(-1, "更新仿真脚本文件失败: " + e.getMessage());
        }
    }

    /**
     * 更新控制策略文件
     *
     * @param simulation 仿真任务对象
     */
    private void updateControlStrategyFile(Simulation simulation) {
        if (simulation == null || simulation.getControlStrategy() == null) {
            log.warn("控制策略文件内容为空，不更新控制策略文件");
            return;
        }

        String strategyFile = getCurrentPath() + "/data/" +
                simulation.getProjectId() + "/" +
                simulation.getSimulationId() + "/" +
                "control_strategy" + "/" +
                "data.json";

        log.info("准备更新控制策略文件: {}", strategyFile);

        try {
            // 创建文件目录
            File scriptFile = new File(strategyFile);
            File scriptDir = scriptFile.getParentFile();

            // 如果目录不存在，则创建
            if (!scriptDir.exists()) {
                boolean created = scriptDir.mkdirs();
                if (created) {
                    log.info("成功创建控制策略目录: {}", scriptDir.getAbsolutePath());
                } else {
                    log.error("无法创建控制策略目录: {}", scriptDir.getAbsolutePath());
                    throw new IOException("无法创建控制策略目录");
                }
            }

            // 写入控制策略内容到文件
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(scriptFile))) {
                writer.write(simulation.getControlStrategy());
                log.info("控制策略文件更新成功: {}", strategyFile);
            }

        } catch (IOException e) {
            log.error("更新控制策略文件时发生错误: {}", strategyFile, e);
            throw new ErrorMsg(-1, "更新控制策略文件失败: " + e.getMessage());
        }
    }

    /**
     * 删除仿真任务
     *
     * @param simulationId 仿真任务ID
     */
    @Override
    public void deleteSimulation(Long simulationId) {
        // 检查参数
        if (simulationId == null || simulationId <= 0) {
            log.error("仿真任务删除失败：仿真ID不能为空");
            throw new ErrorMsg(-1, "仿真任务删除失败：仿真ID不能为空");
        }

        // 检查仿真任务是否存在
        Simulation simulation = simulationDao.findById(simulationId);
        if (simulation == null) {
            log.error("仿真任务删除失败：仿真任务不存在，simulationId: {}", simulationId);
            throw new ErrorMsg(-1, "仿真任务删除失败：仿真任务不存在");
        }

        // 如果已经被删除，直接返回成功
        if (simulation.getIsDeleted() == 1) {
            log.info("仿真任务已被删除，无需再次删除: {}", simulationId);
            return;
        }

        // 逻辑删除仿真任务
        simulation.setIsDeleted(1);
        simulation.setUpdatedAt(System.currentTimeMillis());

        synchronized (simulationConfigLock) {
            int result = simulationDao.updateSimulation(simulation);
            if (result <= 0) {
                log.error("仿真任务删除失败: {}", simulation);
                throw new ErrorMsg(-1, "数据库中仿真任务删除失败");
            }
        }

        log.info("仿真任务删除成功: ID={}", simulationId);
    }

    /**
     * 获取仿真任务详情
     *
     * @param simulationId 仿真任务ID
     * @return 仿真任务详情
     */
    @Override
    public Simulation getSimulationById(Long simulationId) {
        // 检查参数
        if (simulationId == null || simulationId <= 0) {
            log.error("获取仿真任务详情失败：仿真ID不能为空");
            throw new ErrorMsg(-1, "获取仿真任务详情失败：仿真ID不能为空");
        }

        // 查询仿真任务
        Simulation simulation = simulationDao.findById(simulationId);

        // 检查是否存在且未删除
        if (simulation == null || simulation.getIsDeleted() == 1) {
            log.error("获取仿真任务详情失败：仿真任务不存在，simulationId: {}", simulationId);
            throw new ErrorMsg(-1, "获取仿真任务详情失败：仿真任务不存在");
        }

        log.info("获取仿真任务详情成功: ID={}", simulationId);
        return simulation;
    }

    /**
     * 复制仿真任务
     *
     * @param simulationId 原仿真任务ID
     * @return 复制后的新仿真任务
     */
    @Override
    public Simulation copySimulation(Long simulationId) {
        log.info("开始复制仿真任务, simulationId: {}", simulationId);

        // 参数校验
        if (simulationId == null || simulationId <= 0) {
            log.error("复制仿真任务失败：仿真ID不能为空");
            throw new ErrorMsg(-1, "复制仿真任务失败：仿真ID不能为空");
        }

        // 查询原仿真任务
        Simulation sourceSimulation = simulationDao.findById(simulationId);

        // 检查原仿真任务是否存在
        if (sourceSimulation == null || sourceSimulation.getIsDeleted() == 1) {
            log.error("复制仿真任务失败：原仿真任务不存在，simulationId: {}", simulationId);
            throw new ErrorMsg(-1, "复制仿真任务失败：原仿真任务不存在");
        }

        // 生成副本名称（添加"副本"后缀）
        String copyName = sourceSimulation.getSimulationName() + "副本";

        // 检查副本名称是否已存在于同一项目中，如果存在则添加序号
        int copyIndex = 1;
        String finalCopyName = copyName;
        Simulation existingSimulation = simulationDao.findByProjectIdAndName(sourceSimulation.getProjectId(), finalCopyName);

        while (existingSimulation != null && existingSimulation.getIsDeleted() == 0) {
            copyIndex++;
            finalCopyName = copyName + copyIndex;
            existingSimulation = simulationDao.findByProjectIdAndName(sourceSimulation.getProjectId(), finalCopyName);
        }

        // 创建新的仿真任务对象
        Simulation newSimulation = Simulation.builder()
                .simulationId(null)
                .projectId(sourceSimulation.getProjectId())
                .projectName(sourceSimulation.getProjectName())
                .simulationName(finalCopyName)
                .simulationModel(sourceSimulation.getSimulationModel())
                .inputData(sourceSimulation.getInputData())
                .outputData(sourceSimulation.getOutputData())
                .measuredData(sourceSimulation.getMeasuredData())
                .simulationDraft(sourceSimulation.getSimulationDraft())
                .simulationScript(sourceSimulation.getSimulationScript())
                .nodes(sourceSimulation.getNodes())
                .controlStrategy(sourceSimulation.getControlStrategy())
                .runStatus(RunStatus.READY)
                .createdAt(System.currentTimeMillis())
                .updatedAt(System.currentTimeMillis())
                .isDeleted(sourceSimulation.getIsDeleted())
                .build();

        synchronized (simulationConfigLock) {
            // 保存新仿真任务
            int result = simulationDao.createSimulation(newSimulation);
            if (result <= 0) {
                log.error("复制仿真任务失败: 数据库插入失败");
                throw new ErrorMsg(-1, "复制仿真任务失败：数据库操作异常");
            }
        }

        // 复制仿真任务相关的其他数据
        copySimulationRelatedData(sourceSimulation, newSimulation);

        log.info("仿真任务复制成功: 原ID={}, 新ID={}, 新名称={}", simulationId, newSimulation.getSimulationId(), finalCopyName);
        return newSimulation;
    }

    /**
     * 复制仿真任务相关数据
     *
     * @param sourceSimulation 源仿真任务
     * @param targetSimulation 目标仿真任务
     */
    private void copySimulationRelatedData(Simulation sourceSimulation, Simulation targetSimulation) {
        if (sourceSimulation == null || targetSimulation == null) {
            log.error("复制仿真任务数据失败：源仿真任务或目标仿真任务为空");
            throw new ErrorMsg(-1, "复制仿真任务数据失败：源仿真任务或目标仿真任务为空");
        }

        try {
            // 构建源文件夹路径
            String sourceFolder = getCurrentPath() + "/data/" +
                    sourceSimulation.getProjectId() + "/" +
                    sourceSimulation.getSimulationId();

            // 构建目标文件夹路径
            String targetFolder = getCurrentPath() + "/data/" +
                    targetSimulation.getProjectId() + "/" +
                    targetSimulation.getSimulationId();

            // 确保目标文件夹存在，如果不存在则创建
            File targetFolderFile = new File(targetFolder);
            if (!targetFolderFile.exists()) {
                boolean created = targetFolderFile.mkdirs();
                if (!created) {
                    log.error("创建目标仿真任务文件夹失败: {}", targetFolder);
                    throw new ErrorMsg(-1, "创建目标仿真任务文件夹失败");
                }
            }

            // 检查源文件夹是否存在
            File sourceFolderFile = new File(sourceFolder);
            if (!sourceFolderFile.exists()) {
                log.warn("源仿真任务文件夹不存在，无需复制: {}", sourceFolder);
                return ;
            }

            // 复制文件夹下的所有内容
            copyDirectory(sourceFolderFile, targetFolderFile);

            log.info("成功复制仿真任务数据，从 {} 到 {}", sourceFolder, targetFolder);
        } catch (IOException e) {
            log.error("复制仿真任务文件夹时发生错误", e);
            throw new ErrorMsg(-1, "复制仿真任务数据失败：" + e.getMessage());
        }
    }

    /**
     * 上传文件到仿真任务
     *
     * @param simulationId 仿真任务ID
     * @param file 上传的文件
     * @param fileType 文件类型
     * @return 文件保存路径
     */
    @Override
    public String uploadFile(Long simulationId, MultipartFile file, String fileType) {
        if (simulationId == null || file == null || file.isEmpty()) {
            log.error("上传文件失败：仿真任务ID为空或文件为空");
            throw new ErrorMsg(-1, "上传文件失败：仿真任务ID为空或文件为空");
        }

        // 生成文件名（使用原始文件名或生成唯一名称）
        String fileName = file.getOriginalFilename();

        try {
            // 查询仿真任务
            Simulation simulation = simulationDao.findById(simulationId);
            if (simulation == null) {
                log.error("上传文件失败：未找到ID为{}的仿真任务", simulationId);
                throw new ErrorMsg(-1, "上传文件失败：未找到指定的仿真任务");
            }

            // 构建目标文件夹路径
            String targetFolder = getCurrentPath() + "/data/" +
                    simulation.getProjectId() + "/" +
                    simulation.getSimulationId();

            String subFolder;
            String filePath;

            boolean isMeasuredData;

            switch (fileType) {
                /*case "control_strategy":
                    subFolder = targetFolder + "/control_strategy";
                    filePath = subFolder + "/" + fileName;
                    simulation.setControlStrategy(fileName);
                    break;*/
                case "measured_data":
                    isMeasuredData = true;
                    subFolder = targetFolder + "/measured_data";
                    filePath = subFolder + "/" + fileName;
                    simulation.setMeasuredData(fileName);
                    break;
                default:
                    log.error("上传文件失败：未知的文件类型: {}", fileType);
                    throw new ErrorMsg(-1, "上传文件失败：未知的文件类型");
            }

            // 确保子文件夹存在
            File subFolderFile = new File(subFolder);
            if (!subFolderFile.exists()) {
                boolean created = subFolderFile.mkdirs();
                if (!created) {
                    log.error("创建子文件夹失败: {}", subFolder);
                    throw new ErrorMsg(-1, "上传文件失败：无法创建子文件夹");
                }
            }

            // 构建完整的文件路径
            File targetFile = new File(filePath);

            // 保存文件
            file.transferTo(targetFile);

            boolean finalIsMeasuredData = isMeasuredData;
            REPORT_EXECUTOR.submit(() -> {
                if (finalIsMeasuredData) {
                    VoltageQualityReport.generateVoltageReport(filePath);
                }
            });

            log.info("成功上传文件到仿真任务，仿真ID: {}, 文件类型: {}, 文件路径: {}",
                    simulationId, fileType, filePath);

            simulation.setUpdatedAt(System.currentTimeMillis());

            synchronized (simulationConfigLock) {
                int result = simulationDao.updateSimulation(simulation);
                if (result <= 0) {
                    log.error("上传文件失败：数据库更新失败");
                    throw new ErrorMsg(-1, "上传文件失败：数据库操作异常");
                }
            }

            return filePath;
        } catch (IOException e) {
            log.error("上传文件时发生IO错误", e);
            throw new ErrorMsg(-1, "上传文件失败：" + e.getMessage());
        }
    }

    /**
     * 运行仿真任务
     *
     * @param simulationId 仿真任务ID
     */
    @Override
    public void runSimulation(Long simulationId) {
        log.info("开始运行仿真任务，仿真ID: {}", simulationId);

        // 检查参数
        if (simulationId == null || simulationId <= 0) {
            log.error("运行仿真任务失败：仿真ID不能为空或无效");
            throw new ErrorMsg(-1, "运行仿真任务失败：仿真ID不能为空或无效");
        }

        // 检查仿真任务是否存在
        Simulation simulation = simulationDao.findById(simulationId);
        if (simulation == null || simulation.getIsDeleted() == 1) {
            log.error("运行仿真任务失败：仿真任务不存在，simulationId: {}", simulationId);
            throw new ErrorMsg(-1, "运行仿真任务失败：仿真任务不存在");
        }

        // 获取仿真脚本文件夹路径
        String simulationScriptDir = (getCurrentPath() + "/data/" +
                simulation.getProjectId() + "/" +
                simulation.getSimulationId() + "/" +
                "simulation_script" + "/").replace(File.separator, "/");

        // 清理文件夹下所有.csv文件
        deleteFilesByExtension(simulationScriptDir, ".csv");

        // 获取必要的文件路径
        String dssFilePath = simulationScriptDir + "simulationScript.dss";

        // 检查文件是否存在
        if (!checkFileExists(dssFilePath)) {
            log.error("运行仿真任务失败：DSS文件不存在，dssFilePath: {}", dssFilePath);
            throw new ErrorMsg(-1, "运行仿真任务失败：DSS脚本未保存");
        }

        simulation.setRunStatus(RunStatus.RUNNING);
        simulation.setOutputData(null);
        simulation.setUpdatedAt(System.currentTimeMillis());

        synchronized (simulationConfigLock) {
            int result = simulationDao.updateSimulation(simulation);
            if (result <= 0) {
                log.error("运行仿真任务失败：数据库更新失败");
                throw new ErrorMsg(-1, "运行仿真任务失败：数据库操作异常");
            }
        }

        // 将DSS服务的调用部分异步执行
        final String strategyParam = simulation.getControlStrategy();
        DSS_TASK_EXECUTOR.submit(() -> {
            try {
                log.info("开始异步执行DSS仿真，仿真ID: {}", simulationId);
                // 调用DSS服务运行仿真
                ResponseEntity<Map> response = dssUtils.runDssWithStrategy(dssFilePath, strategyParam, dssUrl);
                log.info("仿真任务运行结果，HTTP状态码: {}, HTTP信息：{}, 仿真ID: {}", response.getStatusCode(), Objects.requireNonNull(response.getBody()).get("strategy_result") , simulationId);
                // 处理响应结果
                if (response.getStatusCode() == HttpStatus.OK) {
                    // 成功时设置 outputData 为 null
                    simulation.setOutputData(null);
                    updateSimulationStatus(simulation, RunStatus.SUCCESS, simulationId);
                } else {
                    // 失败时设置 outputData 为响应中的 output_data
                    try {
                        Object outputData = response.getBody() != null ? response.getBody().get("output_data") : null;
                        simulation.setOutputData(outputData != null ? outputData.toString() : null);
                    } catch (Exception ex) {
                        log.warn("获取响应中的output_data失败，设置为null，仿真ID: {}", simulationId, ex);
                        simulation.setOutputData(null);
                    }
                    updateSimulationStatus(simulation, RunStatus.FAILED, simulationId);
                }
            } catch (Exception e) {
                log.error("DSS仿真执行过程中发生异常，仿真ID: {}", simulationId, e);
                // 异常时设置 outputData 为 null
                simulation.setOutputData(null);
                updateSimulationStatus(simulation, RunStatus.FAILED, simulationId);
            }
        });

        log.info("仿真任务已提交到异步队列，仿真ID: {}", simulationId);
    }

    /**
     * 更新仿真状态
     *
     * @param simulation 仿真对象
     * @param status 新状态
     * @param simulationId 仿真ID（用于日志）
     */
    private void updateSimulationStatus(Simulation simulation, RunStatus status, Long simulationId) {
        simulation.setRunStatus(status);
        simulation.setUpdatedAt(System.currentTimeMillis());

        try {
            synchronized (simulationConfigLock) {
                int result = simulationDao.updateSimulation(simulation);
                if (result <= 0) {
                    log.error("更新仿真状态失败：数据库更新失败，仿真ID: {}, 目标状态: {}", simulationId, status);
                } else {
                    log.info("仿真状态已更新为{}，仿真ID: {}", status, simulationId);
                }
            }
        } catch (Exception dbException) {
            log.error("更新仿真状态时数据库操作失败，仿真ID: {}, 目标状态: {}", simulationId, status, dbException);
        }
    }

    /**
     * 获取仿真策略列表
     *
     * @return 仿真策略列表，key为英文，value为中文
     */
    @Override
    public Map<String, String> getSimulationStrategies() {
        Map<String, String> strategies = new LinkedHashMap<>();

        // 添加控制策略
        strategies.put("REACTIVE_POWER_VOLTAGE_CONTROL", "无功-电压控制策略");
        strategies.put("ACTIVE_POWER_VOLTAGE_CONTROL", "有功-电压控制策略");

        return strategies;
    }

    /**
     * 导出仿真配置文件
     *
     * @param simulationId 仿真任务ID
     * @param response  HTTP响应对象，用于写入文件流
     */
    @Override
    public void exportSimulation(Long simulationId, HttpServletResponse response) {
        File tempJsonFile = null;
        File tempZipFile = null;
        try {
            log.info("开始导出仿真配置文件，仿真ID: {}", simulationId);
            // 验证仿真任务是否存在
            Simulation simulation = simulationDao.findById(simulationId);
            if (simulation == null || simulation.getIsDeleted() == 1) {
                log.error("仿真任务不存在或已删除: simulationId={}", simulationId);
                throw new ErrorMsg(-1, "仿真任务不存在或已删除");
            }
            // 生成临时JSON文件
            tempJsonFile = File.createTempFile("simulation_config_", ".json");
            log.info("临时JSON文件已创建: {}", tempJsonFile.getAbsolutePath());
            // 创建配置数据
            Map<String, Object> config = getSimulationMap(simulation);
            // 将配置转换为JSON
            String jsonContent = convertObjectToJsonString(config);
            log.info("成功生成仿真配置JSON内容");
            // 将JSON数据写入文件
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(tempJsonFile))) {
                writer.write(jsonContent);
                log.info("成功将配置数据写入临时文件: {}", tempJsonFile.getAbsolutePath());
            } catch (IOException e) {
                log.error("写入临时JSON文件时发生错误: {}", e.getMessage(), e);
                throw new ErrorMsg(-1, "写入配置文件失败: " + e.getMessage());
            }
            // 获取仿真数据文件夹路径
            String sourceFolder = getCurrentPath() + "/data/" +
                    simulation.getProjectId() + "/" +
                    simulation.getSimulationId();

            File sourceFolderFile = new File(sourceFolder);
            if (!sourceFolderFile.exists() || !sourceFolderFile.isDirectory()) {
                log.warn("仿真数据文件夹不存在: {}", sourceFolder);
            } else {
                log.info("找到仿真数据文件夹: {}", sourceFolder);
            }
            // 创建临时ZIP文件
            tempZipFile = File.createTempFile("simulation_export_", ".zip");
            log.info("临时ZIP文件已创建: {}", tempZipFile.getAbsolutePath());
            // 创建ZIP文件
            try (FileOutputStream fos = new FileOutputStream(tempZipFile);
                 ZipOutputStream zipOut = new ZipOutputStream(fos)) {

                // 添加配置JSON文件到ZIP
                FileInputStream fis = new FileInputStream(tempJsonFile);
                ZipEntry zipEntry = new ZipEntry("config.json");
                zipOut.putNextEntry(zipEntry);

                byte[] bytes = new byte[1024];
                int length;
                while ((length = fis.read(bytes)) >= 0) {
                    zipOut.write(bytes, 0, length);
                }
                fis.close();
                log.info("成功将配置JSON文件添加到ZIP");

                // 如果数据文件夹存在，添加文件夹内容到ZIP
                if (sourceFolderFile.exists() && sourceFolderFile.isDirectory()) {
                    zipFolder(sourceFolderFile, "data", zipOut);
                    log.info("成功将仿真数据文件夹添加到ZIP");
                }

            } catch (IOException e) {
                log.error("创建ZIP文件时发生错误: {}", e.getMessage(), e);
                throw new ErrorMsg(-1, "创建ZIP文件失败: " + e.getMessage());
            }
            // 使用downloadFile方法下载ZIP文件
            downloadFile(tempZipFile.getAbsolutePath(), response);
            log.info("仿真配置和数据已成功打包并准备下载: {}", tempZipFile.getAbsolutePath());
        } catch (IOException e) {
            log.error("导出仿真配置文件失败: {}", e.getMessage(), e);
            throw new ErrorMsg(-1, "导出仿真配置文件失败: " + e.getMessage());
        } finally {
            // 删除临时文件
            if (tempJsonFile != null && tempJsonFile.exists()) {
                if (tempJsonFile.delete()) {
                    log.info("临时JSON文件已成功删除: {}", tempJsonFile.getAbsolutePath());
                } else {
                    log.warn("临时JSON文件删除失败: {}", tempJsonFile.getAbsolutePath());
                }
            }

            if (tempZipFile != null && tempZipFile.exists()) {
                if (tempZipFile.delete()) {
                    log.info("临时ZIP文件已成功删除: {}", tempZipFile.getAbsolutePath());
                } else {
                    log.warn("临时ZIP文件删除失败: {}", tempZipFile.getAbsolutePath());
                }
            }
        }
    }

    private static Map<String, Object> getSimulationMap(Simulation simulation) {
        Map<String, Object> config = new HashMap<>();
        config.put("simulationId", simulation.getSimulationId());
        config.put("simulationName", simulation.getSimulationName());
        config.put("simulationModel", simulation.getSimulationModel());
        config.put("simulationDraft", simulation.getSimulationDraft());
        config.put("inputData", simulation.getInputData());
        config.put("outputData", simulation.getOutputData());
        config.put("measuredData", simulation.getMeasuredData());
        config.put("simulationScript", simulation.getSimulationScript());
        config.put("nodes", simulation.getNodes());
        config.put("controlStrategy", simulation.getControlStrategy());
        return config;
    }

    /**
     * 导入仿真配置文件
     *
     * @param projectId 项目ID
     * @param file 上传的仿真配置文件（ZIP格式）
     * @return 导入成功的仿真任务信息
     */
    @Override
    public Simulation importSimulation(Long projectId, MultipartFile file) {
        log.info("开始导入仿真配置文件，项目ID: {}", projectId);

        if (projectId == null || file == null || file.isEmpty()) {
            log.error("导入仿真配置文件失败：项目ID为空或文件为空");
            throw new ErrorMsg(-1, "导入仿真配置文件失败：项目ID为空或文件为空");
        }

        // 验证项目是否存在
        Project project = projectDao.findById(projectId);
        if (project == null || project.getIsDeleted() == 1) {
            log.error("导入仿真配置文件失败：项目不存在或已删除, projectId={}", projectId);
            throw new ErrorMsg(-1, "导入仿真配置文件失败：项目不存在或已删除");
        }

        // 临时目录，用于解压ZIP文件
        File tempDir = null;
        File tempZipFile = null;
        Simulation newSimulation;

        try {
            // 创建临时ZIP文件
            tempZipFile = File.createTempFile("simulation_import_", ".zip");
            file.transferTo(tempZipFile);
            log.info("成功将上传的文件保存到临时文件: {}", tempZipFile.getAbsolutePath());

            // 创建临时目录用于解压
            tempDir = Files.createTempDirectory("simulation_import_dir_").toFile();
            log.info("创建临时解压目录: {}", tempDir.getAbsolutePath());

            // 解压ZIP文件
            unzipFile(tempZipFile, tempDir);
            log.info("成功解压ZIP文件到临时目录");

            // 读取配置文件
            File configFile = new File(tempDir, "config.json");
            if (!configFile.exists()) {
                log.error("导入仿真配置文件失败：配置文件不存在");
                throw new ErrorMsg(-1, "导入仿真配置文件失败：配置文件不存在或格式不正确");
            }

            // 解析配置文件内容
            Map<String, Object> config = convertJsonFileToMap(configFile);
            log.info("成功解析配置文件内容");

            // 检查是否已存在同名仿真任务
            String simulationName = (String) config.get("simulationName");
            Simulation existingSimulation = simulationDao.findByProjectIdAndName(projectId, simulationName);

            // 如果存在同名仿真任务，则添加后缀
            if (existingSimulation != null && existingSimulation.getIsDeleted() == 0) {
                int copyIndex = 1;
                String baseName = simulationName;
                do {
                    simulationName = baseName + "导入" + copyIndex;
                    copyIndex++;
                    existingSimulation = simulationDao.findByProjectIdAndName(projectId, simulationName);
                } while (existingSimulation != null && existingSimulation.getIsDeleted() == 0);
            }

            // 创建新的仿真任务
            newSimulation = Simulation.builder()
                    .projectId(projectId)
                    .projectName(project.getProjectName())
                    .simulationName(simulationName)
                    .simulationModel((String) config.get("simulationModel"))
                    .simulationDraft((String) config.get("simulationDraft"))
                    .inputData((String) config.get("inputData"))
                    .outputData((String) config.get("outputData"))
                    .measuredData((String) config.get("measuredData"))
                    .simulationScript((String) config.get("simulationScript"))
                    .nodes((String) config.get("nodes"))
                    .controlStrategy((String) config.get("controlStrategy"))
                    .runStatus(RunStatus.READY)
                    .createdAt(System.currentTimeMillis())
                    .updatedAt(System.currentTimeMillis())
                    .isDeleted(0)
                    .build();

            // 保存新仿真任务到数据库
            synchronized (simulationConfigLock) {
                int result = simulationDao.createSimulation(newSimulation);
                if (result <= 0) {
                    log.error("导入仿真配置文件失败：数据库插入失败");
                    throw new ErrorMsg(-1, "导入仿真配置文件失败：数据库操作异常");
                }
            }

            log.info("仿真任务导入成功，ID: {}, 名称: {}", newSimulation.getSimulationId(), simulationName);

            // 创建仿真任务文件夹
            String targetFolder = getCurrentPath() + "/data/" +
                    project.getProjectId() + "/" +
                    newSimulation.getSimulationId();

            File targetFolderFile = new File(targetFolder);
            if (!targetFolderFile.exists()) {
                boolean created = targetFolderFile.mkdirs();
                if (!created) {
                    log.error("创建仿真任务文件夹失败: {}", targetFolder);
                    throw new ErrorMsg(-1, "导入仿真配置文件失败：无法创建仿真任务文件夹");
                }
            }

            // 复制解压后的数据文件
            File sourceDataFolder = new File(tempDir, "data");
            if (sourceDataFolder.exists() && sourceDataFolder.isDirectory()) {
                copyDirectory(sourceDataFolder, targetFolderFile);
                log.info("成功复制仿真数据文件到目标文件夹: {}", targetFolder);
            }

            return newSimulation;

        } catch (IOException e) {
            log.error("导入仿真配置文件时发生IO错误", e);
            throw new ErrorMsg(-1, "导入仿真配置文件失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("导入仿真配置文件时发生错误", e);
            throw new ErrorMsg(-1, "导入仿真配置文件失败：" + e.getMessage());
        } finally {
            // 清理临时文件和目录
            if (tempZipFile != null && tempZipFile.exists()) {
                if (tempZipFile.delete()) {
                    log.info("临时ZIP文件已成功删除: {}", tempZipFile.getAbsolutePath());
                } else {
                    log.warn("临时ZIP文件删除失败: {}", tempZipFile.getAbsolutePath());
                }
            }

            if (tempDir != null && tempDir.exists()) {
                try {
                    deleteDirectory(tempDir);
                    log.info("临时解压目录已成功删除: {}", tempDir.getAbsolutePath());
                } catch (IOException e) {
                    log.warn("删除临时解压目录失败: {}", tempDir.getAbsolutePath(), e);
                }
            }
        }
    }

    /**
     * 获取仿真任务输出路径
     *
     * @param simulationId 仿真任务ID
     * @return 仿真任务的输出路径
     */
    @Override
    public String getSimulationOutputPath(Long simulationId){

        // 检查参数
        if (simulationId == null || simulationId <= 0) {
            log.error("获取仿真任务输出路径失败：仿真ID不能为空或无效");
            throw new ErrorMsg(-1, "获取仿真任务输出路径失败：仿真ID不能为空或无效");
        }

        // 检查仿真任务是否存在
        Simulation simulation = simulationDao.findById(simulationId);
        if (simulation == null || simulation.getIsDeleted() == 1) {
            log.error("获取仿真任务输出路径失败：仿真任务不存在，simulationId: {}", simulationId);
            throw new ErrorMsg(-1, "获取仿真任务输出路径任务失败：仿真任务不存在");
        }


        return Paths.get(getCurrentPath(), "data",
                        String.valueOf(simulation.getProjectId()),
                        String.valueOf(simulation.getSimulationId()),
                        "simulation_script")
                .toString().replace(File.separator, "/");
    }

    /**
     * 获取仿真指标列表，返回树形结构
     *
     * @param simulationId 仿真任务ID
     * @return 指标树形列表
     */
    @Override
    public List<Metric> getSimulationMetrics(Long simulationId) {
        log.info("开始获取仿真指标列表, simulationId: {}", simulationId);
        // 参数校验
        if (simulationId == null || simulationId <= 0) {
            log.error("获取仿真指标失败：仿真ID不能为空");
            throw new ErrorMsg(-1, "获取仿真指标失败：仿真ID不能为空");
        }
        // 查询原仿真任务
        Simulation simulation = simulationDao.findById(simulationId);
        // 检查仿真任务是否存在
        if (simulation == null || simulation.getIsDeleted() == 1) {
            log.error("获取仿真指标失败：仿真任务不存在，simulationId: {}", simulationId);
            throw new ErrorMsg(-1, "获取仿真指标失败：仿真任务不存在");
        }
        // 创建返回结果列表
        List<Metric> result = new ArrayList<>();
        Metric rootMetric = Metric.builder()
                .metricId("simulationOutputMetric")
                .metricName("仿真结果指标")
                .children(new ArrayList<>())
                .build();
        result.add(rootMetric);
        try {
            // 1. 从CSV文件中获取仿真指标
            getCsvMetrics(simulation, rootMetric);
            // 2. 从Excel文件中获取测量数据指标
            getExcelMetrics(simulation, result);
            log.info("成功获取仿真指标列表，共找到 {} 个分类", result.size());
            return result;
        } catch (Exception e) {
            log.error("获取仿真指标列表失败: {}", e.getMessage());
            throw new ErrorMsg(-1, "获取仿真指标列表失败：" + e.getMessage());
        }
    }

    /**
     * 从CSV文件中获取仿真指标
     *
     * @param simulation 仿真任务对象
     * @param rootMetric 根指标节点
     */
    private void getCsvMetrics(Simulation simulation, Metric rootMetric) {
        // 处理仿真脚本结果目录中的CSV文件
        String scriptFolder = getCurrentPath() + "/data/" +
                simulation.getProjectId() + "/" +
                simulation.getSimulationId() + "/" +
                "simulation_script";

        // 调用CsvUtils提取指标
        CsvUtils.extractMetrics(scriptFolder, rootMetric);
    }

    /**
     * 从Excel文件中获取测量数据指标
     *
     * @param simulation 仿真任务对象
     * @param result 结果指标列表
     */
    private void getExcelMetrics(Simulation simulation, List<Metric> result) {
        // 处理测量数据中的电压指标（如果存在）
        if (simulation.getMeasuredData() != null && !simulation.getMeasuredData().isEmpty()) {
            // 构建文件路径
            String filePath = getCurrentPath() + "/data/" +
                    simulation.getProjectId() + "/" +
                    simulation.getSimulationId() + "/measured_data/" +
                    simulation.getMeasuredData();
            // 获取文件对象
            File file = new File(filePath);
            // 检查文件是否存在且是否为文件
            if (file.exists() && file.isFile()) {
                // 检查是否为Excel文件
                String fileName = file.getName();
                if (fileName.toLowerCase().endsWith(".xlsx") || fileName.toLowerCase().endsWith(".xls")) {
                    log.info("开始处理电压数据文件: {}", filePath);
                    try {
                        // 使用ExcelUtils提取指标
                        List<Metric> voltageMetrics = ExcelUtils.extractMetrics(filePath);
                        if (voltageMetrics != null && !voltageMetrics.isEmpty()) {
                            log.info("成功提取文件 {} 的电压指标，共 {} 个指标", fileName, voltageMetrics.size());
                            result.addAll(voltageMetrics);
                        } else {
                            log.warn("文件 {} 没有提取到有效电压指标", fileName);
                        }
                    } catch (Exception e) {
                        log.error("处理电压数据文件失败: {}, 错误: {}", fileName, e.getMessage(), e);
                        // 不中断处理，继续返回已获取的仿真指标
                        log.warn("无法处理电压指标文件，但仍会返回已获取的仿真指标");
                    }
                } else {
                    log.warn("测量数据文件不是Excel格式: {}", filePath);
                }
            } else {
                log.warn("测量数据文件不存在或不是有效文件: {}", filePath);
            }
        }
    }

    /**
     * 获取特定仿真任务的指标数据
     *
     * @param simulationId   仿真任务ID，用于确定数据所在的目录
     * @param metricId       指标ID，格式为"文件名.列名"
     * @param startTimestamp 起始时间戳
     * @return 包含指标数据的{@link MetricData}对象，包括数据点列表和统计信息
     */
    @Override
    public List<MetricData> getSimulationMetricData(Long simulationId, String metricId, Long startTimestamp) {
        log.info("开始获取仿真指标数据, simulationId: {}, metricId: {}", simulationId, metricId);
        // 参数校验
        if (simulationId == null || simulationId <= 0) {
            log.error("获取仿真指标数据失败：仿真ID不能为空");
            throw new ErrorMsg(-1, "获取仿真指标数据失败：仿真ID不能为空");
        }
        if (metricId == null || metricId.trim().isEmpty()) {
            log.error("获取仿真指标数据失败：指标ID不能为空");
            throw new ErrorMsg(-1, "获取仿真指标数据失败：指标ID不能为空");
        }
        // 查询原仿真任务
        Simulation simulation = simulationDao.findById(simulationId);
        // 检查仿真任务是否存在
        if (simulation == null || simulation.getIsDeleted() == 1) {
            log.error("获取仿真指标数据失败：仿真任务不存在，simulationId: {}", simulationId);
            throw new ErrorMsg(-1, "获取仿真指标数据失败：仿真任务不存在");
        }
        try {
            // 尝试从仿真CSV文件中获取数据
            List<MetricData> csvData = getCsvMetricData(simulation, metricId, startTimestamp);
            if (!csvData.isEmpty()) {
                return csvData;
            }
            // 如果CSV文件中未找到数据，且存在测量数据文件，则尝试从Excel文件中获取数据
            if (simulation.getMeasuredData() != null && !simulation.getMeasuredData().isEmpty()) {
                return getExcelMetricData(simulation, metricId);
            }
            // 如果两种方式都没有找到数据，返回空列表
            log.warn("未找到指标数据: simulationId={}, metricId={}", simulationId, metricId);
            return new ArrayList<>();
        } catch (ErrorMsg e) {
            throw e;
        } catch (Exception e) {
            log.error("获取仿真指标数据失败: {}", e.getMessage(), e);
            throw new ErrorMsg(-1, "获取仿真指标数据失败：" + e.getMessage());
        }
    }

    /**
     * 从CSV文件中获取指标数据
     *
     * @param simulation     仿真任务对象
     * @param metricId       指标ID
     * @param startTimestamp 起始时间戳
     * @return 指标数据列表
     */
    private List<MetricData> getCsvMetricData(Simulation simulation, String metricId, Long startTimestamp) {
        // 解析metricId，提取文件名
        String[] parts = metricId.split("\\.", 2);
        if (parts.length != 2) {
            // 如果不符合CSV指标ID格式，返回空列表
            return new ArrayList<>();
        }
        String fileName = parts[0];

        // 构建文件路径
        String folderPath = getCurrentPath() + "/data/" +
                simulation.getProjectId() + "/" +
                simulation.getSimulationId() + "/" +
                "simulation_script";
        String filePath = folderPath + "/" + fileName + ".csv";

        // 调用CsvUtils获取指标数据
        try {
            return CsvUtils.getMetricData(filePath, metricId, startTimestamp);
        } catch (RuntimeException e) {
            // 转换为业务异常
            throw new ErrorMsg(-1, e.getMessage());
        }
    }

    /**
     * 从Excel文件中获取电压指标数据
     * @param simulation 仿真任务对象
     * @param metricId 指标ID
     * @return 指标数据列表
     */
    private List<MetricData> getExcelMetricData(Simulation simulation, String metricId) {
        // 构建文件路径
        String filePath = getCurrentPath() + "/data/" +
                simulation.getProjectId() + "/" +
                simulation.getSimulationId() + "/measured_data/" +
                simulation.getMeasuredData();
        // 获取文件对象
        File file = new File(filePath);
        // 检查文件是否存在且是否为文件
        if (!file.exists() || !file.isFile()) {
            log.error("电压数据文件不存在或不是有效文件: {}", filePath);
            throw new ErrorMsg(-1, "获取指标数据失败：找不到测量数据文件");
        }
        // 检查是否为Excel文件
        String fileName = file.getName();
        if (!fileName.toLowerCase().endsWith(".xlsx") && !fileName.toLowerCase().endsWith(".xls")) {
            log.error("文件不是Excel格式: {}", filePath);
            throw new ErrorMsg(-1, "获取指标数据失败：测量数据文件格式不正确");
        }
        log.info("开始处理Excel指标数据文件: {}", fileName);
        // 构建Metric对象
        Metric targetMetric = Metric.builder()
                .metricId(metricId)
                .build();
        try {
            // 调用Excel工具类获取指标数据
            MetricData metricData = ExcelUtils.getMetricData(filePath, targetMetric);
            // 将单个MetricData放入列表中返回
            List<MetricData> result = new ArrayList<>();
            if (metricData != null) {
                result.add(metricData);
                log.info("成功获取Excel电压指标数据，共 {} 个数据点",
                        metricData.getDataPoints() != null ? metricData.getDataPoints().size() : 0);
            } else {
                log.warn("未找到Excel电压指标数据: metricId={}", metricId);
            }
            return result;
        } catch (Exception e) {
            log.error("处理Excel指标数据失败: {}", e.getMessage(), e);
            throw new ErrorMsg(-1, "获取指标数据失败：" + e.getMessage());
        }
    }

    /**
     * 批量获取特定仿真任务的指标数据
     *
     * @param simulationId   仿真任务ID，用于确定数据所在的目录
     * @param metricIds      指标ID列表，格式为"文件名.列名"
     * @param startTimestamp 仿真指标起始时间戳
     * @return 包含指标数据的{@link MetricData}对象列表，包括数据点列表和统计信息
     */
    @Override
    public List<MetricData> getMultipleSimulationMetricData(Long simulationId, List<String> metricIds, Long startTimestamp) {
        log.info("开始批量获取仿真指标数据, simulationId: {}, metricIds: {}", simulationId, metricIds);

        // 参数校验
        if (simulationId == null || simulationId <= 0) {
            log.error("批量获取仿真指标数据失败：仿真ID不能为空");
            throw new ErrorMsg(-1, "批量获取仿真指标数据失败：仿真ID不能为空");
        }

        if (metricIds == null || metricIds.isEmpty()) {
            log.error("批量获取仿真指标数据失败：指标ID列表不能为空");
            throw new ErrorMsg(-1, "批量获取仿真指标数据失败：指标ID列表不能为空");
        }

        // 验证指标ID列表中是否有空值
        for (String metricId : metricIds) {
            if (metricId == null || metricId.trim().isEmpty()) {
                log.error("批量获取仿真指标数据失败：指标ID不能为空");
                throw new ErrorMsg(-1, "批量获取仿真指标数据失败：指标ID不能为空");
            }
        }

        List<MetricData> allMetricData = new ArrayList<>();
        List<String> failedMetricIds = new ArrayList<>();
        List<String> successfulMetricIds = new ArrayList<>();

        try {
            // 逐个获取指标数据
            for (String metricId : metricIds) {
                try {
                    log.debug("正在获取指标数据: {}", metricId);
                    List<MetricData> metricDataList = getSimulationMetricData(simulationId, metricId, startTimestamp);
                    allMetricData.addAll(metricDataList);
                    successfulMetricIds.add(metricId);
                    log.debug("成功获取指标数据: {}, 数据点数量: {}", metricId,
                            metricDataList.stream().mapToInt(data ->
                                    data.getDataPoints() != null ? data.getDataPoints().size() : 0).sum());
                } catch (Exception e) {
                    log.warn("获取指标数据失败: {}, 错误信息: {}", metricId, e.getMessage());
                    failedMetricIds.add(metricId);
                    // 继续处理其他指标，不中断整个批量操作
                }
            }

            // 为MetricData设置metricName
            setMetricNames(allMetricData, successfulMetricIds);

            // 记录处理结果
            int successCount = metricIds.size() - failedMetricIds.size();
            log.info("批量获取仿真指标数据完成, 成功: {}, 失败: {}, 总共获取数据点: {}",
                    successCount, failedMetricIds.size(),
                    allMetricData.stream().mapToInt(data ->
                            data.getDataPoints() != null ? data.getDataPoints().size() : 0).sum());

            // 如果有失败的指标，记录详细信息
            if (!failedMetricIds.isEmpty()) {
                log.warn("以下指标获取失败: {}", failedMetricIds);
            }

            return allMetricData;

        } catch (Exception e) {
            log.error("批量获取仿真指标数据发生未知错误: {}", e.getMessage(), e);
            throw new ErrorMsg(-1, "批量获取仿真指标数据失败：" + e.getMessage());
        }
    }

    /**
     * 导出特定仿真任务的指标数据到Excel文件
     *
     * @param simulationId   仿真任务ID，用于确定数据所在的目录
     * @param pic            图表Base64字符串
     * @param metricIds      指标ID列表，格式为"文件名.列名"
     * @param startTimestamp 仿真指标起始时间戳
     * @param response       HTTP响应对象，用于写入Excel文件流
     */
    @Override
    public void exportMultipleSimulationMetricData(Long simulationId, String pic, List<String> metricIds, Long startTimestamp, HttpServletResponse response) {
        try {
            log.info("开始导出仿真指标数据，仿真ID: {}, 指标数量: {}", simulationId, metricIds.size());
            // 验证仿真任务是否存在
            Simulation simulation = simulationDao.findById(simulationId);
            if (simulation == null || simulation.getIsDeleted() == 1) {
                log.error("仿真任务不存在或已删除: simulationId={}", simulationId);
                throw new ErrorMsg(-1, "仿真任务不存在或已删除");
            }
            // 获取指标数据
            List<MetricData> metricDataList = getMultipleSimulationMetricData(simulationId, metricIds, startTimestamp);
            log.info("成功获取{}个指标的数据", metricDataList.size());
            // 设置响应头
            String fileName = "simulation_metrics_" + simulationId + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            // 创建并导出Excel
            try (OutputStream outputStream = response.getOutputStream()) {
                generateMetricExcel(metricDataList, pic, outputStream);
                log.info("仿真指标数据已成功导出为Excel文件: {}", fileName);
            } catch (IOException e) {
                log.error("创建Excel文件时发生错误: {}", e.getMessage(), e);
                throw new ErrorMsg(-1, "创建Excel文件失败: " + e.getMessage());
            }
        } catch (Exception e) {
            log.error("导出仿真指标数据失败: {}", e.getMessage(), e);
            throw new ErrorMsg(-1, "导出仿真指标数据失败: " + e.getMessage());
        }
    }

    /**
     * 为MetricData列表设置metricName
     * 根据metricId生成能够区分所有指标的最短后缀作为metricName
     *
     * @param metricDataList MetricData列表
     * @param metricIds 对应的metricId列表
     */
    private void setMetricNames(List<MetricData> metricDataList, List<String> metricIds) {
        if (metricDataList.isEmpty() || metricIds.isEmpty()) {
            return;
        }

        // 生成metricName映射
        Map<String, String> metricNameMap = generateMetricNames(metricIds);

        // 为每个MetricData设置metricName
        for (MetricData metricData : metricDataList) {
            if (metricData.getMetric() != null && metricData.getMetric().getMetricId() != null) {
                String metricId = metricData.getMetric().getMetricId();
                String metricName = metricNameMap.get(metricId);
                if (metricName != null) {
                    metricData.getMetric().setMetricName(metricName);
                    log.debug("设置metricName: {} -> {}", metricId, metricName);
                }
            }
        }
    }

    /**
     * 根据metricId列表生成能够区分所有指标的最短后缀作为metricName
     * 从字符串末尾开始往前遍历，直到找到能区分所有metricId的位置，且该位置前一个字符是'.'
     *
     * @param metricIds metricId列表
     * @return metricId到metricName的映射
     */
    private Map<String, String> generateMetricNames(List<String> metricIds) {
        Map<String, String> result = new HashMap<>();

        if (metricIds.size() == 1) {
            String metricId = metricIds.get(0);
            int lastDotIndex = metricId.lastIndexOf('.');
            String name = lastDotIndex >= 0 ? metricId.substring(lastDotIndex + 1) : metricId;
            result.put(metricId, name);
            return result;
        }

        // 找到所有可能的切分点（即所有的'.'位置）
        List<List<Integer>> allDotPositions = metricIds.stream()
                .map(id -> {
                    List<Integer> positions = new ArrayList<>();
                    for (int i = id.length() - 1; i >= 0; i--) {
                        if (id.charAt(i) == '.') {
                            positions.add(i);
                        }
                    }
                    return positions;
                })
                .collect(Collectors.toList());

        // 从最后一个'.'开始，逐步向前检查
        for (int i = 0; i < allDotPositions.get(0).size(); i++) {
            Set<String> suffixes = new HashSet<>();
            boolean canDistinguish = true;

            for (int j = 0; j < metricIds.size(); j++) {
                String metricId = metricIds.get(j);
                if (i >= allDotPositions.get(j).size()) {
                    canDistinguish = false;
                    break;
                }
                int dotPosition = allDotPositions.get(j).get(i);
                String suffix = metricId.substring(dotPosition + 1);
                if (suffixes.contains(suffix)) {
                    canDistinguish = false;
                    break;
                }
                suffixes.add(suffix);
            }

            if (canDistinguish) {
                for (int j = 0; j < metricIds.size(); j++) {
                    String metricId = metricIds.get(j);
                    int dotPosition = allDotPositions.get(j).get(i);
                    String name = metricId.substring(dotPosition + 1);
                    result.put(metricId, name);
                }
                return result;
            }
        }

        // 如果无法找到合适的位置，返回完整的metricId作为名称
        for (String metricId : metricIds) {
            result.put(metricId, metricId);
        }

        return result;
    }

    /**
     * 获取台区电压质量统计报表
     *
     * @param simulationId 仿真任务ID，用于确定数据所在的目录
     * @return 包含电压统计数据的{@link CourtsStatistics}对象，包括合格率、越限率等统计信息
     */
    @Override
    public CourtsStatistics getCourtsVoltageReport(Long simulationId) {
        try {
            // 获取仿真任务信息
            Simulation simulation = simulationDao.findById(simulationId);
            if (simulation == null) {
                log.error("获取电压报表失败：未找到ID为{}的仿真任务", simulationId);
                return null;
            }

            // 构建报表文件路径
            String rootPath = getCurrentPath() + "/data/" +
                    simulation.getProjectId() + "/" +
                    simulation.getSimulationId() +
                    "/measured_data/";

            String reportPath = rootPath +
                    "电压合格率报表.xlsx";

            File reportFile = new File(reportPath);

            // 检查报表文件是否存在，如果不存在则生成
            if (!reportFile.exists()) {
                log.info("电压合格率报表文件不存在，开始生成报表：{}", reportPath);

                if (simulation.getMeasuredData() == null || simulation.getMeasuredData().isEmpty()) {
                    log.error("未找到测量数据文件，无法生成电压报表");
                    throw new ErrorMsg(-1, "未找到测量数据文件");
                } else {
                    VoltageQualityReport.generateVoltageReport(rootPath + simulation.getMeasuredData());
                }

                // 再次检查文件是否成功生成
                if (!reportFile.exists()) {
                    log.error("生成电压合格率报表失败，文件不存在：{}", reportPath);
                    return null;
                }
            }

            // 读取数据
            return VoltageQualityReport.readFromExcel(reportFile);
        } catch (Exception e) {
            log.error("获取电压报表数据时发生错误", e);
            return null;
        }
    }

    /**
     * 获取用户电压合格率统计报表（支持分页和关键字查询）
     *
     * @param simulationId 仿真任务ID，用于确定数据所在的目录
     * @param keyword 关键字，用于筛选用户名称
     * @param pageNum 当前页码（从1开始）
     * @param pageSize 每页显示条数
     * @return 包含用户电压统计数据的{@link List<UserStatistics>}对象，包括合格率、越限率等统计信息
     */
    @Override
    public List<UserStatistics> getUserVoltageReport(Long simulationId, String keyword,
                                                     Integer pageNum, Integer pageSize) {
        try {
            // 获取仿真任务信息
            Simulation simulation = simulationDao.findById(simulationId);
            if (simulation == null) {
                log.error("获取用户电压报表失败：未找到ID为{}的仿真任务", simulationId);
                return Collections.emptyList();
            }

            // 构建报表文件路径
            String rootPath = getCurrentPath() + "/data/" +
                    simulation.getProjectId() + "/" +
                    simulation.getSimulationId() +
                    "/measured_data/";

            String reportPath = rootPath + "电压合格率报表.xlsx";

            File reportFile = new File(reportPath);

            // 检查报表文件是否存在，如果不存在则生成
            if (!reportFile.exists()) {
                log.info("电压合格率报表文件不存在，开始生成报表：{}", reportPath);

                if (simulation.getMeasuredData() == null || simulation.getMeasuredData().isEmpty()) {
                    log.error("未找到测量数据文件，无法生成用户电压报表");
                    throw new ErrorMsg(-1, "未找到测量数据文件");
                } else {
                    VoltageQualityReport.generateVoltageReport(rootPath + simulation.getMeasuredData());
                }

                if (!reportFile.exists()) {
                    log.error("生成用户电压合格率报表失败，文件不存在：{}", reportPath);
                    return Collections.emptyList();
                }
            }

            // 读取数据（带分页和关键字查询）
            return VoltageQualityReport.readUserStatisticsFromReport(reportFile, keyword, pageNum, pageSize);

        } catch (Exception e) {
            log.error("获取用户电压报表数据时发生错误", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取用户电压合格率统计报表中符合条件的记录总数
     *
     * @param simulationId 仿真任务ID，用于确定数据所在的目录
     * @param keyword 关键字，用于筛选用户名称
     * @return 符合条件的记录总数
     */
    @Override
    public Long countUserVoltageReport(Long simulationId, String keyword) {
        try {
            // 获取仿真任务信息
            Simulation simulation = simulationDao.findById(simulationId);
            if (simulation == null) {
                log.error("获取用户电压报表记录数失败：未找到ID为{}的仿真任务", simulationId);
                return 0L;
            }

            // 构建报表文件路径
            String rootPath = getCurrentPath() + "/data/" +
                    simulation.getProjectId() + "/" +
                    simulation.getSimulationId() +
                    "/measured_data/";

            String reportPath = rootPath + "电压合格率报表.xlsx";

            File reportFile = new File(reportPath);

            // 检查报表文件是否存在，如果不存在则生成
            if (!reportFile.exists()) {
                log.info("电压合格率报表文件不存在，开始生成报表：{}", reportPath);

                if (simulation.getMeasuredData() == null || simulation.getMeasuredData().isEmpty()) {
                    log.error("未找到测量数据文件，无法生成用户电压报表");
                    throw new ErrorMsg(-1, "未找到测量数据文件");
                } else {
                    VoltageQualityReport.generateVoltageReport(rootPath + simulation.getMeasuredData());
                }

                if (!reportFile.exists()) {
                    log.error("生成用户电压合格率报表失败，文件不存在：{}", reportPath);
                    return 0L;
                }
            }

            // 获取符合条件的记录总数
            return (long) VoltageQualityReport.getUserStatisticsCount(reportFile, keyword);

        } catch (Exception e) {
            log.error("获取用户电压报表记录数时发生错误", e);
            return 0L;
        }
    }

    /**
     * 计算仿真数据的电压合格率统计
     *
     * @param simulationId 仿真任务ID
     * @return 电压合格率统计结果，包含总数据点数、合格数据点数、合格率百分比等信息
     */
    @Override
    public CourtsStatistics calculateSimulationVoltageQualityRate(Long simulationId) {
        log.info("开始计算仿真任务电压合格率，仿真ID: {}", simulationId);

        // 参数校验
        if (simulationId == null || simulationId <= 0) {
            log.error("计算仿真电压合格率失败：仿真ID不能为空或无效");
            throw new ErrorMsg(-1, "计算仿真电压合格率失败：仿真ID不能为空或无效");
        }

        // 检查仿真任务是否存在
        Simulation simulation = simulationDao.findById(simulationId);
        if (simulation == null || simulation.getIsDeleted() == 1) {
            log.error("计算仿真电压合格率失败：仿真任务不存在，simulationId: {}", simulationId);
            throw new ErrorMsg(-1, "计算仿真电压合格率失败：仿真任务不存在");
        }

        // 获取仿真脚本输出目录路径
        String simulationScriptDir = FileUtils.getCurrentPath() + "/data/" +
                simulation.getProjectId() + "/" +
                simulation.getSimulationId() + "/" +
                "simulation_script";

        log.info("仿真输出目录: {}", simulationScriptDir);

        // 检查目录是否存在
        File directory = new File(simulationScriptDir);
        if (!directory.exists() || !directory.isDirectory()) {
            log.warn("仿真输出目录不存在: {}", simulationScriptDir);
            return buildEmptyVoltageStatistics();
        }

        try {
            // 记录开始时间用于性能监控
            long startTime = System.currentTimeMillis();

            // 从CSV文件目录中提取电压数据并计算统计信息
            VoltageStatisticsCalculator calculator = CsvUtils.extractVoltageDataFromCSVDirectory(simulationScriptDir);

            // 记录处理时间
            long processingTime = System.currentTimeMillis() - startTime;

            // 检查是否有电压数据
            if (calculator.getTotalReadings() == 0) {
                log.warn("仿真任务 {} 的输出目录中未找到电压数据，处理时间: {}ms", simulationId, processingTime);
                return buildEmptyVoltageStatistics();
            }

            // 构建统计结果
            CourtsStatistics result = CourtsStatistics.builder()
                    .totalUsers(0)  // 仿真场景下用户数为0
                    .qualifiedRate(calculator.getQualifiedRate())
                    .aboveMaxRate(calculator.getAboveMaxRate())
                    .belowMinRate(calculator.getBelowMinRate())
                    .maxVoltage(calculator.getMaxVoltage())
                    .minVoltage(calculator.getMinVoltage())
                    .totalReadings(calculator.getTotalReadings())
                    .qualifiedReadings(calculator.getQualifiedReadings())
                    .aboveMaxReadings(calculator.getAboveMaxReadings())
                    .belowMinReadings(calculator.getBelowMinReadings())
                    .build();

            log.info("仿真任务 {} 电压合格率计算完成：总数据点={}, 合格数据点={}, 合格率={}%, 处理时间={}ms",
                    simulationId, result.getTotalReadings(), result.getQualifiedReadings(),
                    String.format("%.2f", result.getQualifiedRate()), processingTime);

            return result;

        } catch (Exception e) {
            log.error("计算仿真电压合格率时发生错误，仿真ID: {}", simulationId, e);
            throw new ErrorMsg(-1, "计算仿真电压合格率失败：" + e.getMessage());
        }
    }

    /**
     * 构建空的电压统计结果
     *
     * @return 空的CourtsStatistics对象
     */
    private CourtsStatistics buildEmptyVoltageStatistics() {
        return CourtsStatistics.builder()
                .totalUsers(0)
                .qualifiedRate(0.0)
                .aboveMaxRate(0.0)
                .belowMinRate(0.0)
                .maxVoltage(0.0)
                .minVoltage(0.0)
                .totalReadings(0)
                .qualifiedReadings(0)
                .aboveMaxReadings(0)
                .belowMinReadings(0)
                .build();
    }

}

