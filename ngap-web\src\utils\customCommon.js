import _ from "lodash";
import moment from "moment";
import { Message } from "element-ui";
import Axios from "axios";
import { mhCONST } from "@/config/const";
import { getEnumByName, getEnumTextById } from "./enumInfo.js";
import customApi from "@/api/custom";
import { httping } from "@omega/http";
import AES from "./aes.js";
import omegaI18n from "@omega/i18n";

const InvalidValue = [
  null,
  -2147483648,
  "NaN",
  NaN,
  "Infinity",
  Infinity,
  2147483648
];
import Task from "@/utils/task.js";
const placeholder = "--";

const isInvalid = val => {
  return InvalidValue.includes(val);
};

function transConditions(queryBody) {
  if (!_.has(queryBody, "rootCondition")) return queryBody;
  let conditions = _.get(queryBody, "rootCondition.filter.expressions", []);
  let result = {
    page: _.get(queryBody, "rootCondition.page")
  };
  conditions.forEach(item => {
    result[item.prop] = item.limit;
  });
  return result;
}

function get(object, path, defaultValue = "--") {
  let val = _.get(object, path, defaultValue);
  if (isInvalid(val)) {
    return defaultValue;
  }
  return val;
}
// 清除图表数据
function clearChartData(chartConfig) {
  chartConfig.options = Object.assign({}, chartConfig.options, { series: [] });
}
// 通过周期 获取当前的时段
function getCurrentTimeByCycle(aggregationCycle = 14) {
  let time = moment();
  // 12 当日  14 当月   17 当年
  let unitMap = { 12: "d", 14: "M", 17: "y" };
  let startTime = time.startOf(unitMap[aggregationCycle]).valueOf();
  let endTime = time.endOf(unitMap[aggregationCycle]).valueOf();
  return { startTime, endTime, aggregationCycle };
}

// 格式化日期
function formatDate(time, format = "YYYY-MM-DD HH:mm:ss", defaultValue = "--") {
  if (isInvalid(time)) {
    return defaultValue;
  }
  let val = +new Date(time) || +time || time;

  return moment(val).format(format);
}
// 将数字四舍五入,输出数字
function roundNumber(val, precision = 2, defaultValue = undefined) {
  if (isInvalid(val)) {
    return defaultValue;
  }
  let num = _.round(val, precision);
  return _.isNaN(num) ? defaultValue : num;
}

// 设置数字toFixed,增加单位,输出字符串
function setNumFixed(val, precision = 2, unit = "", defaultValue = "--") {
  if (isInvalid(val)) {
    return defaultValue;
  }
  let num = _.round(val, precision);
  return (_.isNaN(num) ? defaultValue : num.toFixed(precision)) + unit;
}

function startRealtimeTasks(interval = 5000) {
  let realtimeTasks = this.realtimeTasks;
  let taskNames = Object.keys(realtimeTasks);
  taskNames.forEach(name => {
    realtimeTasks[name] = realtimeTasks[name] || new Task(this[name], interval);
    realtimeTasks[name].start(true);
  });
}
function startRealtimeTasksOfDifferenceInterval(
  differenceName,
  interval1 = 1000 * 60 * 10,
  interval2 = 5000
) {
  let realtimeTasks = this.realtimeTasks;
  let taskNames = Object.keys(realtimeTasks);
  taskNames.forEach(name => {
    let interval = differenceName.includes(name) ? interval1 : interval2;
    realtimeTasks[name] = realtimeTasks[name] || new Task(this[name], interval);
    realtimeTasks[name].start(true);
  });
}

function stopRealtimeTasks() {
  let realtimeTasks = this.realtimeTasks;
  for (const name in realtimeTasks) {
    if (Object.hasOwnProperty.call(realtimeTasks, name)) {
      const task = realtimeTasks[name];
      task && task.stop();
    }
  }
}

function transNavMenu(arr) {
  return arr.map(item => {
    if (["subMenu", "menuItemGroup"].includes(item.type)) {
      return {
        name: item.label,
        children: transNavMenu(item.subMenuList)
      };
    }
    return {
      name: item.label,
      id: item.permission
    };
  });
}

// 校验是否为正确的ip地址
function validateIpAddress(ipAddress) {
  let ipAddressRegex =
    /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  let isIpAddressValid = ipAddressRegex.test(ipAddress);
  return isIpAddressValid;
}

function arrayBufferToBlob(buffer, type = "application/octet-stream") {
  return new Blob([buffer], { type });
}

function downloadFile(data, fileName) {
  const href = URL.createObjectURL(data);
  const link = document.createElement("a");
  link.href = href;
  link.setAttribute("download", fileName);
  document.body.appendChild(link);
  link.click(); // 触发下载
  document.body.removeChild(link); // 清理DOM
  URL.revokeObjectURL(href); // 吊销URL对象
}

function getFileNameFromHeader(res) {
  let filename = "";
  const disposition =
    res.headers["content-disposition"] || res.headers["Content-Disposition"];

  if (disposition && disposition.indexOf("attachment") !== -1) {
    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
    const matches = filenameRegex.exec(disposition);
    if (matches != null && matches[1]) {
      filename = matches[1].replace(/['"]/g, "");
    }
  }
  return decodeURIComponent(filename);
}

function downloadFileWithName(res) {
  const fileName = getFileNameFromHeader(res);
  let data = res.data;
  if (data instanceof ArrayBuffer) {
    data = arrayBufferToBlob(data);
  }
  downloadFile(data, fileName);
}

export default {
  get,
  placeholder,
  downloadFileWithName,
  validateIpAddress,
  // 将数字四舍五入,输出数字
  roundNumber,
  // 设置数字toFixed,增加单位,输出字符串
  setNumFixed,
  formatDate,
  needFilterDraw() {
    let needFilter;
    if ([3].includes(mhCONST("systemType"))) {
      needFilter = ["switchControl", "signalStructure"];
    } else {
      needFilter = ["airControl", "environmentMonitor", "securityMonitor"];
    }
    return needFilter;
  },
  formatSystemName() {
    switch (mhCONST("systemType")) {
      case 1:
      case 2:
      case 5:
        return $T("EPMS项目");
      case 4:
        return $T("BAS项目");
      case 3:
        return $T("DCIM项目");
      default:
        return $T("数据中心");
    }
  },
  decryptMsg(value, msg) {
    if (!value) return "";
    let info = ["电话", "邮箱", "电话：", "邮箱：", "移动电话", "电子邮箱"];
    if (msg && !info.includes(msg)) return value;
    if (!mhCONST("isNewAuthService")) return value;
    return AES.decrypt(value);
  },
  capitalize(value) {
    if (!value) return "";
    value = value.toString();
    // return value.charAt(0).toUpperCase() + value.slice(1)
    return value.substring(0, 2) + "*****" + value.substring(value.length - 2);
  },

  initDateRange(type = "d") {
    const start = moment().startOf(type).valueOf();
    const end = moment().endOf(type).valueOf() + 1;
    return [start, end];
  },
  // 格式化坐标轴
  formatXAxisLabel(time, cycle = 14) {
    // 年17，月14，日12

    if (!cycle) return +time;
    let value = +time;
    if (cycle === 17) return moment(value).format("MM" + $T("月 "));
    if (cycle === 14) return moment(value).format("DD" + $T("日 "));
    if (cycle === 12) return moment(value).format("HH" + $T("时 "));
  },
  // 获取根据字段名表格数据
  getColumnValue(row, column, cellValue, index) {
    return get(row, column.property);
  },
  getColumnNumber(precision) {
    return (row, column, cellValue, index) => setNumFixed(cellValue, precision);
  },
  transNavMenu,
  startRealtimeTasks,
  stopRealtimeTasks,
  startRealtimeTasksOfDifferenceInterval,
  validateUploadImg(val) {
    const isJPG = val.type === "image/jpeg";
    const isGIF = val.type === "image/gif";
    const isPNG = val.type === "image/png";
    const isBMP = val.type === "image/bmp";
    const isLt2M = val.size / 1024 / 1024 < 2;

    if (!isJPG && !isGIF && !isPNG && !isBMP) {
      Message.error($T("上传图片必须是JPG/GIF/PNG/BMP 格式!"));
    }
    if (!isLt2M) {
      Message.error($T("上传图片大小不能超过 2MB!"));
    }
    return (isJPG || isBMP || isGIF || isPNG) && isLt2M;
  },
  //格式化日期

  formatStr(string) {
    if (string === "") return "--";
    if (!string) return "--";
    return string;
  },

  downExcel(url, data, name, timeout = 60000, cb) {
    Axios.post(url, data, {
      responseType: "arraybuffer",
      // headers: {
      //   Authorization: "Bearer " + window.sessionStorage.getItem("omega_token"),
      //   "Content-Type": "application/json;charset=UTF-8 "
      // },
      timeout: timeout
    })
      .then(res => {
        // 判断响应头，如果响应头是json格式的说明有异常
        if (res.headers["content-type"]?.indexOf("application/json") > -1) {
          const blob = new Blob([res.data]);
          const reader = new FileReader();
          reader.readAsText(blob, "utf-8");
          reader.onload = function () {
            const resData = JSON.parse(reader.result || "{}");
            Message({
              message: resData?.msg || $T("导出Excel文件异常！"),
              showClose: true,
              type: "error"
            });
          };
          cb && cb(new Error($T("导出Excel文件异常！")));
          return;
        }
        const url = window.URL.createObjectURL(
          new Blob([res.data], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          })
        );
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;

        // 获取导出文件的名称
        const fileNameStr = res.headers["content-disposition"];
        var reg = /filename=(.*)/;
        var arr = reg.exec(fileNameStr);
        var fileName = "导出文件";
        if (arr && arr[1].trim()) {
          fileName = decodeURI(arr[1].trim());
        }
        link.setAttribute("download", name || fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        cb && cb();
      })
      .catch(function (error) {
        Message({
          message: $T("请检查网络是否连接正常"),
          showClose: true,
          type: "error"
        });
        cb && cb(error);
      });
  },
  formatTipText(time, cycle = 14) {
    // 年17，月14，日12
    if (!cycle) return time;
    let value = +time;
    if (cycle === 17) return moment(value).format("YYYY-MM");
    if (cycle === 14) return moment(value).format("YYYY-MM-DD");
    if (cycle === 12) return moment(value).format("YYYY-MM-DD HH") + $T(" 时");
  },
  fileNumArr(min, max) {
    let temp = [];
    for (let i = min; i <= max; i++) {
      temp.push(i);
    }
    return temp;
  },

  // 将对象数组 按照对象的某一个属性进行分类
  // 某一个对象没有此属性时 取 defaultValue
  groupByKey(objectArray, property, defaultValue = "$empty") {
    return objectArray.reduce(function (acc, obj) {
      var key = obj[property] || defaultValue;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(obj);
      return acc;
    }, {});
  },
  getEnumByName,
  getEnumTextById,
  clearChartData,

  getCurrentTimeByCycle,
  transConditions,
  deleteNode(vm, obj) {
    return new Promise((resolve, reject) => {
      let list = ["dcbase", "building", "floor", "room"];
      vm.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        type: "warning",
        distinguishCancelAndClose: true,
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        cancelButtonClass: "btn-custom-cancel"
      })
        .then(() => {
          if (list.includes(obj.modelLabel)) {
            customApi["deleteLevelNode"](obj).then(res => {
              resolve(res);
            });
          } else {
            customApi["deleteDevice"](obj).then(res => {
              resolve(res);
            });
          }
        })
        .catch(err => {
          reject(err);
        });
    });
  },
  // 获取班次列表
  queryShift(vm, obj) {
    return new Promise((resolve, reject) => {
      customApi["queryShift"]().then(res => {
        let result = res.data || [];
        let shifts = result.filter(item => {
          if (_.isEmpty(item.openEndTime)) {
            item.label = item.name;
          } else {
            item.label =
              item.name + " " + item.openEndTime[0] + "~" + item.openEndTime[1];
          }

          return item.delflag === 0;
        });
        resolve(shifts);
      });
    });
  },
  formatNum(...args) {
    return setNumFixed(...args);
  },
  addNumSymbol(num, precision = 0, unit = "") {
    if (num === 0) return "0";
    if (!num || _.isNaN(_.toNumber(num))) return "--";
    if (!precision && precision !== 0) return num.toLocaleString();
    let pow = Math.pow(10, precision);
    return (Math.round(num * pow) / pow).toLocaleString() + (unit ? unit : "");
  },
  alarmLevelColor(val) {
    let level = mhCONST("customalarmlevel").find(
      item => item.alarmlevel === val
    );
    if (level) {
      return level.colorsetid;
    } else {
      return "";
    }
  },
  alarmLevelStyle(val) {
    let level = mhCONST("customalarmlevel").find(
      item => item.alarmlevel === val
    );
    let colorsetid = _.get(level, "colorsetid", "");
    return {
      color: "#000",
      background: colorsetid,
      borderColor: colorsetid
    };
  },
  alarmLevelName(val) {
    let alarmLevel = mhCONST("customalarmlevel").find(
      item => item.alarmlevel === val
    );
    if (alarmLevel) {
      return alarmLevel.name;
    } else {
      return val;
    }
  },
  maintain(val) {
    if (val == 1) {
      return "一个月/次";
    } else if (val == 3) {
      return "三个月/次";
    } else if (val == 6) {
      return "六个月/次";
    } else if (val == 12) {
      return "一年/次";
    } else {
      return "--";
    }
  },
  // 将秒转化为xx小时xx分xx秒
  secondFormat(value) {
    const ONE_MINUTES_MILLISECONDS = 60 * 1000;
    const second = parseInt(value) * 1000;
    if (isNaN(second)) {
      return "--";
    }
    if (second === 0) {
      return 0;
    }
    if (second < ONE_MINUTES_MILLISECONDS) {
      return second / 1000 + $T("秒");
    }
    const days = moment.duration(second).days();
    const hours = moment.duration(second).hours();
    const minutes = moment.duration(second).minutes();
    const seconds = moment.duration(second).seconds();

    let timeArr = [];
    timeArr.push(days ? days + $T("天") : "");
    timeArr.push(hours ? hours + $T("小时") : "");
    timeArr.push(minutes ? minutes + $T("分") : "");
    timeArr.push(seconds ? seconds + $T("秒") : "");
    return timeArr.join("");
  },
  //适用于get请求
  downByGet(url, name, timeout = 60000, cb) {
    Axios.get(url, {
      // params: data,
      timeout,
      responseType: "arraybuffer"
    })
      .then(res => {
        // 判断响应头，如果响应头是json格式的说明有异常
        if (res.headers["content-type"].indexOf("application/json") > -1) {
          Message({
            message: $T("导出文件异常！"),
            showClose: true,
            type: "error"
          });
          cb && cb(new Error($T("导出文件异常！")));
          return;
        }

        const url = window.URL.createObjectURL(
          new Blob([res.data], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          })
        );
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;

        // 获取导出文件的名称

        link.setAttribute("download", name);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        cb && cb();
      })
      .catch(function (error) {
        Message({
          message: $T("请检查网络是否连接正常"),
          showClose: true,
          type: "error"
        });
        cb && cb(error);
      });
  }

  //导出pdf格式
  /* downPDF(url, data, name, timeout = 60000, cb) {
    let config = {
      responseType: "blob",
      timeout: timeout
    };
    if (window.sessionStorage.getItem("omega_token")) {
      config.headers = {
        Authorization: "Bearer " + window.sessionStorage.getItem("omega_token")
      };
    }
    Axios.post(url, data, config)
      .then(res => {
        // 判断响应头，如果响应头是json格式的说明有异常
        if (res.headers["content-type"].indexOf("application/json") > -1) {
          Message({
            message: $T("获取文件失败！"),
            showClose: true,
            type: "error"
          });
          cb && cb();
          return;
        }
        let reader = new FileReader();
        reader.readAsDataURL(res.data);
        // onload当读取操作成功完成时调用
        reader.onload = function (e) {
          let fileName = decodeURIComponent(
            res.headers["content-disposition"]
          ).split("=");
          fileName = fileName[fileName.length - 1];

          let a = document.createElement("a");
          a.target = "_blank";
          a.download = name || fileName;

          a.href = e.target.result;
          a.click();
          a.remove();
          cb && cb();
        };
      })
      .catch(err => {
        Message({
          message: $T("请检查网络是否连接正常"),
          showClose: true,
          type: "error"
        });
        cb && cb(err);
      });
  } */
};
