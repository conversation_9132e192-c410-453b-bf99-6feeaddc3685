<template>
  <div class="page">
    <el-card class="fullfilled">
      <div class="flex-column">
        <div class="card-title">
          <el-tooltip
            :content="projectData?.projectName"
            placement="top"
            effect="light"
          >
            <p class="text-ellipsis">{{ get(projectData, "projectName") }}</p>
          </el-tooltip>

          <el-dropdown
            placement="bottom-end"
            trigger="click"
            @command="handleCommand"
          >
            <i class="card-title-icon el-icon-more" />
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="edit">
                <span class="edit">编辑</span>
              </el-dropdown-item>
              <el-dropdown-item command="delete">
                <span class="delete">删除</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <span class="pt16" style="display: flex; gap: 0px">
          <span class="color-T3">场景类型：</span>
          <span class="color-T1">
            {{ formatProjectType(get(projectData, "projectType")) }}
          </span>
          <!-- <i class="pr5 el-icon-pie-chart" style="font-weight: bold" /> -->
        </span>
        <span class="pt8">
          <span class="color-T3">最后修改：</span>
          <span class="color-T1">
            {{ formatItemTime(get(projectData, "updatedAt")) }}
          </span>
          <!-- <i class="pr5 el-icon-pie-chart" style="font-weight: bold" /> -->
        </span>
        <span class="pt8">
          <span class="color-T3">创建时间：</span>
          <span class="color-T1">
            {{ formatItemTime(get(projectData, "createdAt")) }}
          </span>
          <!-- <i class="pr5 el-icon-date" style="font-weight: bold" /> -->
        </span>
      </div>
      <div class="fr mt16 mb16">
        <el-button size="mini" @click="handleCopyClick(projectData)">
          复制
        </el-button>
        <el-button
          type="primary"
          size="mini"
          @click="handleDetailClick(projectData)"
        >
          详情
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import common from "@/utils/common";
import { mhCONST } from "@/config/const";
import moment from "moment";
import customApi from "@/api/custom";

export default {
  name: "ProjectCard",
  components: {},
  props: {
    projectData: {
      type: Object
    },
    scenariosMap: {
      type: Object
    }
  },
  data() {
    return {};
  },
  watch: {},
  methods: {
    get(...arg) {
      return common.get(...arg);
    },
    // 下拉选择
    handleCommand(val) {
      if (val == "delete") {
        this.deleteProject();
      } else {
        this.editProject();
      }
    },
    // 编辑项目
    editProject() {
      this.$emit("editProject", this.projectData);
    },
    // 删除项目
    deleteProject() {
      let vm = this;
      vm.$confirm("确定要删除所选项目吗？", "删除项目确认", {
        type: "warning",
        distinguishCancelAndClose: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消"
      })
        .then(() => {
          customApi["deleteProject"](this.projectData?.projectId).then(res => {
            if (res && res.code == 0) {
              this.$message.success($T("删除成功"));
              vm.$emit("refreshList");
            }
          });
        })
        .catch(() => {});
    },
    // 详情
    handleDetailClick(val) {
      this.$emit("handleDetailClick", {
        ...val,
        typeName: this.formatProjectType(val.projectType)
      });
    },
    // 复制
    handleCopyClick(val) {
      customApi["copyProject"](val?.projectId).then(res => {
        if (res && res.code == 0) {
          this.$message.success($T("复制成功"));
          this.$emit("refreshList");
        }
      });
    },
    // 格式化时间
    formatItemTime(time) {
      return `${moment(time).format("YYYY-MM-DD HH:mm:ss")}`;
    },
    // 格式化场景类型
    formatProjectType(val) {
      return _.get(this.scenariosMap, val, "--");
    }
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
p {
  margin: 0 !important;
  font-weight: bold;
  @include font_color(T1);
}
span {
  font-size: 14px;
  // @include font_color(T2);
}
.edit {
  @include font_color(ZS, !important);
}

.delete {
  @include font_color(Sta3, !important);
}
.card-title {
  display: flex;
  justify-content: space-between; /* 左右两端对齐 */
  align-items: center; /* 垂直居中（可选） */
  i {
    transform: rotate(90deg);
  }
}

.card-title-icon {
  color: #989898;
  font-size: 18px;
  cursor: pointer;
}

.el-dropdown {
  i:hover {
    @include font_color(ZS);
  }
}
</style>
