<template>
  <div v-loading="loading">
    <!-- 弹窗组件 -->
    <CetDialog v-bind="CetDialog_pagedialog" v-on="CetDialog_pagedialog.event">
      <template v-slot:footer>
        <span>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
        </span>
      </template>
      <div class="content">
        <div class="middle pl8 pr8 mt8 mb8">
          <el-upload
            style="text-align: center"
            ref="upload"
            :action="uploadUrl"
            :headers="{}"
            :on-remove="handleRemove"
            :on-success="handleSuccess"
            :on-error="handleError"
            :on-change="handleChange"
            :before-upload="beforeUploadFile"
            :auto-upload="false"
            :file-list="fileList"
          >
            <el-button slot="trigger" size="small" type="primary">
              {{ $T("选取文件") }}
            </el-button>
            <div slot="tip" class="color-secondary mt5 fs14">
              ({{ $T("只能上传一个文件，且不超过20M") }})
            </div>
          </el-upload>
        </div>
      </div>
    </CetDialog>
  </div>
</template>
<script>
import { mhCONST } from "@/config/const";
import { getToken } from "@omega/http/getToken";
import { getPAContent } from "@/omega/http.js";
//
export default {
  name: "Export",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    currentNode: {
      type: Object
    },
    uploadUrl: {
      type: String
    }
  },
  data() {
    return {
      fileList: [],
      fileListflag: null,
      headers: { Authorization: "Bearer " },
      loading: false,
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("导入"),
        width: mhCONST("dialogSize.small"),
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确认"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.fileList = [];
      this.fileListflag = null;
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {},
    inputData_in(val) {}
  },
  methods: {
    CetDialog_pagedialog_openTrigger_out(val) {
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_preserve_statusTrigger_out(val) {
      this.submitUpload();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    submitUpload() {
      if (_.isEmpty(this.fileListflag))
        return this.$message($T("请上传一个文件"));
      this.$refs.upload.submit();
    },
    handleRemove(file, fileList) {
      this.fileListflag = fileList;
    },
    handleChange(file, fileList) {
      if (fileList.length > 1) {
        fileList.splice(0, 1);
      }
      this.fileListflag = fileList;
    },
    beforeUploadFile(file, fileList) {
      let fileName = file.name;
      let pos = fileName.lastIndexOf(".");
      let lastName = fileName.substring(pos, fileName.length);
      const isLt20M = file.size / 1024 / 1024 < 20;

      /* accept=".xlsx,.xls" */
      // if (
      //   lastName.toLowerCase() !== ".xlsx" &&
      //   lastName.toLowerCase() !== ".xls"
      // ) {
      //   this.$message.error($T("上传文件只能是 xlsx 和 xls 格式"));
      //   return false;
      // }

      if (!isLt20M) {
        this.$message.error($T("上传文件大小不能超过 20M"));
        return false;
      }
    },
    handleSuccess(res, file, fileList) {
      if (res.code === 0) {
        this.$emit("importSuccess");
        this.$message.success($T("导入成功"));
        this.CetDialog_pagedialog.closeTrigger_in = new Date().getTime();
      } else {
        this.$message.warning(res.msg);
        this.$refs.upload.clearFiles();
        this.fileList = [];
      }
    },
    handleError(err, file, fileList) {
      this.$message.error(_.get(JSON.parse(err.message), "message"));
      this.$refs.upload.clearFiles();
      this.fileList = [];
    },
    no() {}
  },
  mounted: function () {}
};
</script>
<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: column;
  // justify-content: center;
  align-items: center;
  .slot-wrap {
    display: flex;
  }
}
</style>
