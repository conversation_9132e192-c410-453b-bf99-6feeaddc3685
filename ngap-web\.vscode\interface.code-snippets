{
  // Place your V2.x 工作区 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  // "Print to console": {
  // 	"scope": "javascript,typescript",
  // 	"prefix": "log",
  // 	"body": [
  // 		"console.log('$1');",
  // 		"$2"
  // 	],
  // 	"description": "Log output to console"
  // }

  //cet-interface-template的代码片段
  "cet-interface-template": {
    "prefix": "cet-interface-template",
    "body": [
      "   <CetInterface                                   ",
      "    :data.sync=\"CetInterface_$1.data\"                      ",
      "    :dynamicInput.sync=\"CetInterface_$1.dynamicInput\"                      ",
      "    v-bind=\"CetInterface_$1\"                            ",
      "    v-on=\"CetInterface_${1:请输入组件唯一识别字符串}.event\"                   ",
      "   ></CetInterface>                            "
    ],
    "description": ""
  },
  //cet-interface-data的代码片段
  "cet-interface-data": {
    "prefix": "cet-interface-data",
    "body": [
      "  CetInterface_$1: {                                                ",
      "    queryMode: \"trigger\", //查询条件变化，立即查询                                                ",
      "    data: [],                                                          ",
      "    dataConfig: {                                                      ",
      "      queryFunc: \"\",                                                ",
      "      modelLabel: \"\",                                                ",
      "      dataIndex: [],                                                ",
      "      modelList: [],                                                ",
      "      filters: [], // { name: \"name_in\", operator: \"LIKE\", prop: \"name\" }",
      "      treeReturnEnable: false,                                                ",
      "      hasQueryNode: true,                                                ",
      "      hasQueryId: false                                                ",
      "    },                                                               ",
      "    queryNode_in: null,                                                ",
      "    queryId_in: -1,                                                                    ",
      "    queryTrigger_in: new Date().getTime(),                                                ",
      "    dynamicInput: {},                                                ",
      "    page_in: null, // exp:{ index: 1, limit: 20 }                                               ",
      "    //defaultSort: { prop: \"code\"  order: \"descending\" },                                   ",
      "    event: {                                                      ",
      "      result_out:this.CetInterface_$1_result_out,                                                ",
      "      finishTrigger_out: this.CetInterface_$1_finishTrigger_out,                                        ",
      "      failTrigger_out: this.CetInterface_$1_failTrigger_out,                                        ",
      "      totalNum_out: this.CetInterface_$1_totalNum_out                                        ",
      "    }                                                               ",
      "  },                                                        "
    ],
    "description": ""
  },

  "cet-interface-method": {
    "prefix": "cet-interface-method",
    "body": [
      "// ${1:设置组件唯一识别字段}展示文字输出                            ",

      "CetInterface_$1_finishTrigger_out(val) {                           ",
      "},                                                  ",
      "CetInterface_$1_failTrigger_out(val) {                   ",
      "},                                                  ",
      "CetInterface_$1_totalNum_out(val) {                ",
      "},                                                  ",
      "CetInterface_$1_result_out(val) {                  ",
      "},                                                      "
    ],
    "description": ""
  }
}
