import { guid } from "@omega/layout/utils/util";
import _ from "lodash";
import moment from "moment";
import omegaAuth from "@omega/auth";

// 判断是否在当前时间24小时内
function is24HourRange(time) {
  const end = moment().endOf("d").valueOf();
  const start = new Date().getTime() - 24 * 60 * 60 * 1000;
  return _.inRange(time, start, end);
}
function isMeet(val, condition) {
  if (_.isEmpty(condition)) return true;
  if (condition.includes(val)) return true;

  if (_.toLower(val).includes(_.toLower(condition))) return true;
  return false;
}

const collecter = {
  _items: [],
  async timer(fn) {
    if (this._start) {
      return;
    }
    this._start = true;

    while (true && this._start) {
      await new Promise(reslove => {
        setTimeout(() => {
          reslove();
        }, 1e3);
      });

      fn(this._items);
      this._items.length = 0;
      this._start = false;
    }
  },
  add(item, fn) {
    this._items.push(item);

    this.timer(fn);
  }
};

export default {
  namespaced: true,
  state: {
    // 是否暂停告警持续提示音
    replayAudio: true,
    // 消息计数器  只要有消息过来就会+1，监听此数据变化
    counter: 0,
    // 消息计数器,用于播放声音
    videosCount: 0,
    // 返回告警提示音
    returnAlarm: 0,
    //消息通知列表数据
    items: [],
    // 语音播报队列
    playList: [],
    // 视频消息列表
    videos: [],
    eventClasses: [],
    // U位状态刷新时间戳
    refreshUStatus: new Date().getTime(),
    databaseBack: [],
    // 报警声音状态
    alarmStatus: 0,
    // 1开始 2 进行中  3 结束
    databaseBackStatus: 1,
    serverChrony: []
  },
  getters: {
    total(state) {
      return state.items.length;
    },
    videoTotal(state) {
      return state.videos.length;
    },
    minEventClass(state) {
      return _.min(state.eventClasses.filter(item => item > 0));
    },
    filterItems(state) {
      return condition => {
        if (
          _.isEmpty(condition.keyWord) &&
          _.isEmpty(condition.eventClasses) &&
          _.isEmpty(condition.eventTypes)
        )
          return state.items;
        return _.filter(state.items, ele => {
          if (_.isEmpty(condition)) return true;
          // _.every([[ele.content, condition.keyWord],[ele.description.eventClass, condition.eventClasses],[ele.logType, condition.eventTypes]],isMeet.bind(null,...item))
          return (
            isMeet(ele.content, condition.keyWord) &&
            isMeet(ele.description.eventClass, condition.eventClasses) &&
            isMeet(ele.logType, condition.eventTypes)
          );
        });
      };
    }
  },
  mutations: {
    setItems(state, data) {
      state.items = data;
    },
    ADD_COUNT(state) {
      state.counter++;
    },
    ADD_ITEM(state, item) {
      collecter.add(item, items => {
        items.forEach(item => {
          state.items.unshift({
            id: guid("notice_"),
            ...JSON.parse(JSON.stringify(item))
          });
          state.eventClasses.unshift(item.description.eventClass);
          // 缓存数量达到100 后 去除不在最近24小时范围内的
          if (
            state.items.length >
            100 /* && is24HourRange(_.last(state.items).time) */
          ) {
            state.items.pop();
            state.eventClasses.pop();
          }
        });

        const userId = omegaAuth.user.getUserId();
        sessionStorage.setItem(
          "message_" + userId,
          JSON.stringify(state.items)
        );
        if ([2, 3].includes(_.get(item, "description.pushType"))) return;
        state.videosCount++;
      });
    },
    ADD_RETURN(state, item) {
      state.returnAlarm++;
    },
    ADD_PLAY(state, item) {
      state.playList.unshift({
        id: guid("play_"),
        ...JSON.parse(JSON.stringify(item))
      });
      if (state.playList.length > 1000) {
        state.playList.pop();
      }
    },
    REMOVE_ITEM(state, item) {
      const index = _.findIndex(state.items, {
        id: item.id
      });
      if (!_.isNil(index)) {
        state.items.splice(index, 1);
        state.eventClasses.splice(index, 1);
        sessionStorage.setItem("message", JSON.stringify(state.items));
      }
    },
    REMOVE_ITEMS(state, items) {
      let ids = items.map(item => item.id);
      for (let i = state.items.length - 1; i >= 0; i--) {
        if (ids.includes(state.items[i].id)) {
          state.items.splice(i, 1);
          state.eventClasses.splice(i, 1);
        }
      }
      sessionStorage.setItem("message", JSON.stringify(state.items));
    },
    CLEAR_ITEMS(state, item) {
      state.items = [];
      state.eventClasses = [];
      sessionStorage.removeItem("message");
    },
    CLEAR_PLAYLIST(state, item) {
      state.playList = [];
    },
    setDatabaseBackStatus(state, status) {
      state.databaseBackStatus = status;
    },
    ADD_VIDEO(state, item) {
      // let list = _.differenceBy(item.description, state.videos, "id");
      // current为true 是当前视频，显示标红
      state.videos.forEach(item => (item.current = false));
      let list = item.description;
      list.forEach(item => {
        // id相同的视频删除旧的
        let index = _.findIndex(state.videos, ["id", item.id]);
        if (index !== -1) {
          state.videos.splice(index, 1);
        }
        item.current = true;
      });
      state.videos.unshift(...list);
      // 缓存数量达到50
      if (state.videos.length > 50) {
        state.videos.pop();
      }
    },
    REMOVE_VIDEOS(state, items) {
      let ids = items.map(item => item.id);
      for (let i = state.videos.length - 1; i >= 0; i--) {
        if (ids.includes(state.videos[i].id)) {
          state.videos.splice(i, 1);
        }
      }
    },
    CLEAR_VIDEOS(state, item) {
      state.videos = [];
    },
    // 添加数据库备份时的消息
    addBackNotice(state, item) {
      state.databaseBack.push({
        id: guid("addBackNotice_"),
        ...JSON.parse(JSON.stringify(item))
      });
      if (item.logType === 101) state.databaseBackStatus = 3;
    },
    removeBackNotice(state, item) {
      state.databaseBack = [];
    },
    REFRESH_UStATUS(state, time) {
      state.refreshUStatus = time;
    },
    changeAlarmStatus(state, status) {
      state.alarmStatus = status;
    },
    changeReplayAudio(state, status) {
      state.replayAudio = status;
    },
    // 服务器校时配置
    addServerChronyMsg(state, item) {
      state.serverChrony.push({
        id: guid("addServerChrony_"),
        ...JSON.parse(JSON.stringify(item))
      });
    },
    removeServerChronyMsg(state) {
      state.serverChrony = [];
    }
  }
};
