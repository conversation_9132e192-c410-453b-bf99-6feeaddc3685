import _ from "lodash";
import moment from "moment";
import customCommon from "@/utils/customCommon.js";
//自定义格式化表格列数据的函数, 函数名不能重名, 配置column的formatter为函数名即可

const InvalidValue = [
  null,
  -2147483648,
  "NaN",
  "Infinity",
  Infinity,
  2147483648
];

function toFixed2(value, precision) {
  precision = precision || 0;
  var pow = Math.pow(10, precision);
  return (Math.round(value * pow) / pow).toFixed(precision);
}

function formatterDate(formatStr = "YYYY-MM-DD HH:mm:ss") {
  return function (row, column, cellValue, index) {
    //设置时间格式化字符串，如果配置有效则采用配置字符串，无效则采用默认格式化字符串
    if (cellValue) {
      return moment(cellValue).format(formatStr);
    } else if (cellValue === 0 || cellValue === "") {
      return cellValue;
    } else {
      return "--";
    }
  };
}

function checkStringLength(length = 40) {
  return {
    min: 1,
    max: length,
    message: $T("长度在 1 到 {0} 个字符", length),
    trigger: ["blur", "change"]
  };
}

//格式化数字列
function formatNumber(precision = 2) {
  return function (row, column, cellValue, index) {
    if (cellValue) {
      if (!_.isNumber(cellValue)) {
        //先转换成数字
        cellValue = parseFloat(cellValue);
      }

      return cellValue.toFixed2(precision); //保留两位小数
    } else if (cellValue === 0) {
      return cellValue;
    } else {
      return "--";
    }
  };
}

function resetApiFuncName(funObj, prefix = "epms_") {
  let result = {};
  for (const key in funObj) {
    if (Object.hasOwnProperty.call(funObj, key)) {
      result[prefix + key] = funObj[key];
    }
  }
  return result;
}

export default {
  resetApiFuncName,
  /***表格列自定义formatter函数 ***/
  formatDateColumn: formatterDate(), // 格式化日期格式数据
  formatNumberColumn: formatNumber(), //
  formatDateCol: formatterDate, //柯里化格式化表格时间列, 可以传入自定义时间格式字符串
  formatNumberCol: formatNumber, //柯里化格式化表格数值列, 可以传入保留的小数位
  /****************************/

  ac: function (store, funcStr) {
    var funcList = store.state.initialInfo.functionList;
    for (var i = 0, len = funcList.length; i < len; i++) {
      if (funcList[i].id === funcStr) {
        return true;
      }
    }
    return false;
  },
  formatNumber: function (config, value) {
    let common = this;
    if (_.has(config, "precision") && _.isNumber(config.precision)) {
      value = common.formatNumberWithPrecision(value, config.precision);
    }

    return value;
  },
  formatNumberWithPrecision: function (value, precision) {
    if (_.isNumber(precision)) {
      if (!_.isNumber(value)) {
        //先转换成数字
        value = parseFloat(value);
      }
      if (isNaN(value)) {
        //如果为空直接返回空
        return null;
      }
      value = toFixed2(value, precision); //不为空的话就保留小数位
    }

    return value;
  },
  formatBoolean: function (config, value, vm) {
    if (_.has(config, "trueText") && _.has(config, "falseText")) {
      value = value ? config.trueText : config.falseText;
    }
    return value;
  },
  check_stringLessThan140: {
    min: 1,
    max: 140,
    message: $T("长度在 1 到 140 个字符"),
    trigger: ["blur", "change"]
  },
  check_name: checkStringLength(20),
  checkStringLength,
  check_strongPassword: {
    pattern:
      /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[`~!@$%^&#*()+=\[\]{}\\|;:'"<,>.?/]).{8,18}$/,
    trigger: ["blur", "change"],
    message: $T("密码需含有大写、小写字母、数字和特殊字符，且长度为8~18位")
  },
  pattern_name: {
    // pattern: /^[a-z]+$/,
    // pattern: /((?=[\x21-\x7e]+)[^A-Za-z0-9])/,
    // pattern: /^((?!`~!@#$%^&*()_+-=[]{}\|;:\'"<,>.?\/).)*$/,
    pattern: /^((?![`~!@$%^&*()+=\[\]{}\\|;:\'"<,>.?\/]).)*$/, //如果把-加进去就表示数字也算特殊字符了，所以-不能加进去
    message: $T("请不要输入特殊字符"),
    trigger: ["blur", "change"]
  },
  check_phone: {
    pattern: /^1[3-9][0-9]\d{8}$/,
    trigger: ["blur", "change"],
    message: $T("请输入正确的手机号")
  },

  check_telephone: {
    pattern: /^((0\d{2,3})-)(\d{7,8})(-(\d{3,}))?$/,
    trigger: ["blur", "change"],
    message: $T("请输入正确的固定电话 例：010-88888888-123")
  },
  check_phone_or_telephone: {
    pattern:
      /(^((0\d{2,3})-)(\d{7,8})(-(\d{3,}))?$)|(^1[3|4|5|7|8][0-9]\d{8}$)/,
    trigger: ["blur", "change"],
    message: $T("请输入正确的手机号或固定电话")
  },
  pickerOptions_laterThanTodayEarlierThan28: {
    disabledDate(time) {
      return time.getTime() <= Date.now() || time.getDate() > 28;
    }
  },
  pickerOptions_laterThanYesterday: {
    disabledDate(time) {
      return time.getTime() <= moment(Date.now()).subtract(1, "days");
    }
  },
  pickerOptions_earlierThanTomorrow: {
    disabledDate(time) {
      return time.getTime() > Date.now();
    }
  },
  check_select: function (val) {
    return {
      required: true,
      message: $T("请选择") + val,
      trigger: "blur"
    };
  },
  checkMessageExceed60: function (val) {
    return val && val.length > 60 ? "#E6A23C" : "transparent";
  },
  //获取所有用户
  getUserData(vm) {
    let common = this;
    // 获取用户所在的用户组信息;
    $.ajax(`/auth/v1/usergroup/${vm.userInfo.rootUserGroupID}`, {
      type: "GET",
      headers: {
        Authorization: "Bearer " + window.sessionStorage.getItem("omega_token")
      }
    }).then(function (response) {
      if (response.code === 0) {
        let root = response.data;
        var levelOrder = [common.expandNodeM(root)];
        vm.CetTree_Left.inputData_in = levelOrder;
        //
        // vm.CetTree_authTree.inputData_in = root.auths;
      }
    });
  },
  expandNodeM: function (root) {
    //递归
    var res = root;

    function bfs(tree, idx) {
      if (!tree) return;
      // if (!Array.isArray(tree[idx])) {
      //   tree[idx] = [];
      // }
      tree.name = tree.nodeName;
      tree.id = tree.nodeID;
      tree.modelLabel = tree.nodeType;
      tree.tree_id = tree.nodeType + "_" + tree.id;

      if (!Array.isArray(tree.children)) {
        return;
      }

      if (tree.children.length > 0) {
        for (var i = 0; i < tree.children.length; i++) {
          bfs(tree.children[i], idx + 1);
        }
      }
    }
    bfs(root, 0);
    return res;
  },
  total: function (list, dropItemText) {
    var money = 0;
    for (var i = 0; i < list.length; i++) {
      money = money + list[i][dropItemText];
    }
    return money;
  },
  download: function (vm) {
    vm.$confirm($T("您未安装lodop套打软件，请问是否下载安装？"), $T("提示"), {
      confirmButtonText: $T("下载"),
      cancelButtonText: $T("取消"),
      type: "warning",
      closeOnClickModal: false,
      showClose: false,
      cancelButtonClass: "btn-custom-cancel",
      beforeClose: function (action, instance, done) {
        if (action == "confirm") {
          window.open("static/DownLoad/CLodop打印工具.zip");
        } else {
          vm.$message({
            type: "info",
            message: $T("取消操作！")
          });
        }
        instance.confirmButtonLoading = false;
        done();
      }
    });
  },
  exportFilename(table) {
    return `${table.dynamicInput.name_in}工单列表`;
  },
  // 通过key查找值,未找到时输出defaultValue
  findDataByKey(object, path, defaultValue = "--") {
    let val = _.get(object, path, defaultValue);
    if (InvalidValue.includes(val)) {
      return defaultValue;
    }
    return val;
  },
  formatNumber1(val, precision = 0, placeholder = "--") {
    if (!_.isNumber(val)) {
      //先转换成数字
      val = parseFloat(val);
    }
    if (isNaN(val)) {
      return placeholder;
    }

    return val.toFixed(precision); //不为空的话就保留小数位
  },
  // 将数字四舍五入,输出数字
  roundNumber(val, precision = 2, defaultValue = undefined) {
    if (InvalidValue.includes(val)) {
      return defaultValue;
    }
    let num = _.round(val, precision);
    return _.isNaN(num) ? defaultValue : num;
  },
  ...customCommon
};
