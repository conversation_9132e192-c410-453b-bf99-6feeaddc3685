<template>
  <div class="page">
    <div class="text-center tip lh50">
      <slot>{{ tip }}</slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "emptyTip",
  components: {},
  // props: {
  //   title: {
  //     default: "",
  //     type: String
  //   }
  // },
  computed: {
    tip() {
      return this.$route.meta.tip || "暂无数据";
    }
  },
  data() {
    return {};
  },
  watch: {},
  methods: {}
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}
.tip {
  font-size: 14px;
  line-height: 60px;
  color: #74788d;
}
</style>
