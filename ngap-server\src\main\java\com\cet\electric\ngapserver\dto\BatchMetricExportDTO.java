package com.cet.electric.ngapserver.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "指标数据批量导出参数")
public class BatchMetricExportDTO {
    @ApiModelProperty(value = "图片")
    private String pic;

    @ApiModelProperty(value = "指标ID列表", required = true, example = "[\"cpu_usage\", \"memory_usage\", \"network_io\"]")
    private List<String> metricIds;

    @ApiModelProperty(value = "起始时间戳", required = true, example = "1625097600000")
    private Long startTimestamp;
}
