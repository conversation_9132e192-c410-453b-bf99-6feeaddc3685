@import "@omega/theme/tailwind.var.css";

[data-theme="blue"] {
  /* 主色 */
  --ZS: #5664d2;
  /* 辅助色1 */
  --F1: #4451d1;
  /* 辅助色2 */
  --F2: #d9294f;
  /* 辅助色3 */
  --F3: #eaf7ef;
  /* 成功 */
  --Sta1: #1cbb8c;
  /* 警告 */
  --Sta2: #fcb92c;
  /* 危险 */
  --Sta3: #ff3d60;
  /* 一般 */
  --Sta4: #1890ff;
  /* 次要 */
  --Sta5: #722ed1;
  /* 状态 */
  --Sta6: #eef0f2;
  /* >>>> 文字色 */
  /* 主要 */
  --T1: #343a40;
  /* 常规 */
  --T2: #555f65;
  /* 次要 */
  --T3: #74788d;
  /* 占位 */
  --T4: #cccccc;
  /* 带背景的文字色 */
  --T5: #ffffff;
  /* >>>> 边框色 */
  /* 主要 */
  --B1: #dcdee2;
  /* 次要 */
  --B2: #ebeef5;
  /* >>>> 背景色 */
  --BG: #f1f5f7;
  /* 主要 */
  --BG1: #ffffff;
  /* 滑入 */
  --BG2: #ebeef5;
  /* 点击 */
  --BG3: #dcdee2;
  /* 选中 */
  --BG4: #eef0fb;
  --SCR: rgba(85, 95, 101, 0.3);
}

[data-theme="darkblue"] {
  /* 主色 */
  --ZS: #1087ff;
  /* 辅助色1 */
  --F1: #106fe0;
  /* 辅助色2 */
  --F2: #d83603;
  /* 辅助色3 */
  --F3: #eaf7ef;
  /* 成功 */
  --Sta1: #7fff7d;
  /* 警告 */
  --Sta2: #ff8c00;
  /* 危险 */
  --Sta3: #f95e5a;
  /* 一般 */
  --Sta4: #00ced1;
  /* 次要 */
  --Sta5: #1e90ff;
  /* 状态 */
  --Sta6: #eef0f2;
  /* >>>> 文字色 */
  /* 主要 */
  --T1: #fafafa;
  /* 常规 */
  --T2: #f6f6f6;
  /* 次要 */
  --T3: #efefef;
  /* 占位 */
  --T4: #cdcfdc;
  /* 带背景的文字色 */
  --T5: #ffffff;
  /* >>>> 边框色 */
  /* 主要 */
  --B1: #456cdd;
  /* 次要 */
  --B2: #2f42ab;
  /* >>>> 背景色 */
  --BG: #0d1b64;
  /* 主要 */
  --BG1: #1f3087;
  /* 滑入 */
  --BG2: #324398;
  /* 点击 */
  --BG3: #4967b1;
  /* 选中 */
  --BG4: #4670d0;
  --SCR: rgba(246, 246, 246, 0.3);
}

[data-theme="dark"] {
  --ZS: #0d86ff;
  --F1: #1375da;
  --F2: #d53c40;
  --Sta1: #0dff86;
  --Sta2: #ff782b;
  --Sta3: #ff3f3f;
  --Sta4: #15e3e3;
  --Sta5: #19c6c6;
  --Sta6: #eef0f2;
  --T1: #f0f1f2;
  --T2: #e6e8ea;
  --T3: #ced2d6;
  --T4: #b1b5b8;
  --T5: #ffffff;
  --B1: #3d4251;
  --B2: #3a506b;
  --BG: #212635;
  --BG1: #2b3244;
  --BG2: #363d50;
  --BG3: #586073;
  --BG4: #283a57;
  --SCR: rgba(230, 232, 234, 0.3);
}

[data-theme="black"] {
  --ZS: #0d86ff;
  --F1: #1375da;
  --F2: #d53c40;
  --Sta1: #0dff86;
  --Sta2: #ff782b;
  --Sta3: #ff3f3f;
  --Sta4: #15e3e3;
  --Sta5: #19c6c6;
  --Sta6: #eef0f2;
  --T1: #f0f1f2;
  --T2: #e6e8ea;
  --T3: #ced2d6;
  --T4: #3a506b;
  --T5: #ffffff;
  --B1: #3d4251;
  --B2: #3a506b;
  --BG: #000;
  --BG1: #111;
  --BG2: #222;
  --BG3: #333;
  --BG4: #444;
  --SCR: rgba(230, 232, 234, 0.3);
}

/* @layer components {
  .btnCy {
    @apply ml-J2 rounded-full;
    @apply bg-ZS !important;
  }
  .btn {
    @apply px-4 py-2 rounded-full bg-gray-400;
  }
} */
