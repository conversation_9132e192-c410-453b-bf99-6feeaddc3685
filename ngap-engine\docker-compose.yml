version: '3.8'

services:
  ngap-engine:
    image: *************/base/ngap-engine:1.0.0-SNAPSHOT
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${ngap_engine_ip}
        aliases:
          - ngap-engine
    ports:
      - 18008:5050
    hostname: ngap-engine
    restart: always
    privileged: true
    volumes:
      - /var/ngap/data:/data
    environment:
      TZ: Asia/Shanghai
