<template>
  <div>
    <CetDialog
      :title="title"
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
    >
      <template v-slot:footer>
        <span>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
        </span>
      </template>

      <div class="custom-form">
        <el-form :model="formData" :rules="rules" ref="form" style="width: 65%">
          <el-form-item
            class="custom-form-item gray-label mb30"
            label="项目名称"
            prop="projectName"
            for="projectName"
          >
            <el-input
              id="projectName"
              style="width: 250px"
              v-model="formData.projectName"
              placeholder="请输入项目名称"
            />
          </el-form-item>
          <el-form-item
            class="custom-form-item gray-label"
            label="场景类型"
            prop="projectType"
            for="projectType"
          >
            <ElSelect
              id="projectType"
              :disabled="isEdit"
              v-model="formData.projectType"
              v-bind="ElSelect_projectType"
              v-on="ElSelect_projectType.event"
            >
              <ElOption
                v-for="item in ElOption_projectType.options_in"
                :key="item[ElOption_projectType.key]"
                :label="item[ElOption_projectType.label]"
                :value="item[ElOption_projectType.value]"
                :disabled="item[ElOption_projectType.disabled]"
              ></ElOption>
            </ElSelect>
          </el-form-item>
        </el-form>
      </div>
    </CetDialog>
  </div>
</template>
<script>
import common from "@/utils/common";
import { mhCONST } from "@/config/const";
import customApi from "@/api/custom";

export default {
  name: "NewOrEditProjectDialog",
  components: {},
  computed: {
    title() {
      return this.isEdit ? "编辑项目" : "新建项目";
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    isEdit: {
      type: Boolean
    },
    scenariosOPtion: {
      type: Array
    }
  },
  data() {
    return {
      formData: {
        projectName: "",
        projectType: null
      },
      rules: {
        projectName: [
          {
            required: true,
            message: "名称不能为空，且不能带有特殊符号。",
            trigger: ["blur", "change"]
          },
          {
            required: true,
            message: "名称不能为空，且不能带有特殊符号。",
            pattern: /^((?![`~!@$%^&*()_+=\[\]{}\\|;:\'"<,>.?\/]).)*$/
          }
        ],
        projectType: [
          {
            required: true,
            message: "请选择场景类型",
            trigger: ["blur", "change"]
          }
        ]
      },
      // projectType组件
      ElSelect_projectType: {
        style: {
          width: "250px"
        },
        event: {}
      },
      // projectType组件
      ElOption_projectType: {
        options_in: [],
        key: "value",
        value: "value",
        label: "name",
        disabled: "disabled"
      },
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        width: "600px",
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },

      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: "确定",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.ElOption_projectType.options_in = this.scenariosOPtion || [];

      if (this.$refs["form"] && !this.isEdit) {
        this.formData = {
          projectName: "",
          projectType: null
        };
      }

      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {},
    inputData_in(val) {
      if (this.isEdit) {
        this.formData = {
          projectName: val.projectName,
          projectType: val.projectType
        };
      }
    }
  },
  methods: {
    CetDialog_pagedialog_openTrigger_out(val) {
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    async CetButton_preserve_statusTrigger_out(val) {
      const { form } = this.$refs;
      await form.validate();
      let queryFunc = this.isEdit ? "putProjectRename" : "createProject";
      let params = this.isEdit
        ? {
            projectId: this.inputData_in.projectId,
            projectName: this.formData.projectName
          }
        : this.formData;
      customApi[queryFunc](params).then(res => {
        if (res && res.code == 0) {
          this.$message.success($T("保存成功"));
          this.$emit("saveData_out", new Date().getTime());
          this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
        }
      });
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    no() {}
  },
  created: function () {}
};
</script>
<style lang="scss" scoped></style>
