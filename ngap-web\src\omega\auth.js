import omegaApp from "@omega/app";
import { OmegaAuthPlugin } from "@omega/auth";

omegaApp.plugin.register(OmegaAuthPlugin, {
  defaultHomepage: "/projectmanage",
  // isTokenPersist: false,
  openPermissionCheck: false,
  // whiteRouteList: [],
  // openHomepage: true,
  openPagePermission: false,
  isUseSuperAdminRole: true
  // apiPrefix: {
  //   "auth-service": "/auth"
  // },
  // apiRequestInterceptor: function(config) {return config}
});
