<template>
  <el-popover
    placement="bottom-end"
    trigger="click"
    width="400"
    v-model="visible"
  >
    <el-badge :hidden="!total" :value="total" :max="99" slot="reference">
      <omega-icon
        class="icon-hover-normal"
        symbolId="message-lin"
        v-if="false"
      />
    </el-badge>
    <div class="layout-notice">
      <div class="layout-notice-header">
        <span class="title">
          {{ $T("消息") }}
          <!-- <VolumeCtrl ref="volumeCtrl" class="ml10" /> -->
        </span>
        <span class="total">
          {{ $T("共 $0 条", total) }}
        </span>
      </div>
      <div class="layout-notice-main">
        <div class="layout-notice-empty">{{ $T("暂无数据") }}</div>
        <transition-group name="el-zoom-in-top">
          <!-- <NoticeItem
            class="layout-notice-item"
            v-for="(item, index) in items"
            :key="item.id || index"
            :item="item"
            @removeItem="removeItem"
          /> -->
        </transition-group>
      </div>
      <div class="layout-notice-footer">
        <el-link :underline="false" type="primary" @click="clearItems">
          {{ $T("全部已读") }}
        </el-link>
        <el-link type="primary" @click="evViewAllClick">
          {{ $T("查看全部") }}
        </el-link>
      </div>
    </div>
  </el-popover>
</template>

<script>
import { WebSocketConnectClient } from "./modules/WebSocketClient";
import { http } from "@omega/http";
import omegaAuth from "@omega/auth";
// import NoticeItem from "./NoticeItem";
import _ from "lodash";

const client = new WebSocketConnectClient();

export default {
  name: "LayoutNotice",
  components: {
    // NoticeItem,
  },
  data() {
    return {
      items: [],
      visible: false
    };
  },
  created() {
    // const userId = omegaAuth.user.getUserId();
    // client.connect({
    //   url: `ws://${window.location.host}/messageServer/websocket/${userId}`
    // });
    // client.on("open", () => {
    //   http({
    //     url: `/messageServer/v1/service/web/client`,
    //     method: "POST",
    //     data: {
    //       // 用户登录ID
    //       userId: userId,
    //       // 用户标签，用户给一组用户发送信息时用
    //       // 一个用户对应多个标签用“,”隔开，如巡检员,预试员,业主,项目1
    //       tags: [`${userId}`]
    //     }
    //   });
    // });
    // client.on("message", msg => {
    //   // this.$refs.volumeCtrl.rePlay();
    //   this.addItem(msg);
    // });
  },
  computed: {
    total() {
      return this.items.length;
    }
  },
  methods: {
    addItem(item) {
      // this.items.unshift({
      //   id: _.uniqueId("notice_"),
      //   ...item
      // });
      // // 最大条目限制为100条
      // if (this.items.length > 99) {
      //   this.items.pop();
      // }
    },
    removeItem(item) {
      const index = _.findIndex(this.items, {
        id: item.id
      });
      this.items.splice(index, 1);
    },
    clearItems() {
      this.items = [];
    },
    evViewAllClick() {}
  },
  beforeDestroy() {
    client.close();
  }
};
</script>

<style lang="scss" scoped>
.layout-notice {
  margin: -12px;
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    box-sizing: border-box;
    @include padding(0 J3);
    @include font_color(T1);
    border-bottom: 1px solid;
    @include border_direction_color(B2, "bottom");
    & .title {
      @include font_size(H3);
    }
    & .total {
      @include font_size(Ab);
    }
  }
  &-main {
    min-height: 300px;
    max-height: 600px;
    overflow: auto;
    @include padding(0 J3);
    & .layout-notice-item:not(:last-child) {
      border-bottom: 1px solid;
      @include border_direction_color(B2, "bottom");
    }
  }
  &-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    box-sizing: border-box;
    @include padding(0 J3);
    @include font_size(Aa);
    // @include font_color(ZS);
    border-top: 1px solid;
    @include border_direction_color(B2, "top");
  }
}
::v-deep {
  .el-badge__content {
    z-index: 1;
  }
}
.layout-notice-empty {
  text-align: center;
  margin: 24px;
}
</style>
