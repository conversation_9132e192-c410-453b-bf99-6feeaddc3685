<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cet.electric.ngapserver.dao.DeviceDao">
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.cet.electric.ngapserver.entity.Device">
        <id column="parameter_id" property="parameterId" jdbcType="BIGINT"/>
        <result column="device_type" property="deviceType" jdbcType="VARCHAR"/>
        <result column="parameter_code" property="parameterCode" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        parameter_id, device_type, parameter_code, version
    </sql>

    <!-- 插入新设备 -->
    <insert id="createDevice" parameterType="com.cet.electric.ngapserver.entity.Device" useGeneratedKeys="true" keyProperty="parameterId">
        INSERT INTO device (
            device_type,
            parameter_code,
            version
        ) VALUES (
            #{deviceType},
            #{parameterCode},
            #{version}
        )
    </insert>

    <sql id="Device_Where_Clause">
        <where>
            <if test="keyword != null and keyword != ''">
                AND (
                    device_type LIKE '%' || #{keyword} || '%'
                    OR parameter_code LIKE '%' || #{keyword} || '%'
                    OR version LIKE '%' || #{keyword} || '%'
                )
            </if>
        </where>
    </sql>

    <select id="getDevices" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM device
        <include refid="Device_Where_Clause"/>
        ORDER BY
        <choose>
            <when test="sortBy != null and sortBy == 'deviceType'">device_type</when>
            <when test="sortBy != null and sortBy == 'parameterCode'">parameter_code</when>
            <when test="sortBy != null and sortBy == 'version'">version</when>
            <otherwise>parameter_id</otherwise>
        </choose>
        <choose>
            <when test="sortOrder != null and sortOrder.equalsIgnoreCase('asc')">ASC</when>
            <otherwise>DESC</otherwise>
        </choose>
        LIMIT #{offset}, #{size}
    </select>

    <select id="countDevices" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM device
        <include refid="Device_Where_Clause"/>
    </select>

    <select id="findByParameterCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List" />
        FROM device
        WHERE parameter_code = #{parameterCode}
        LIMIT 1
    </select>

    <select id="findById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List" />
        FROM device
        WHERE parameter_id = #{parameterId}
        LIMIT 1
    </select>

    <update id="updateDevice" parameterType="com.cet.electric.ngapserver.entity.Device">
        UPDATE device
        SET
            device_type = #{deviceType},
            parameter_code = #{parameterCode},
            version = #{version}
        WHERE
            parameter_id = #{parameterId}
    </update>

    <delete id="deleteDevice" parameterType="java.lang.Long">
        DELETE FROM device
        WHERE parameter_id = #{parameterId}
    </delete>
</mapper>
