package com.cet.electric.ngapserver.entity;

import com.cet.electric.ngapserver.enums.RunStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 仿真任务实体类
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Simulation {
    /**
     * 仿真ID：主键，自增
     */
    private Long simulationId;

    /**
     * 关联的项目ID：外键
     */
    private Long projectId;

    /**
     * 关联的项目名称：外键
     */
    private String projectName;

    /**
     * 仿真名称：非空
     */
    private String simulationName;

    /**
     * 仿真模型
     */
    private String simulationModel;

    /**
     * 仿真模型草稿
     */
    private String simulationDraft;

    /**
     * 输入数据
     */
    private String inputData;

    /**
     * 输出数据
     */
    private String outputData;

    /**
     * 实测数据
     */
    private String measuredData;

    /**
     * 仿真脚本
     */
    private String simulationScript;

    /**
     * 并网点
     */
    private String nodes;

    /**
     * 控制策略
     */
    private String controlStrategy;

    /**
     * 运行状态
     */
    private RunStatus runStatus;

    /**
     * 创建时间：默认当前时间
     */
    private Long createdAt;

    /**
     * 修改时间：自动更新
     */
    private Long updatedAt;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer isDeleted;
}
