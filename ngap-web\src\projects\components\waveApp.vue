<template>
  <div style="height: 100%">
    <cetWaveform :comtradeData="comtradeData" :title="data.title" :langCode="langCode"></cetWaveform>
  </div>
</template>
<script>
import common from "@/utils/common";
import cetWaveform from "cet-waveform";
import customApi from "@/api/custom";
import omegaI18n from "@omega/i18n";

export default {
  name: "waveApp",
  components: { cetWaveform },
  props: {
    data: {
      type: [Object],
      default: () => ({})
    },
    title: {
      type: String
    }
  },
  computed: {},
  watch: {
    //外部输入数据
    data: {
      deep: true,
      immediate: true,
      handler: function (val) {
        if (this._.isEmpty(val)) return;
        this.getWaveData();
      }
    }
  },
  mounted() {},
  data() {
    return {
      comtradeData: [],
      langCode: omegaI18n.locale == "en" ? "EN" : "CN"
    };
  },
  methods: {
    getWaveData() {
      let me = this;
      if (!me.data.deviceId || !me.data.waveTime) {
        me.comtradeData = [];
        return;
      }
      customApi.getWaveData(me.data).then(res => {
        me.comtradeData = common.get(res, "data", []);
      });
    }
  }
};
</script>
<style lang="scss" scope></style>
