package com.cet.electric.ngapserver.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "仿真任务查询参数")
public class SimulationQueryDTO {
    @ApiModelProperty(value = "页码", example = "1")
    private Integer page = 1;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer size = 10;

    @ApiModelProperty(value = "排序字段", example = "created_at")
    private String sortBy = "created_at";

    @ApiModelProperty(value = "排序方式", example = "desc")
    private String sortOrder = "desc";

    @ApiModelProperty(value = "项目ID", required = true, example = "1")
    private Long projectId;

    @ApiModelProperty(value = "关键字搜索")
    private String keyword;

    @ApiModelProperty(value = "起始时间(时间戳-毫秒)", example = "1735660800000")
    private Long startTime;

    @ApiModelProperty(value = "结束时间(时间戳-毫秒)", example = "1767196799000")
    private Long endTime;
}
