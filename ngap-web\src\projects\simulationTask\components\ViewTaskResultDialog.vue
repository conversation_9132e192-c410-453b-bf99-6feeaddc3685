<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog v-bind="CetDialog_pagedialog" v-on="CetDialog_pagedialog.event">
      <template v-slot:footer>
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
        </span>
      </template>
      <ViewTaskResult
        :inputData_in="inputData_in"
        style="height: 600px"
      ></ViewTaskResult>
    </CetDialog>
  </div>
</template>
<script>
import common from "@/utils/common";
import ViewTaskResult from "./ViewTaskResult.vue";
import { mhCONST } from "@/config/const";

export default {
  name: "ViewTaskResultDialog",
  components: { ViewTaskResult },
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: "运行结果",
        width: mhCONST("dialogSize.large"),
        top: "5vh",
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },

      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {},
    inputData_in(val) {}
  },
  methods: {
    CetDialog_pagedialog_openTrigger_out(val) {
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    no() {}
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 20px 20px 0;
}
</style>
