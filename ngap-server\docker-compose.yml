version: '3'
services:
  ngap-server:
    image: *************/base/ngap-server:1.0.10-SNAPSHOT
    ports:
      - 18009:28080
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${ngap_server_ip}
        aliases:
          - ngap-server
    hostname: ngap-server
    restart: always
    privileged: true
    environment:
      TZ: Asia/Shanghai
      DATABASE_URL: *****************************
      DSS_URL: http://localhost:5000
    volumes:
      - /var/ngap/database:/database
      - /var/ngap/logs:/logs
      - /var/ngap/data:/data

networks:
  eureka-net:
    driver: bridge
    ipam:
      driver: default
      config:
      - subnet: ${master_network_segment}.0/24

