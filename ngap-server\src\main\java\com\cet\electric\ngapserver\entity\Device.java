package com.cet.electric.ngapserver.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 设备实体类
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Device {
    /**
     * 参数ID：主键，自增
     */
    private Long parameterId;

    /**
     * 设备类型：非空
     */
    private String deviceType;

    /**
     * 参数代码：非空
     */
    private String parameterCode;

    /**
     * 版本：系统自动管理，新建时默认为1.0
     */
    private String version;
}
