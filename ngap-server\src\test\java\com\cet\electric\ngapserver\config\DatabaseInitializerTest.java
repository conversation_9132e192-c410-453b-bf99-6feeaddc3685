package com.cet.electric.ngapserver.config;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.ApplicationArguments;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * DatabaseInitializer 单元测试
 * 
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class DatabaseInitializerTest {
    
    @InjectMocks
    private DatabaseInitializer databaseInitializer;
    
    private ApplicationArguments mockArgs;
    private static final String TEST_DB_URL = "*******************************";
    private static final String TEST_DRIVER = "org.sqlite.JDBC";
    
    private File testDbFile;
    private File testDataDir;
    
    @Before
    public void setUp() {
        // 设置测试属性
        ReflectionTestUtils.setField(databaseInitializer, "databaseUrl", TEST_DB_URL);
        ReflectionTestUtils.setField(databaseInitializer, "driverClassName", TEST_DRIVER);
        
        // 创建mock对象
        mockArgs = mock(ApplicationArguments.class);
        
        // 创建测试目录和文件引用
        testDataDir = new File("./test_data");
        testDbFile = new File("./test_data/test.db");
        
        // 清理可能存在的测试文件
        cleanupTestFiles();
    }
    
    @After
    public void tearDown() {
        // 清理测试文件
        cleanupTestFiles();
    }
    
    private void cleanupTestFiles() {
        if (testDbFile.exists()) {
            testDbFile.delete();
        }
        if (testDataDir.exists()) {
            testDataDir.delete();
        }
    }
    
    @Test
    public void testRun_IntegrationTest_WithNewDatabase() throws Exception {
        // 确保数据库文件不存在
        cleanupTestFiles();

        // 执行测试
        databaseInitializer.run(mockArgs);

        // 验证数据库文件是否创建
        assertTrue("数据库文件应该被创建", testDbFile.exists());

        // 验证表结构
        try (Connection conn = DriverManager.getConnection(TEST_DB_URL)) {
            assertTrue("project表应该存在", isTableExists(conn, "project"));
            assertTrue("simulation表应该存在", isTableExists(conn, "simulation"));
            assertTrue("device表应该存在", isTableExists(conn, "device"));
        }
    }

    @Test
    public void testRun_IntegrationTest_WithExistingCompleteDatabase() throws Exception {
        // 先创建完整的数据库
        testDataDir.mkdirs();
        try (Connection conn = DriverManager.getConnection(TEST_DB_URL)) {
            conn.createStatement().execute("CREATE TABLE project (project_id INTEGER PRIMARY KEY)");
            conn.createStatement().execute("CREATE TABLE simulation (simulation_id INTEGER PRIMARY KEY)");
            conn.createStatement().execute("CREATE TABLE device (parameter_id INTEGER PRIMARY KEY)");
        }

        // 执行测试
        databaseInitializer.run(mockArgs);

        // 验证数据库仍然存在且表结构正确
        assertTrue("数据库文件应该存在", testDbFile.exists());
        try (Connection conn = DriverManager.getConnection(TEST_DB_URL)) {
            assertTrue("project表应该存在", isTableExists(conn, "project"));
            assertTrue("simulation表应该存在", isTableExists(conn, "simulation"));
            assertTrue("device表应该存在", isTableExists(conn, "device"));
        }
    }

    @Test
    public void testRun_IntegrationTest_WithIncompleteDatabase() throws Exception {
        // 创建不完整的数据库（只有部分表）
        testDataDir.mkdirs();
        try (Connection conn = DriverManager.getConnection(TEST_DB_URL)) {
            conn.createStatement().execute("CREATE TABLE project (project_id INTEGER PRIMARY KEY)");
            // 缺少simulation和device表
        }

        // 执行测试
        databaseInitializer.run(mockArgs);

        // 验证所有表都存在（应该被修复）
        try (Connection conn = DriverManager.getConnection(TEST_DB_URL)) {
            assertTrue("project表应该存在", isTableExists(conn, "project"));
            assertTrue("simulation表应该存在", isTableExists(conn, "simulation"));
            assertTrue("device表应该存在", isTableExists(conn, "device"));
        }
    }
    
    @Test(expected = Exception.class)
    public void testRun_WhenDriverLoadFails_ShouldThrowException() throws Exception {
        // 设置无效的驱动类名
        ReflectionTestUtils.setField(databaseInitializer, "driverClassName", "invalid.driver.Class");

        // 执行测试，应该抛出异常
        databaseInitializer.run(mockArgs);
    }

    /**
     * 检查表是否存在的辅助方法
     */
    private boolean isTableExists(Connection connection, String tableName) {
        try {
            String sql = "SELECT name FROM sqlite_master WHERE type='table' AND name='" + tableName + "'";
            return connection.createStatement().executeQuery(sql).next();
        } catch (SQLException e) {
            return false;
        }
    }
}
