<template>
  <div class="page">
    <VLayoutSidebarToggle>
      <template #full>
        <div
          :class="
            isShowBottomLeftIcon
              ? [
                  'vlayout-nav-logo',
                  systemType == 5
                    ? 'vlayout-nav-logo-after-short'
                    : 'vlayout-nav-logo-after-full'
                ]
              : ''
          "
        />
      </template>
      <template #short>
        <div
          :class="
            isShowBottomLeftIcon
              ? ['vlayout-nav-logo', 'vlayout-nav-logo-after-short']
              : ''
          "
        />
      </template>
    </VLayoutSidebarToggle>
  </div>
</template>

<script>
import { VLayoutSidebarToggle } from "@omega/layout";
import common from "@/utils/common";

export default {
  name: "VframeMenuFooter",
  components: { VLayoutSidebarToggle },
  computed: {
    isShowBottomLeftIcon() {
      let systemCfg = this.$store.state.systemConfig.systemCfg;
      console.log(systemCfg, "systemCfg");
      return common.get(systemCfg, "isShowBottomLeftIcon");
    },
    systemType() {
      let systemCfg = this.$store.state.systemConfig.systemCfg;
      return common.get(systemCfg, "systemType");
    }
  },
  data() {
    return {};
  },
  watch: {},
  methods: {}
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
}
.vlayout-nav-logo {
  box-sizing: border-box;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}
.vlayout-nav-logo-after-full {
  height: 80px;
  background-size: auto;
  background-image: url(/static/image/logo-after-unfold.png);
}
.vlayout-nav-logo-after-short {
  height: 80px;
  background-size: auto;
  background-image: url(/static/image/logo-after-fold.png);
}
</style>
