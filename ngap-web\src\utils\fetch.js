import { httping, http, HttpBase } from "@omega/http";
import { Message } from "element-ui";

const config = {
  headers: {
    post: {
      "Content-Type": "application/json;charset=UTF-8 "
    }
  },
  responseType: "json"
};

const auth = false;

// 没有loading 有msg
const httpReject = new HttpBase(
  { loading: false, rejectErrorCode: false, auth },
  config
);

// 有loading 有msg
const httpingReject = new HttpBase({ rejectErrorCode: false, auth }, config);

// 有loading 没有msg
const httpingSilent = new HttpBase(
  { silent: true, rejectErrorCode: false, auth },
  config
);

// 没有loading 没有msg
const httpSilent = new HttpBase(
  { silent: true, loading: false, rejectErrorCode: false, auth },
  config
);

function fetch(option) {
  const [hideNotice, hideMsg] = [
    option?.headers?.hideNotice,
    option?.headers?.hideMsg
  ];

  // hideNotice为true ：没loading没msg
  // hideMsg为true ：没msg

  if (hideNotice) {
    return httpSilent(option);
  }
  // 有loading 没有msg
  if (!hideNotice && hideMsg) {
    return httpingSilent(option);
  }
  // 有loading有msg
  if (!hideNotice && !hideMsg) {
    return httpingReject(option);
  }
}

export default fetch;

export const util = {
  getContentDispositionFileNmae(res) {
    const contentDisposition = res.headers["content-disposition"];
    const regExp = /filename=([^;]*)/;
    if (contentDisposition) {
      const [, fileName] = regExp.exec(contentDisposition);
      return window.decodeURIComponent(fileName);
    }
  },
  /**
   * 下载文件
   * @param url 文件url
   * @param name 文件名
   */
  download(url, name, cb) {
    const a = document.createElement("a");
    a.download = name;
    a.href = url;
    a.target = "_blank";
    document.head.appendChild(a);
    a.click();
    document.head.removeChild(a);
    cb && cb();
  }
};

/**
 * 以POST请求方式需要导出的文件
 * @param url 必须
 * @param param 非必须
 * @param filename 非必须
 */
export function download(url, param, filename, method = "POST", cb) {
  return httpReject({
    url: url,
    method: method,
    responseType: "blob",
    data: param
  })
    .then(res => {
      const url = window.URL.createObjectURL(res.data);
      util.download(
        url,
        filename || util.getContentDispositionFileNmae(res),
        cb
      );
    })
    .catch(function (error) {
      Message({
        message: $T("请检查网络是否连接正常"),
        showClose: true,
        type: "error"
      });
      cb && cb(error);
    });
}

// 扁平化参数
export function flattenParams(data) {
  if (!_.has(data, "rootCondition")) return data;
  let conditions = _.get(data, "rootCondition.filter.expressions", []);
  let id = _.get(data, "rootCondition.treeNode.id");
  let modelLabel = _.get(data, "rootCondition.treeNode.modelLabel");
  let page = _.get(data, "rootCondition.page");
  let result = {};
  conditions.forEach(item => {
    result[item.prop] = item.limit;
  });
  result.page = page;
  result.id = id;
  result.modelLabel = modelLabel;
  return result;
}
