package com.cet.electric.ngapserver.dao;

import com.cet.electric.ngapserver.entity.Project;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Component
public interface ProjectDao {

    /**
     * 创建新项目
     *
     * @param project 项目实体
     * @return 受影响的行数
     */
    int createProject(Project project);

    /**
     * 分页查询项目
     *
     * @param offset      偏移量
     * @param size        每页大小
     * @param sortBy      排序字段
     * @param sortOrder   排序方向
     * @param projectType 项目类型(可选)
     * @param keyword     搜索关键词(可选)
     * @param startTime   起始时间(时间戳-毫秒，可选)
     * @param endTime     结束时间(时间戳-毫秒，可选)
     * @return 项目列表
     */
    List<Project> getProjects(@Param("offset") int offset,
                              @Param("size") int size,
                              @Param("sortBy") String sortBy,
                              @Param("sortOrder") String sortOrder,
                              @Param("projectType") String projectType,
                              @Param("keyword") String keyword,
                              @Param("startTime") Long startTime,
                              @Param("endTime") Long endTime);

    /**
     * 查询项目总数
     *
     * @param projectType 项目类型(可选)
     * @param keyword     搜索关键词(可选)
     * @return 总数
     */
    Long countProjects(
            @Param("projectType") String projectType,
            @Param("keyword") String keyword,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);

    /**
     * 根据项目名称查询项目
     *
     * @param projectName 项目名称
     * @return 项目实体，如果不存在返回null
     */
    Project findByProjectName(String projectName);

    /**
     * 根据ID查询项目
     *
     * @param projectId 项目ID
     * @return 项目实体，如果不存在返回null
     */
    Project findById(Long projectId);

    /**
     * 更新项目信息
     *
     * @param project 更新的项目实体
     * @return 受影响的行数
     */
    int updateProject(Project project);

    /**
     * 查询所有标记为已删除的项目
     *
     * @return 已删除的项目列表
     */
    List<Project> findAllDeleted();

    /**
     * 删除所有标记为已删除的项目（物理删除）
     *
     * @return 受影响的行数
     */
    int deleteAllMarkedAsDeleted();
}

