import moment from "moment";
import { mhCONST } from "@/config/const";
import common from "@/utils/common";

export function uppercaseFirst(string) {
  return string.charAt(0).toUpperCase() + string.slice(1);
}
export function formatStr(string) {
  if (string === "") return "--";
  if (!string) return "--";
  return string;
}

export function formatDate(val, format) {
  if (!val) {
    return "--";
  }
  if (!format) format = "YYYY-MM-DD HH:mm:ss";
  return moment(val).format(format);
}

export function addNumSymbol(num, precision = 0, unit = "") {
  if (num === 0) return "0";
  if (!num || _.isNaN(_.toNumber(num))) return "--";
  if (!precision && precision !== 0) return num.toLocaleString();
  let pow = Math.pow(10, precision);
  return (Math.round(num * pow) / pow).toLocaleString() + (unit ? unit : "");
}
export function formatNum(...args) {
  return common.setNumFixed(...args);
}
export function roundNumber(...args) {
  return common.roundNumber(...args);
}
export function alarmLevelColor(val) {
  let level = mhCONST("customalarmlevel").find(item => item.alarmlevel === val);
  if (level) {
    return level.colorsetid;
  } else {
    return "";
  }
}

export function alarmLevelStyle(val) {
  let level = mhCONST("customalarmlevel").find(item => item.alarmlevel === val);
  let colorsetid = _.get(level, "colorsetid", "");
  return {
    color: "#000",
    background: colorsetid,
    borderColor: colorsetid
  };
}
export function alarmLevelName(val) {
  let alarmLevel = mhCONST("customalarmlevel").find(
    item => item.alarmlevel === val
  );
  if (alarmLevel) {
    return alarmLevel.name;
  } else {
    return val;
  }
}

export function maintain(val) {
  if (val == 1) {
    return "一个月/次";
  } else if (val == 3) {
    return "三个月/次";
  } else if (val == 6) {
    return "六个月/次";
  } else if (val == 12) {
    return "一年/次";
  } else {
    return "--";
  }
}
