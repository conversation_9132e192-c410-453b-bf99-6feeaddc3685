package com.cet.electric.ngapserver.util.excel;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;

import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;

/**
 * OptimizedExcelProcessor 数值格式化测试
 * 验证优化方式与传统方式的数值格式化一致性
 */
class OptimizedExcelProcessorTest {

    private OptimizedExcelProcessor processor;
    private Method getCellValueMethod;
    private Method getNumericCellValueMethod;

    @BeforeEach
    void setUp() throws Exception {
        processor = new OptimizedExcelProcessor();
        
        // 使用反射获取私有方法进行测试
        getCellValueMethod = OptimizedExcelProcessor.class.getDeclaredMethod("getCellValueAsStringOptimized", Cell.class);
        getCellValueMethod.setAccessible(true);
        
        getNumericCellValueMethod = OptimizedExcelProcessor.class.getDeclaredMethod("getNumericCellValueOptimized", Cell.class);
        getNumericCellValueMethod.setAccessible(true);
    }

    @Test
    @DisplayName("测试大整数格式化 - 用户编号")
    void testLargeIntegerFormatting() throws Exception {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet();
            Row row = sheet.createRow(0);
            Cell cell = row.createCell(0);
            
            // 设置大整数值（用户编号）
            cell.setCellValue(4206858070152L);
            
            String result = (String) getCellValueMethod.invoke(processor, cell);
            
            // 验证结果应该是整数格式，不是科学计数法
            assertEquals("4206858070152", result);
            assertFalse(result.contains("E"));
            assertFalse(result.contains("."));
        }
    }

    @Test
    @DisplayName("测试整数格式化 - 电压等级")
    void testIntegerFormatting() throws Exception {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet();
            Row row = sheet.createRow(0);
            Cell cell = row.createCell(0);
            
            // 设置整数值（电压等级）
            cell.setCellValue(220);
            
            String result = (String) getCellValueMethod.invoke(processor, cell);
            
            // 验证结果应该是整数格式
            assertEquals("220", result);
            assertFalse(result.contains("."));
        }
    }

    @Test
    @DisplayName("测试小整数格式化 - 相别")
    void testSmallIntegerFormatting() throws Exception {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet();
            Row row = sheet.createRow(0);
            Cell cell = row.createCell(0);
            
            // 设置小整数值（相别）
            cell.setCellValue(1);
            
            String result = (String) getCellValueMethod.invoke(processor, cell);
            
            // 验证结果应该是整数格式
            assertEquals("1", result);
            assertFalse(result.contains("."));
        }
    }

    @Test
    @DisplayName("测试浮点数格式化")
    void testFloatFormatting() throws Exception {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet();
            Row row = sheet.createRow(0);
            Cell cell = row.createCell(0);
            
            // 设置浮点数值
            cell.setCellValue(220.5);
            
            String result = (String) getCellValueMethod.invoke(processor, cell);
            
            // 验证结果应该是浮点数格式
            assertEquals("220.5", result);
            assertTrue(result.contains("."));
        }
    }

    @Test
    @DisplayName("测试字符串类型单元格")
    void testStringCellFormatting() throws Exception {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet();
            Row row = sheet.createRow(0);
            Cell cell = row.createCell(0);
            
            // 设置字符串值
            cell.setCellValue("胡元凯");
            
            String result = (String) getCellValueMethod.invoke(processor, cell);
            
            // 验证结果应该是原字符串
            assertEquals("胡元凯", result);
        }
    }

    @Test
    @DisplayName("测试空单元格")
    void testNullCellFormatting() throws Exception {
        String result = (String) getCellValueMethod.invoke(processor, (Cell) null);
        
        // 验证空单元格返回空字符串
        assertEquals("", result);
    }

    @Test
    @DisplayName("测试数值格式化方法直接调用")
    void testNumericCellValueMethod() throws Exception {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet();
            Row row = sheet.createRow(0);

            // 测试整数
            Cell intCell = row.createCell(0);
            intCell.setCellValue(4206858070152L);
            String intResult = (String) getNumericCellValueMethod.invoke(processor, intCell);
            assertEquals("4206858070152", intResult);

            // 测试浮点数
            Cell floatCell = row.createCell(1);
            floatCell.setCellValue(220.5);
            String floatResult = (String) getNumericCellValueMethod.invoke(processor, floatCell);
            assertEquals("220.5", floatResult);
        }
    }

    @Test
    @DisplayName("测试列索引解析")
    void testParseColumnIndex() throws Exception {
        Method parseColumnIndexMethod = OptimizedExcelProcessor.class.getDeclaredMethod("parseColumnIndex", String.class);
        parseColumnIndexMethod.setAccessible(true);

        // 测试正常的列名
        assertEquals(1, parseColumnIndexMethod.invoke(processor, "p1"));
        assertEquals(96, parseColumnIndexMethod.invoke(processor, "u96"));
        assertEquals(50, parseColumnIndexMethod.invoke(processor, "p50"));

        // 测试异常情况
        assertEquals(-1, parseColumnIndexMethod.invoke(processor, "invalid"));
        assertEquals(-1, parseColumnIndexMethod.invoke(processor, "p"));
    }

    @Test
    @DisplayName("测试日期解析")
    void testParseDate() throws Exception {
        Method parseDateMethod = OptimizedExcelProcessor.class.getDeclaredMethod("parseDate", String.class);
        parseDateMethod.setAccessible(true);

        // 测试 yyyyMMdd 格式（传统方式）
        Object result = parseDateMethod.invoke(processor, "20240101");
        assertNotNull(result);
        assertEquals("2024-01-01", result.toString());

        // 测试无效日期
        Object invalidResult = parseDateMethod.invoke(processor, "invalid");
        assertNull(invalidResult);
    }
}
