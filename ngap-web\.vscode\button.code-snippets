{
  // Place your V2.x 工作区 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  // "Print to console": {
  // 	"scope": "javascript,typescript",
  // 	"prefix": "log",
  // 	"body": [
  // 		"console.log('$1');",
  // 		"$2"
  // 	],
  // 	"description": "Log output to console"
  // }

  //cet-button的代码片段
  "cet-button-template": {
    "prefix": "cet-button-template",
    "body": [
      "<!-- ${1:设置组件唯一识别字段}按钮组件 -->                                               ",
      "<CetButton                                                                       ",
      "  v-bind = 'CetButton_$1'                                                        ",
      "  v-on=\"CetButton_$1.event\"                            ",
      "></CetButton>                                                                     "
    ],
    "description": ""
  },
  "cet-button-data": {
    "prefix": "cet-button-data",
    "body": [
      " // ${1:设置组件唯一识别字段}组件                                                                    ",
      " CetButton_$1:{                                                        ",
      "   visible_in: true,                                   ",
      "   disable_in: false,                                   ",
      "   title: '按钮',                                   ",
      "   type: 'primary',                                   ",
      "   plain: true,                                                                      ",
      "   event: {                                                              ",
      "      statusTrigger_out:this.CetButton_$1_statusTrigger_out,                                                ",
      "   }                                                                           ",
      " },                                                                                   "
    ],
    "description": ""
  },
  "cet-button-method": {
    "prefix": "cet-button-method",
    "body": ["// ${1:设置组件唯一识别字段}输出", "    CetButton_$1_statusTrigger_out(val) { ", "    },                                   "],
    "description": ""
  }
}
