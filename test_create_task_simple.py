"""
简化版仿真任务创建测试
参考原始登录测试样例的风格，编写简洁的创建任务测试
"""
from playwright.sync_api import Page, expect
import time


def test_create_new_simulation_task(page: Page) -> None:
    """创建新仿真任务的测试"""
    
    # 登录系统
    page.goto("http://10.12.135.167:9089/#/login")
    page.get_by_placeholder("请输入账号").click()
    page.get_by_placeholder("请输入账号").fill("ROOT")
    page.get_by_placeholder("请输入密码").click()
    page.get_by_placeholder("请输入密码").fill("sA123456@")
    page.get_by_role("button", name="登录").click()
    
    # 验证登录成功
    expect(page.locator(".frame-vlayout-nav-logo")).to_be_visible()
    
    # 等待页面加载完成
    page.wait_for_load_state("networkidle")
    
    # 点击第一个项目的详情按钮
    page.locator(".el-card").first().get_by_role("button", name="详情").click()
    
    # 点击新增按钮
    page.get_by_role("button", name="新增").click()
    
    # 填写任务名称
    task_name = f"测试任务_{int(time.time())}"
    page.get_by_placeholder("请输入任务名称").fill(task_name)
    
    # 点击确定按钮保存
    page.get_by_role("button", name="确定").click()
    
    # 验证创建成功
    expect(page.locator(".el-message--success")).to_be_visible()
    expect(page.locator(f"text={task_name}")).to_be_visible()


def test_cancel_create_task(page: Page) -> None:
    """测试取消创建任务"""
    
    # 登录系统
    page.goto("http://10.12.135.167:9089/#/login")
    page.get_by_placeholder("请输入账号").fill("ROOT")
    page.get_by_placeholder("请输入密码").fill("sA123456@")
    page.get_by_role("button", name="登录").click()
    expect(page.locator(".frame-vlayout-nav-logo")).to_be_visible()
    
    # 进入仿真任务页面
    page.wait_for_load_state("networkidle")
    page.locator(".el-card").first().get_by_role("button", name="详情").click()
    
    # 点击新增按钮
    page.get_by_role("button", name="新增").click()
    
    # 填写任务名称后取消
    page.get_by_placeholder("请输入任务名称").fill("取消测试任务")
    page.get_by_role("button", name="取消").click()
    
    # 验证弹窗关闭，任务未创建
    expect(page.get_by_placeholder("请输入任务名称")).not_to_be_visible()
