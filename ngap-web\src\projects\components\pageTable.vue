<template>
  <div class="page">
    <CetTable
      style="height: calc(100% - 40px)"
      :data.sync="CetTable_list.data"
      :dynamicInput.sync="CetTable_list.dynamicInput"
      v-bind="CetTable_list"
      v-on="CetTable_list.event"
    >
      <slot name="handler"></slot>
    </CetTable>
    <el-pagination
      class="pt5"
      @current-change="handleCurrentChange"
      background
      :page-size="100"
      :current-page="currentPage"
      layout="prev, pager, next"
      :total="total"
    ></el-pagination>
  </div>
</template>

<script>
import common from "@/utils/common";
import customApi from "@/api/custom";

export default {
  name: "pageTable",
  components: {},
  props: {
    // columnsList: {
    //   type: Array
    // },
    queryFunc: {
      type: String
    },
    params: {
      type: Object
    },
    queryTrigger_in: {
      type: Number
    },
    refreshTrigger_in: {
      type: Number
    }
  },
  data() {
    return {
      page: { index: 0, limit: 100 },
      flagTotal: 0,
      total: 0,
      currentPage: 1,

      // list表格组件
      CetTable_list: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {}
      }
    };
  },
  watch: {
    queryTrigger_in(val) {
      this.$nextTick(() => {
        this.queryTableList();
      });
    },
    refreshTrigger_in(val) {
      this.currentPage = 1;
      this.page = { index: 0, limit: 100 };
      this.queryTableList();
    }
  },
  methods: {
    handleCurrentChange(val) {
      console.log(`当前List页: ${val}`);
      this.currentPage = val;
      this.page.index = (val - 1) * 100;
      this.queryTableList();
    },
    queryTableList() {
      let params = this._.cloneDeep(this.params);
      params.page = this.page;
      customApi[this.queryFunc](params).then(res => {
        if (res && res.code == 0) {
          let data = _.get(res, "data", []) || [];
          if (_.get(res, "total", null)) {
            this.total = _.get(res, "total", null);
            this.flagTotal = this.total;
          } else {
            if (data.length == 0) this.total = 0;
            else this.total = this.flagTotal;
          }
          this.CetTable_list.data = data;
        } else {
          this.CetTable_list.data = [];
          this.total = 0;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
}
</style>
