package com.cet.electric.ngapserver.service.impl;

import com.cet.electric.ngapserver.dao.ProjectDao;
import com.cet.electric.ngapserver.dao.SimulationDao;
import com.cet.electric.ngapserver.dto.ErrorMsg;
import com.cet.electric.ngapserver.entity.Project;
import com.cet.electric.ngapserver.entity.Simulation;
import com.cet.electric.ngapserver.util.FileUtils;
import com.cet.electric.ngapserver.util.JsonUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ProjectServiceImpl.class, FileUtils.class, JsonUtils.class})
public class ProjectServiceImplTest {

    @InjectMocks
    private ProjectServiceImpl projectService;

    @Mock
    private ProjectDao projectDao;

    @Mock
    private SimulationDao simulationDao;

    @Mock
    private MultipartFile mockFile;

    @Mock
    private HttpServletResponse mockResponse;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        // Mock 静态方法
        mockStatic(FileUtils.class);
        mockStatic(JsonUtils.class);
    }

    @Test
    public void testCreateProject_Success() {
        // 准备测试数据
        Project project = Project.builder()
                .projectName("测试项目")
                .projectType("General_scenario")
                .build();

        // Mock DAO 行为
        when(projectDao.findByProjectName("测试项目")).thenReturn(null);
        when(projectDao.createProject(any(Project.class))).thenReturn(1);

        // 执行测试
        Project result = projectService.createProject(project);

        // 验证结果
        assertNotNull(result);
        assertEquals("测试项目", result.getProjectName());
        verify(projectDao).findByProjectName("测试项目");
        verify(projectDao).createProject(any(Project.class));
    }

    @Test(expected = ErrorMsg.class)
    public void testCreateProject_EmptyName() {
        // 准备测试数据
        Project project = Project.builder()
                .projectName("")
                .build();

        // 执行测试 - 应该抛出异常
        projectService.createProject(project);
    }

    @Test(expected = ErrorMsg.class)
    public void testCreateProject_NullName() {
        // 准备测试数据
        Project project = Project.builder()
                .projectName(null)
                .build();

        // 执行测试 - 应该抛出异常
        projectService.createProject(project);
    }

    @Test(expected = ErrorMsg.class)
    public void testCreateProject_DuplicateName() {
        // 准备测试数据
        Project project = Project.builder()
                .projectName("重复项目")
                .build();

        Project existingProject = Project.builder()
                .projectId(1L)
                .projectName("重复项目")
                .isDeleted(0)
                .build();

        // Mock DAO 行为
        when(projectDao.findByProjectName("重复项目")).thenReturn(existingProject);

        // 执行测试 - 应该抛出异常
        projectService.createProject(project);
    }

    @Test(expected = ErrorMsg.class)
    public void testCreateProject_DatabaseError() {
        // 准备测试数据
        Project project = Project.builder()
                .projectName("测试项目")
                .build();

        // Mock DAO 行为
        when(projectDao.findByProjectName("测试项目")).thenReturn(null);
        when(projectDao.createProject(any(Project.class))).thenReturn(0);

        // 执行测试 - 应该抛出异常
        projectService.createProject(project);
    }

    @Test
    public void testGetProjectScenarios() {
        // 执行测试
        Map<String, String> scenarios = projectService.getProjectScenarios();

        // 验证结果
        assertNotNull(scenarios);
        assertEquals(2, scenarios.size());
        assertEquals("通用场景", scenarios.get("General_scenario"));
        assertEquals("电压合格率场景", scenarios.get("Voltage_qualification_scenario"));
    }

    @Test
    public void testGetProjects_Success() {
        // 准备测试数据
        List<Project> mockProjects = Arrays.asList(
                Project.builder().projectId(1L).projectName("项目1").build(),
                Project.builder().projectId(2L).projectName("项目2").build()
        );

        // Mock DAO 行为
        when(projectDao.getProjects(anyInt(), anyInt(), anyString(), anyString(), 
                anyString(), anyString(), any(), any())).thenReturn(mockProjects);

        // 执行测试
        List<Project> result = projectService.getProjects(1, 10, "created_at", "desc", 
                null, null, null, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.size());
        verify(projectDao).getProjects(0, 10, "created_at", "desc", null, null, null, null);
    }

    @Test
    public void testGetProjects_WithDefaultParams() {
        // 准备测试数据
        List<Project> mockProjects = new ArrayList<>();

        // Mock DAO 行为
        when(projectDao.getProjects(anyInt(), anyInt(), anyString(), anyString(), 
                anyString(), anyString(), any(), any())).thenReturn(mockProjects);

        // 执行测试 - 传入null参数
        List<Project> result = projectService.getProjects(null, null, null, null, 
                null, null, null, null);

        // 验证结果
        assertNotNull(result);
        verify(projectDao).getProjects(0, 10, "created_at", "desc", null, null, null, null);
    }

    @Test
    public void testCountProjects() {
        // Mock DAO 行为
        when(projectDao.countProjects(anyString(), anyString(), any(), any())).thenReturn(5L);

        // 执行测试
        Long count = projectService.countProjects("General_scenario", "测试", null, null);

        // 验证结果
        assertEquals(Long.valueOf(5), count);
        verify(projectDao).countProjects("General_scenario", "测试", null, null);
    }

    @Test
    public void testDeleteProject_Success() {
        // 准备测试数据
        Project project = Project.builder()
                .projectId(1L)
                .projectName("测试项目")
                .isDeleted(0)
                .build();

        List<Simulation> simulations = Arrays.asList(
                Simulation.builder().simulationId(1L).projectId(1L).build(),
                Simulation.builder().simulationId(2L).projectId(1L).build()
        );

        // Mock DAO 行为
        when(projectDao.findById(1L)).thenReturn(project);
        when(projectDao.updateProject(any(Project.class))).thenReturn(1);
        when(simulationDao.getSimulations(anyInt(), anyInt(), anyString(), anyString(), 
                eq(1L), anyString(), any(), any())).thenReturn(simulations);
        when(simulationDao.updateSimulation(any(Simulation.class))).thenReturn(1);

        // 执行测试
        projectService.deleteProject(1L);

        // 验证结果
        verify(projectDao).findById(1L);
        verify(projectDao).updateProject(any(Project.class));
        verify(simulationDao).getSimulations(0, 100, "created_at", "asc", 1L, "", null, null);
        verify(simulationDao, times(2)).updateSimulation(any(Simulation.class));
    }

    @Test(expected = ErrorMsg.class)
    public void testDeleteProject_NotFound() {
        // Mock DAO 行为
        when(projectDao.findById(1L)).thenReturn(null);

        // 执行测试 - 应该抛出异常
        projectService.deleteProject(1L);
    }

    @Test(expected = ErrorMsg.class)
    public void testDeleteProject_AlreadyDeleted() {
        // 准备测试数据
        Project project = Project.builder()
                .projectId(1L)
                .projectName("测试项目")
                .isDeleted(1)
                .build();

        // Mock DAO 行为
        when(projectDao.findById(1L)).thenReturn(project);

        // 执行测试 - 应该抛出异常
        projectService.deleteProject(1L);
    }

    @Test(expected = ErrorMsg.class)
    public void testDeleteProject_DatabaseError() {
        // 准备测试数据
        Project project = Project.builder()
                .projectId(1L)
                .projectName("测试项目")
                .isDeleted(0)
                .build();

        // Mock DAO 行为
        when(projectDao.findById(1L)).thenReturn(project);
        when(projectDao.updateProject(any(Project.class))).thenReturn(0);

        // 执行测试 - 应该抛出异常
        projectService.deleteProject(1L);
    }

    @Test
    public void testCopyProject_Success() {
        // 准备测试数据
        Project originalProject = Project.builder()
                .projectId(1L)
                .projectName("原项目")
                .projectType("General_scenario")
                .isDeleted(0)
                .build();

        List<Simulation> simulations = Arrays.asList(
                Simulation.builder().simulationId(1L).projectId(1L).simulationName("仿真1").build()
        );

        // Mock DAO 行为
        when(projectDao.findById(1L)).thenReturn(originalProject);
        when(projectDao.findByProjectName(anyString())).thenReturn(null);
        when(projectDao.createProject(any(Project.class))).thenReturn(1);
        when(simulationDao.getSimulations(anyInt(), anyInt(), anyString(), anyString(),
                eq(1L), anyString(), any(), any())).thenReturn(simulations);
        when(simulationDao.createSimulation(any(Simulation.class))).thenReturn(1);

        // 执行测试
        Project result = projectService.copyProject(1L);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.getProjectName() == null);
        verify(projectDao).findById(1L);
        verify(projectDao).createProject(any(Project.class));
        verify(simulationDao).createSimulation(any(Simulation.class));
    }

    @Test(expected = ErrorMsg.class)
    public void testCopyProject_NotFound() {
        // Mock DAO 行为
        when(projectDao.findById(1L)).thenReturn(null);

        // 执行测试 - 应该抛出异常
        projectService.copyProject(1L);
    }

    @Test(expected = ErrorMsg.class)
    public void testCopyProject_AlreadyDeleted() {
        // 准备测试数据
        Project project = Project.builder()
                .projectId(1L)
                .projectName("测试项目")
                .isDeleted(1)
                .build();

        // Mock DAO 行为
        when(projectDao.findById(1L)).thenReturn(project);

        // 执行测试 - 应该抛出异常
        projectService.copyProject(1L);
    }

    @Test
    public void testExportProject_Success() throws Exception {
        // 准备测试数据
        Project project = Project.builder()
                .projectId(1L)
                .projectName("测试项目")
                .projectType("General_scenario")
                .isDeleted(0)
                .build();

        // Mock DAO 行为
        when(projectDao.findById(1L)).thenReturn(project);

        // Mock JsonUtils 静态方法
        org.powermock.api.mockito.PowerMockito.when(JsonUtils.convertObjectToJsonString(any()))
                .thenReturn("{\"projectId\":1,\"projectName\":\"测试项目\",\"projectType\":\"General_scenario\"}");

        // Mock FileUtils 静态方法 - 使用 doNothing 来避免实际的文件下载操作
        org.powermock.api.mockito.PowerMockito.doNothing().when(FileUtils.class, "downloadFile", anyString(), any(HttpServletResponse.class));

        // 执行测试
        projectService.exportProject(1L, mockResponse);

        // 验证结果
        verify(projectDao).findById(1L);

        // 验证静态方法调用
        org.powermock.api.mockito.PowerMockito.verifyStatic(JsonUtils.class);
        JsonUtils.convertObjectToJsonString(any());

        org.powermock.api.mockito.PowerMockito.verifyStatic(FileUtils.class);
        FileUtils.downloadFile(anyString(), eq(mockResponse));
    }

    @Test(expected = ErrorMsg.class)
    public void testExportProject_NotFound() {
        // Mock DAO 行为
        when(projectDao.findById(1L)).thenReturn(null);

        // 执行测试 - 应该抛出异常
        projectService.exportProject(1L, mockResponse);
    }

    @Test
    public void testImportProject_Success() throws IOException {
        // 准备测试数据 - 模拟解析后的配置数据
        Map<String, Object> mockConfig = new HashMap<>();
        mockConfig.put("projectName", "导入项目");
        mockConfig.put("projectType", "General_scenario");
        mockConfig.put("simulationId", 24);
        mockConfig.put("simulationName", "besstest0611");

        // Mock 文件行为
        when(mockFile.getOriginalFilename()).thenReturn("project.json");
        when(mockFile.isEmpty()).thenReturn(false);
        org.mockito.Mockito.doNothing().when(mockFile).transferTo(any(java.io.File.class));

        // Mock JsonUtils 静态方法
        org.powermock.api.mockito.PowerMockito.when(JsonUtils.convertJsonFileToMap(any(java.io.File.class)))
                .thenReturn(mockConfig);

        // Mock DAO 行为
        when(projectDao.findByProjectName("导入项目")).thenReturn(null);
        when(projectDao.createProject(any(Project.class))).thenReturn(1);

        // 执行测试
        Project result = projectService.importProject(mockFile);

        // 验证结果
        assertNotNull(result);
        assertEquals("导入项目", result.getProjectName());
        assertEquals("General_scenario", result.getProjectType());
        verify(projectDao).findByProjectName("导入项目");
        verify(projectDao).createProject(any(Project.class));

        // 验证静态方法调用
        org.powermock.api.mockito.PowerMockito.verifyStatic(JsonUtils.class);
        JsonUtils.convertJsonFileToMap(any(java.io.File.class));
    }

    @Test(expected = ErrorMsg.class)
    public void testImportProject_InvalidFile() throws IOException {
        // Mock 文件行为
        when(mockFile.getOriginalFilename()).thenReturn("project.txt");

        // 执行测试 - 应该抛出异常
        projectService.importProject(mockFile);
    }

    @Test
    public void testImportProject_DuplicateName() throws IOException {
        // 准备测试数据 - 模拟解析后的配置数据
        Map<String, Object> mockConfig = new HashMap<>();
        mockConfig.put("projectName", "重复项目");
        mockConfig.put("projectType", "General_scenario");

        Project existingProject = Project.builder()
                .projectId(1L)
                .projectName("重复项目")
                .isDeleted(0)
                .build();

        // Mock 文件行为
        when(mockFile.getOriginalFilename()).thenReturn("project.json");
        when(mockFile.isEmpty()).thenReturn(false);
        org.mockito.Mockito.doNothing().when(mockFile).transferTo(any(java.io.File.class));

        // Mock JsonUtils 静态方法
        org.powermock.api.mockito.PowerMockito.when(JsonUtils.convertJsonFileToMap(any(java.io.File.class)))
                .thenReturn(mockConfig);

        // Mock DAO 行为 - 第一次查询返回存在的项目，后续查询返回null
        when(projectDao.findByProjectName("重复项目")).thenReturn(existingProject);
        when(projectDao.findByProjectName(startsWith("重复项目_"))).thenReturn(null);
        when(projectDao.createProject(any(Project.class))).thenReturn(1);

        // 执行测试
        Project result = projectService.importProject(mockFile);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getProjectName().startsWith("重复项目_"));
        verify(projectDao).createProject(any(Project.class));

        // 验证静态方法调用
        org.powermock.api.mockito.PowerMockito.verifyStatic(JsonUtils.class);
        JsonUtils.convertJsonFileToMap(any(java.io.File.class));
    }
}
