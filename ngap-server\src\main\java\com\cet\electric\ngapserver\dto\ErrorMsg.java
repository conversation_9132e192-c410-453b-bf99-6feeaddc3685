package com.cet.electric.ngapserver.dto;

public class ErrorMsg extends RuntimeException {
    /**
     *
     */
    private static final long serialVersionUID = 3361643501161065435L;
    private int code;

    public ErrorMsg(int code, String message) {
        super(message);
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String toString() {
        return "ErrorMsg [code=" + code + ", getMessage()=" + getMessage() + ", getCause()=" + getCause() + "]";
    }

    public static String getStackTraceStr(Exception e) {
        StackTraceElement[] steArray = e.getStackTrace();
        if(steArray == null || steArray.length == 0) {
            return "stackTrace empty";
        }
        StringBuilder sb = new StringBuilder();
        sb.append(e.getMessage());
        for(int i = 0; i < steArray.length; i ++) {
            sb.append(System.lineSeparator());
            sb.append(steArray[i].toString());
        }
        return sb.toString();
    }
}
