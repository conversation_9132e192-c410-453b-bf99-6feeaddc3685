import getSystemConfig from "@/utils/getSystemConfig.js";

import { mhCONST } from "./config/const.js";

async function initSystem() {
  const systemConfig = await getSystemConfig();

  mhCONST("isNewAuthService", systemConfig.isNewAuthService);
  mhCONST("loginAccount&Password", systemConfig["loginAccount&Password"]);
  const title = document.createElement("title");
  title.innerText = systemConfig.title || "仿真管理平台";
  document.head.appendChild(title);
}

(async function startApp() {
  await initSystem();

  return import("./app.js");
})();
