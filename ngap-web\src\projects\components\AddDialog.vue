<template>
  <div class="addDialog">
    <div class="title">
      <div class="fl text">添加</div>
      <img src="./assets/u92.png" alt="" class="del fr" @click="close" />
    </div>
    <div class="shifts">
      <div class="shift" v-for="(item, i) in shifts" :key="i" :style="getBackground(item.color)" @click="select(i)">
        {{ item.tag }}
      </div>
    </div>
    <div class="shifts-list-wrap" v-if="show">
      <!-- confirm按钮组件 -->
      <CetButton v-bind="CetButton_confirm" v-on="CetButton_confirm.event" class="confirm"></CetButton>
      <el-steps direction="vertical" style="height: 80%">
        <el-step>
          <template #icon>
            <div class="cycle"></div>
          </template>
          <template #description>
            <div class="shifts-wrap" style="height: 40px">
              <inputWrap labelWidth="70px" label="选择班次序列" class="ml8">
                <ElSelect
                  v-model="ElSelect_shiftsList.value"
                  v-bind="ElSelect_shiftsList"
                  v-on="ElSelect_shiftsList.event"
                >
                  <ElOption
                    v-for="item in ElOption_shiftsList.options_in"
                    :key="item[ElOption_shiftsList.key]"
                    :label="item[ElOption_shiftsList.label]"
                    :value="item"
                    :disabled="item[ElOption_shiftsList.disabled]"
                  ></ElOption>
                </ElSelect>
              </inputWrap>
            </div>
            <div class="shifts-list">
              <div
                class="ml8 pl8 pr8 box mb8"
                v-for="item in ElSelect_shiftsList.value.children"
                :key="item.id"
                :style="{ background: item.color }"
              >
                <span>{{ item.tag }}</span>
                <span class="ml8 mr8"><i class="el-icon-time"></i></span>
                <span>{{ formatterShiftTime(item) }}</span>
              </div>
            </div>
          </template>
        </el-step>
        <el-step>
          <template #icon>
            <div class="cycle"></div>
          </template>
          <template #description>
            <inputWrap labelWidth="70px" label="结束排班时间" class="ml8">
              <el-date-picker
                v-model="endDay"
                type="date"
                placeholder="date"
                value-format="timestamp"
                :picker-options="pickerdate"
              ></el-date-picker>
            </inputWrap>
          </template>
        </el-step>
      </el-steps>
    </div>
  </div>
</template>

<script>
import common from "@/utils/common";
export default {
  name: "AddDialog",
  components: {},
  props: {
    shifts_in: {
      type: Array
    },
    schedulelist_in: {
      type: Array
    },
    startTime_in: {
      type: Number
    }
  },
  data() {
    return {
      show: false, // 加载班次序列面板显示
      shifts: [],
      // ElSelect_shiftsList组件
      ElSelect_shiftsList: {
        value: "",
        style: {
          width: "200px"
        },
        valueKey: "tree_id",
        event: {
          // change: this.ElSelect_shiftsList_change_out
        }
      },
      // shiftsList组件
      ElOption_shiftsList: {
        options_in: [],
        key: "tree_id",
        value: "value",
        label: "name",
        disabled: "disabled"
      },
      // confirm组件
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "确定",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      endDay: Date.now(),
      pickerdate: {
        disabledDate: time => {
          return (
            time.getTime() <= this.startTime_in || time.getTime() > this.$moment(this.startTime_in).endOf("M").valueOf()
          );
        }
      }
    };
  },
  watch: {},
  methods: {
    getBackground(color) {
      return "background:" + color;
    },
    close() {
      this.$emit("close");
    },
    select(index) {
      if (this.shifts[index].id === 0) {
        this.$emit("select", null);
      } else if (this.shifts[index].id === -1) {
        this.loadShiftList();
      } else {
        this.$emit("select", this.shifts[index]);
      }
    },
    // 加载班次序列
    loadShiftList() {
      this.show = true;
    },
    formatterShiftTime(val) {
      if (!this._.isEmpty(val.starttime) && !this._.isEmpty(val.endtime)) {
        return val.starttime + "-" + val.endtime;
      } else {
        return "--";
      }
    },
    // 确定按钮
    CetButton_confirm_statusTrigger_out() {
      if (this.endDay <= this.startTime_in) return this.$message.warning("结束时间要大于开始时间");
      if (!this.ElSelect_shiftsList.value.children || !this.ElSelect_shiftsList.value.children.length)
        return this.$message.warning("请选择一个有班次的序列");
      let obj = {
        startTime: this.startTime_in,
        endTime: this.endDay,
        list: this.ElSelect_shiftsList.value.children
      };
      this.$emit("save", obj);
      let item = this.ElOption_shiftsList.options_in;
    }
  },
  mounted() {
    let shifts = this._.cloneDeep(this.shifts_in) || [];
    shifts.push({
      color: "#0069e5",
      id: 0,
      name: "撤销",
      tag: "撤销"
    });
    shifts.push({
      color: "#0069e5",
      id: -1,
      name: "加载班次序列",
      tag: "加载班次序列"
    });
    this.shifts = shifts;
    this.ElOption_shiftsList.options_in = this.schedulelist_in;
    this.endDay = this.startTime_in;
    if (this.schedulelist_in.length) this.ElSelect_shiftsList.value = this.schedulelist_in[0];
  }
};
</script>

<style lang="scss" scoped>
.addDialog {
  width: 182px;
  height: 300px;
  @include background_color(BG1);
  border: 1px solid;
  @include border_color(B1);
  border-radius: 6px;
  position: relative;
  .title {
    line-height: 30px;
    overflow: hidden;
    .text {
      margin-left: 20px;
    }
    .del {
      width: 16px;
      height: 16px;
      margin-top: 7px;
      margin-right: 5px;
      cursor: pointer;
    }
  }
  .shifts {
    width: 174px;
    max-height: 250px;
    overflow: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    .shift {
      width: 80%;
      height: 30px;
      line-height: 30px;
      margin-top: 5px;
      border-radius: 6px;
      text-align: center;
      cursor: pointer;
    }
  }
  .cycle {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #00ffff;
  }
  .shifts-list-wrap {
    position: absolute;
    top: 0;
    right: -525px;
    width: 500px;
    height: 100%;
    @include background_color(BG3);
    border: 1px solid;
    @include border_color(B2);

    border-radius: 6px;
    padding: 10px;
    .shifts-list {
      max-height: 200px;
      overflow: auto;
      margin-bottom: 10px;
    }
    ::v-deep .el-step__title {
      display: none;
    }
    ::v-deep .el-step__description {
      &.is-wait {
        color: inherit;
      }
    }
    // ::v-deep .el-step__description {
    //   margin-top: 0;
    // }
    .confirm {
      position: absolute;
      bottom: 20px;
      right: 20px;
    }
  }
}
.box {
  height: 28px;
  line-height: 28px;
  border-radius: 5px;
  display: inline-block;
}
</style>
