import omegaApp from "@omega/app";
import { TokenStore } from "@omega/auth/tokenStore.js";
import { httping, http } from "@omega/http";
import fetch from "@/utils/fetch";
import { Message } from "element-ui";
import util from "@omega/auth/auth/util.js";
import store from "@/base/modules/store";
import _ from "lodash";

omegaApp.plugin.register(
  class enumPlugin {
    async beforeAppBoot() {
      //--------------------------------------- 浏览器版本检测
      let isChrome = /Chrome/.test(navigator.userAgent);
      let chromeVersion = "";

      if (isChrome) {
        let userAgent = navigator.userAgent;
        let chromeIndex = userAgent.indexOf("Chrome/");
        let versionIndex = chromeIndex + 7;
        let spaceIndex = userAgent.indexOf(" ", versionIndex);

        chromeVersion = userAgent.substring(versionIndex, spaceIndex);
        let currentVersion = chromeVersion.split(".")[0] * 1;
        let is64Bit = /x86_64|Win64|x64;/.test(userAgent);

        if (!is64Bit) {
          Message({
            message: $T("请安装最新版本64位Google Chrome进行访问"),
            duration: 0,
            showClose: true
          });
        } else {
          //   谷歌浏览器是在105版本支持h.265解码(目前只有dcim系统才有h265视频)
          if (currentVersion < 105) {
            Message({
              message: $T("当前谷歌浏览器版本不支持H.265解码，请更新至最新！"),
              duration: 0,
              showClose: true
            });
          }
        }
      } else {
        Message({
          message: $T("推荐安装最新版本64位Google Chrome进行访问"),
          duration: 5000
        });
      }
    }
  }
);
