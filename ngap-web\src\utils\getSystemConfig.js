import store from "@/base/modules/store";
import { getJsonFile } from "@omega/http/getJson.js";

async function getSystemConfig() {
  const systemConfig = await getJsonFile(
    `/static/SystemCfg.json?t=${Date.now()}`
  );

  // 定义一个不存在的key，防止和框架设置默认主题的方法冲突
  const defaultTheme = window.localStorage.getItem("omega_theme_default");
  if (!defaultTheme) {
    window.localStorage.setItem("omega_theme_default", systemConfig.themeColor);
    window.localStorage.setItem("omega_theme", systemConfig.themeColor);
  }

  store.commit("systemConfig/setSystemCfg", systemConfig);
  return systemConfig;
}

export default getSystemConfig;
