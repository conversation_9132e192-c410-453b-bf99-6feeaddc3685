<template>
  <div class="viewReport">
    <!-- 弹窗组件 -->
    <CetDialog
      show-close
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
    >
      <!-- <template v-slot:footer>
        <span>
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
        </span>
      </template> -->
      <div style="height: 750px">
        <el-card>
          <div class="space-between" style="height: 30px; line-height: 30px">
            <div>
              <span class="color-T3 fs-Aa">总用户数</span>
              <span class="color-T1 fs-H2 pl20">
                {{ get(cardData, "totalUsers") }}
              </span>
            </div>
            <el-divider direction="vertical"></el-divider>
            <div>
              <span class="color-T3 fs-Aa">整体电压合格率</span>
              <span class="color-T1 fs-H2 pl20">
                {{ setNumFixed(cardData?.qualifiedRate, 2, " %") }}
              </span>
            </div>
            <el-divider direction="vertical"></el-divider>
            <div>
              <span class="color-T3 fs-Aa">整体电压越上限率</span>
              <span class="color-T1 fs-H2 pl20">
                {{ setNumFixed(cardData?.aboveMaxRate, 2, " %") }}
              </span>
            </div>
            <el-divider direction="vertical"></el-divider>
            <div>
              <span class="color-T3 fs-Aa">整体电压越下限率</span>
              <span class="color-T1 fs-H2 pl20">
                {{ setNumFixed(cardData?.belowMinRate, 2, " %") }}
              </span>
            </div>
            <el-divider direction="vertical"></el-divider>
            <div>
              <span class="color-T3 fs-Aa">整体最高电压</span>
              <span class="color-T1 fs-H2 pl20">
                {{ setNumFixed(cardData?.maxVoltage, 2, " V") }}
              </span>
            </div>
            <el-divider direction="vertical"></el-divider>
            <div>
              <span class="color-T3 fs-Aa">整体最低电压</span>
              <span class="color-T1 fs-H2 pl20">
                {{ setNumFixed(cardData?.minVoltage, 2, " V") }}
              </span>
            </div>
          </div>
        </el-card>
        <ElInput
          class="fl mt16 mb8"
          v-model="ElInput_keyword.value"
          v-bind="ElInput_keyword"
          v-on="ElInput_keyword.event"
        ></ElInput>
        <el-table
          border
          height="100%"
          tooltip-effect="light"
          :data="tableData"
          style="height: calc(100% - 160px)"
          :header-cell-style="headerCellStyle"
        >
          <el-table-column
            v-for="(item, index) in Columns"
            v-bind="item"
            :key="item.prop + index"
          ></el-table-column>
        </el-table>
        <el-pagination
          class="fr pt5"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          :page-sizes="pageSizes"
          :page-size.sync="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </CetDialog>
  </div>
</template>
<script>
import common from "@/utils/common";
import { mhCONST } from "@/config/const";
import customApi from "@/api/custom";

export default {
  name: "viewReport",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      tableData: [],
      currentPage: 1,
      pageSizes: [10, 20, 50, 100],
      pageSize: 50,
      total: 0,
      Columns: [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 75,
          label: $T("序号"),
          sortable: false,
          headerAlign: "center",
          //   index: this.indexMethod,
          align: "center",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          prop: "userName",
          //   minWidth: 190,
          width: "",
          label: $T("用户名称"),
          sortable: false,
          headerAlign: "center",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.getColumnValue
        },
        {
          prop: "qualifiedRate",
          //   minWidth: 190,
          width: "",
          label: $T("电压合格率(%)"),
          sortable: false,
          headerAlign: "center",
          align: "left",
          showOverflowTooltip: true,
          formatter: (row, column, cellValue) =>
            common.setNumFixed(cellValue, 2)
        },
        {
          prop: "aboveMaxRate",
          //   minWidth: 190,
          width: "",
          label: $T("电压越上限率(%)"),
          sortable: false,
          headerAlign: "center",
          align: "left",
          showOverflowTooltip: true,
          formatter: (row, column, cellValue) =>
            common.setNumFixed(cellValue, 2)
        },
        {
          prop: "belowMinRate",
          //   minWidth: 190,
          width: "",
          label: $T("电压越下限率(%)"),
          sortable: false,
          headerAlign: "center",
          align: "left",
          showOverflowTooltip: true,
          formatter: (row, column, cellValue) =>
            common.setNumFixed(cellValue, 2)
        },
        {
          prop: "maxVoltage",
          //   minWidth: 190,
          width: "",
          label: $T("最高电压(V)"),
          sortable: false,
          headerAlign: "center",
          align: "left",
          showOverflowTooltip: true,
          formatter: (row, column, cellValue) =>
            common.setNumFixed(cellValue, 2)
        },
        {
          prop: "minVoltage",
          //   minWidth: 190,
          width: "",
          label: $T("最低电压(V)"),
          sortable: false,
          headerAlign: "center",
          align: "left",
          showOverflowTooltip: true,
          formatter: (row, column, cellValue) =>
            common.setNumFixed(cellValue, 2)
        }
      ],
      cardData: {},
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: "电压合格率报表",
        width: mhCONST("dialogSize.large"),
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },

      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // 关键字检索
      ElInput_keyword: {
        value: "",
        placeholder: "请输入关键字检索",
        prefixIcon: "el-icon-search",
        style: {
          width: "250px"
        },
        event: {
          change: this.ElInput_keyword_change_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.tableData = [];
      this.currentPage = 1;
      this.pageSize = 50;
      this.total = 0;
      this.ElInput_keyword.value = "";
      this.cardData = {};
      this.queryReportByIndex();
      this.queryReportByTable();
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {},
    inputData_in(val) {}
  },
  methods: {
    get(...arg) {
      return common.get(...arg);
    },
    setNumFixed(...arg) {
      return common.setNumFixed(...arg);
    },
    ElInput_keyword_change_out(val) {
      this.currentPage = 1;
      this.queryReportByTable();
    },
    queryReportByIndex() {
      if (!this.inputData_in?.simulationId) return;
      customApi.viewReportByIndex(this.inputData_in?.simulationId).then(res => {
        this.cardData = common.get(res, "data", {});
      });
    },
    queryReportByTable() {
      let params = {
        keyword: this.ElInput_keyword.value,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        simulationId: this.inputData_in.simulationId
      };
      customApi.viewReportByTable(params).then(res => {
        this.tableData = common.get(res, "data", []);
        this.total = common.get(res, "total", 0);
      });
    },

    // 每页条数改变时触发
    handleSizeChange(val) {
      this.currentPage = 1;
      this.queryReportByTable();
    },
    // 当前页改变时触发
    handleCurrentChange(val) {
      this.queryReportByTable();
    },
    headerCellStyle({ column, rowIndex }) {
      return {
        background: "#f5f7fa"
      };
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    no() {}
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.viewReport ::v-deep .el-dialog {
  margin-top: 5vh !important;
}
::v-deep .el-dialog__body {
  padding: 15px 20px !important;
}
::v-deep .el-divider--vertical {
  height: 30px;
}
</style>
