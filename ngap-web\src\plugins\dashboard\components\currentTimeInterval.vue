<template>
  <div class="page">
    <div class="flex fullheight">
      <input-wrap label="查询时段">
        <ElSelect
          v-model="ElSelect_type.value"
          v-bind="ElSelect_type"
          v-on="ElSelect_type.event"
        >
          <ElOption
            v-for="item in ElOption_type.options_in"
            :key="item[ElOption_type.key]"
            :label="item[ElOption_type.label]"
            :value="item[ElOption_type.value]"
            :disabled="item[ElOption_type.disabled]"
          ></ElOption>
        </ElSelect>
      </input-wrap>

      <el-checkbox v-model="checked" @change="changeOut" class="ml16">
        定时刷新
      </el-checkbox>
      <input-wrap v-if="checked" label="刷新间隔" class="ml8">
        <ElSelect
          v-model="ElSelect_inteval.value"
          v-bind="ElSelect_inteval"
          v-on="ElSelect_inteval.event"
        >
          <ElOption
            v-for="item in ElOption_inteval.options_in"
            :key="item[ElOption_inteval.key]"
            :label="item[ElOption_inteval.label]"
            :value="item[ElOption_inteval.value]"
            :disabled="item[ElOption_inteval.disabled]"
          ></ElOption>
        </ElSelect>
      </input-wrap>
    </div>
  </div>
</template>

<script>
import common from "@/utils/common";
export default {
  name: "currentTimeInterval",
  components: {},
  props: {
    queryObj: {
      type: Object
    }
  },
  data() {
    return {
      checked: true,
      // type组件
      ElSelect_type: {
        value: 14,
        size: "small",
        style: {
          width: "80px"
        },
        event: {
          change: this.changeOut
        }
      },
      // type组件
      ElOption_type: {
        options_in: [
          {
            label: "当日",
            value: 12
          },
          {
            label: "当月",
            value: 14
          },
          {
            label: "当年",
            value: 17
          }
        ],
        key: "value",
        value: "value",
        label: "label",
        disabled: "disabled"
      },
      // inteval组件
      ElSelect_inteval: {
        value: 30 * 60 * 1000,
        size: "small",
        style: {
          width: "100px"
        },
        event: {
          change: this.changeOut
        }
      },
      // inteval组件
      ElOption_inteval: {
        options_in: [
          {
            label: "5秒",
            value: 5000
          },
          {
            label: "30秒",
            value: 30 * 1000
          },
          {
            label: "1分钟",
            value: 60 * 1000
          },
          {
            label: "5分钟",
            value: 5 * 60 * 1000
          },

          {
            label: "10分钟",
            value: 10 * 60 * 1000
          },
          {
            label: "15分钟",
            value: 15 * 60 * 1000
          },
          {
            label: "30分钟",
            value: 30 * 60 * 1000
          },
          {
            label: "1小时",
            value: 60 * 60 * 1000
          },
          {
            label: "2小时",
            value: 120 * 60 * 1000
          }
        ],
        key: "value",
        value: "value",
        label: "label",
        disabled: "disabled"
      }
    };
  },
  watch: {
    queryObj(val) {
      console.log(val, "queryObjqueryObjqueryObj");
    }
  },
  methods: {
    changeOut() {
      let params = {
        aggregationCycle: this.ElSelect_type.value,
        isInterval: this.checked,
        inteval: this.ElSelect_inteval.value
      };
      this.$emit("changeParams", params);
    },
    initParams() {
      this.ElSelect_type.value = common.get(
        this.queryObj,
        "aggregationCycle",
        this.ElSelect_type.value
      );
      this.checked = common.get(this.queryObj, "isInterval", this.checked);
      this.ElSelect_inteval.value = common.get(
        this.queryObj,
        "inteval",
        this.ElSelect_inteval.value
      );
    }
  },
  mounted() {
    this.initParams();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
