package com.cet.electric.ngapserver.util;

import com.cet.electric.ngapserver.dto.ErrorMsg;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

public class JsonUtilsTest {

    private File tempDir;
    private File testJsonFile;

    @Before
    public void setUp() throws IOException {
        // 创建临时目录和文件用于测试
        tempDir = Files.createTempDirectory("jsonutils_test").toFile();
        testJsonFile = new File(tempDir, "test.json");
    }

    @After
    public void tearDown() throws IOException {
        // 清理测试文件和目录
        if (testJsonFile.exists()) {
            testJsonFile.delete();
        }
        if (tempDir.exists()) {
            tempDir.delete();
        }
    }

    @Test
    public void testConvertObjectToJsonString_SimpleObject() {
        // 准备测试数据
        Map<String, Object> testObject = new HashMap<>();
        testObject.put("name", "测试");
        testObject.put("age", 25);
        testObject.put("active", true);

        // 执行测试
        String jsonString = JsonUtils.convertObjectToJsonString(testObject);

        // 验证结果
        assertNotNull(jsonString);
        assertTrue(jsonString.contains("\"name\""));
        assertTrue(jsonString.contains("\"测试\""));
        assertTrue(jsonString.contains("\"age\""));
        assertTrue(jsonString.contains("25"));
        assertTrue(jsonString.contains("\"active\""));
        assertTrue(jsonString.contains("true"));
    }

    @Test
    public void testConvertObjectToJsonString_NullObject() {
        // 执行测试
        String jsonString = JsonUtils.convertObjectToJsonString(null);

        // 验证结果
        assertNotNull(jsonString);
        assertEquals("null", jsonString);
    }

    @Test
    public void testConvertObjectToJsonString_EmptyMap() {
        // 准备测试数据
        Map<String, Object> emptyMap = new HashMap<>();

        // 执行测试
        String jsonString = JsonUtils.convertObjectToJsonString(emptyMap);

        // 验证结果
        assertNotNull(jsonString);
        assertEquals("{ }", jsonString);
    }

    @Test
    public void testConvertObjectToJsonString_NestedObject() {
        // 准备测试数据
        Map<String, Object> nestedObject = new HashMap<>();
        Map<String, Object> innerObject = new HashMap<>();
        innerObject.put("innerKey", "innerValue");
        nestedObject.put("outer", innerObject);
        nestedObject.put("simple", "value");

        // 执行测试
        String jsonString = JsonUtils.convertObjectToJsonString(nestedObject);

        // 验证结果
        assertNotNull(jsonString);
        assertTrue(jsonString.contains("\"outer\""));
        assertTrue(jsonString.contains("\"innerKey\""));
        assertTrue(jsonString.contains("\"innerValue\""));
        assertTrue(jsonString.contains("\"simple\""));
        assertTrue(jsonString.contains("\"value\""));
    }

    @Test
    public void testConvertJsonFileToMap_ValidJsonFile() throws IOException {
        // 准备测试数据 - 创建有效的JSON文件
        String jsonContent = "{\"name\":\"测试项目\",\"type\":\"General_scenario\",\"id\":1}";
        try (FileWriter writer = new FileWriter(testJsonFile)) {
            writer.write(jsonContent);
        }

        // 执行测试
        Map<String, Object> result = JsonUtils.convertJsonFileToMap(testJsonFile);

        // 验证结果
        assertNotNull(result);
        assertEquals("测试项目", result.get("name"));
        assertEquals("General_scenario", result.get("type"));
        assertEquals(1, result.get("id"));
    }

    @Test
    public void testConvertJsonFileToMap_EmptyJsonFile() throws IOException {
        // 准备测试数据 - 创建空的JSON对象文件
        String jsonContent = "{}";
        try (FileWriter writer = new FileWriter(testJsonFile)) {
            writer.write(jsonContent);
        }

        // 执行测试
        Map<String, Object> result = JsonUtils.convertJsonFileToMap(testJsonFile);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertJsonFileToMap_NestedJsonFile() throws IOException {
        // 准备测试数据 - 创建嵌套的JSON文件
        String jsonContent = "{\"project\":{\"name\":\"嵌套项目\",\"settings\":{\"enabled\":true,\"count\":5}},\"version\":\"1.0\"}";
        try (FileWriter writer = new FileWriter(testJsonFile)) {
            writer.write(jsonContent);
        }

        // 执行测试
        Map<String, Object> result = JsonUtils.convertJsonFileToMap(testJsonFile);

        // 验证结果
        assertNotNull(result);
        assertEquals("1.0", result.get("version"));
        
        @SuppressWarnings("unchecked")
        Map<String, Object> project = (Map<String, Object>) result.get("project");
        assertNotNull(project);
        assertEquals("嵌套项目", project.get("name"));
        
        @SuppressWarnings("unchecked")
        Map<String, Object> settings = (Map<String, Object>) project.get("settings");
        assertNotNull(settings);
        assertEquals(true, settings.get("enabled"));
        assertEquals(5, settings.get("count"));
    }

    @Test(expected = ErrorMsg.class)
    public void testConvertJsonFileToMap_InvalidJsonFile() throws IOException {
        // 准备测试数据 - 创建无效的JSON文件
        String invalidJsonContent = "{\"name\":\"测试\",\"invalid\":}";
        try (FileWriter writer = new FileWriter(testJsonFile)) {
            writer.write(invalidJsonContent);
        }

        // 执行测试 - 应该抛出ErrorMsg异常
        JsonUtils.convertJsonFileToMap(testJsonFile);
    }

    @Test(expected = ErrorMsg.class)
    public void testConvertJsonFileToMap_NonExistentFile() {
        // 准备测试数据 - 不存在的文件
        File nonExistentFile = new File(tempDir, "non_existent.json");

        // 执行测试 - 应该抛出ErrorMsg异常
        JsonUtils.convertJsonFileToMap(nonExistentFile);
    }

    @Test
    public void testConvertJsonFileToMap_JsonWithSpecialCharacters() throws IOException {
        // 准备测试数据 - 包含特殊字符的JSON
        String jsonContent = "{\"name\":\"测试\\\"项目\",\"path\":\"/path/to/file\",\"description\":\"包含\\n换行符\"}";
        try (FileWriter writer = new FileWriter(testJsonFile)) {
            writer.write(jsonContent);
        }

        // 执行测试
        Map<String, Object> result = JsonUtils.convertJsonFileToMap(testJsonFile);

        // 验证结果
        assertNotNull(result);
        assertEquals("测试\"项目", result.get("name"));
        assertEquals("/path/to/file", result.get("path"));
        assertEquals("包含\n换行符", result.get("description"));
    }

    @Test
    public void testConvertJsonFileToMap_JsonWithArrays() throws IOException {
        // 准备测试数据 - 包含数组的JSON
        String jsonContent = "{\"name\":\"数组测试\",\"tags\":[\"tag1\",\"tag2\",\"tag3\"],\"numbers\":[1,2,3]}";
        try (FileWriter writer = new FileWriter(testJsonFile)) {
            writer.write(jsonContent);
        }

        // 执行测试
        Map<String, Object> result = JsonUtils.convertJsonFileToMap(testJsonFile);

        // 验证结果
        assertNotNull(result);
        assertEquals("数组测试", result.get("name"));
        
        @SuppressWarnings("unchecked")
        java.util.List<String> tags = (java.util.List<String>) result.get("tags");
        assertNotNull(tags);
        assertEquals(3, tags.size());
        assertTrue(tags.contains("tag1"));
        assertTrue(tags.contains("tag2"));
        assertTrue(tags.contains("tag3"));
        
        @SuppressWarnings("unchecked")
        java.util.List<Integer> numbers = (java.util.List<Integer>) result.get("numbers");
        assertNotNull(numbers);
        assertEquals(3, numbers.size());
        assertTrue(numbers.contains(1));
        assertTrue(numbers.contains(2));
        assertTrue(numbers.contains(3));
    }

    @Test
    public void testConvertObjectToJsonString_WithNullValues() {
        // 准备测试数据 - 包含null值的对象
        Map<String, Object> objectWithNulls = new HashMap<>();
        objectWithNulls.put("name", "测试");
        objectWithNulls.put("nullValue", null);
        objectWithNulls.put("emptyString", "");

        // 执行测试
        String jsonString = JsonUtils.convertObjectToJsonString(objectWithNulls);

        // 验证结果
        assertNotNull(jsonString);
        assertTrue(jsonString.contains("\"name\""));
        assertTrue(jsonString.contains("\"测试\""));
        assertTrue(jsonString.contains("\"nullValue\""));
        assertTrue(jsonString.contains("null"));
        assertTrue(jsonString.contains("\"emptyString\""));
        assertTrue(jsonString.contains("\"\""));
    }

    @Test
    public void testJsonRoundTrip() throws IOException {
        // 准备测试数据
        Map<String, Object> originalObject = new HashMap<>();
        originalObject.put("projectName", "往返测试项目");
        originalObject.put("projectType", "General_scenario");
        originalObject.put("projectId", 123L);
        originalObject.put("isActive", true);

        // 对象转JSON字符串
        String jsonString = JsonUtils.convertObjectToJsonString(originalObject);

        // JSON字符串写入文件
        try (FileWriter writer = new FileWriter(testJsonFile)) {
            writer.write(jsonString);
        }

        // 从文件读取并转换回Map
        Map<String, Object> resultObject = JsonUtils.convertJsonFileToMap(testJsonFile);

        // 验证往返转换的一致性
        assertNotNull(resultObject);
        assertEquals(originalObject.get("projectName"), resultObject.get("projectName"));
        assertEquals(originalObject.get("projectType"), resultObject.get("projectType"));
        assertEquals(originalObject.get("isActive"), resultObject.get("isActive"));
        // 注意：JSON中的数字可能会被解析为不同的类型，所以需要特殊处理
        assertEquals(originalObject.get("projectId").toString(), resultObject.get("projectId").toString());
    }
}
