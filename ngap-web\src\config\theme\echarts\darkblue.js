export default {
  seriesCnt: "20",
  titleColor: "#fafafa",
  subtitleColor: "#f6f6f6",
  textColorShow: false,
  textColor: "#333",
  markTextColor: "#f6f6f6",
  color: [
    "#6860ff",
    "#fb923c",
    "#f43f5e",
    "#14b8a6",
    "#d946ef",
    "#facc15",
    "#a3e635",
    "#22d3ee",
    "#818cf8",
    "#f472b6",
    "#198754",
    "#64748b",
    "#fba44b",
    "#ff671a",
    "#fe6b6b",
    "#ba82e5",
    "#8471dd",
    "#5671e2",
    "#83afda",
    "#19aaaa"
  ],
  borderColor: "#ccc",
  borderWidth: "0",
  visualMapColor: ["#f14444", "#d88273", "#f6efa6"],
  legendTextColor: "#f6f6f6",
  kColor: "#eb5454",
  kColor0: "#47b262",
  kBorderColor: "#eb5454",
  kBorderColor0: "#47b262",
  kBorderWidth: 1,
  lineWidth: 2,
  symbolSize: 4,
  symbol: "emptyCircle",
  symbolBorderWidth: 1,
  lineSmooth: false,
  graphLineWidth: 1,
  graphLineColor: "#456cdd",
  mapLabelColor: "#000",
  mapLabelColorE: "rgb(100,0,0)",
  mapBorderColor: "#444",
  mapBorderColorE: "#444",
  mapBorderWidth: 0.5,
  mapBorderWidthE: 1,
  mapAreaColor: "#eee",
  mapAreaColorE: "rgba(255,215,0,0.8)",
  axes: [
    {
      type: "all",
      axisLineShow: true,
      axisLineColor: "#a0a0a7",
      axisTickShow: true,
      axisTickColor: "#a0a0a7",
      axisLabelShow: true,
      axisLabelColor: "#f6f6f6",
      splitLineShow: false,
      splitLineColor: ["#E0E6F1"],
      splitAreaShow: false,
      splitAreaColor: ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]
    },
    {
      type: "category",
      axisLineShow: true,
      axisLineColor: "#6E7079",
      axisTickShow: true,
      axisTickColor: "#6E7079",
      axisLabelShow: true,
      axisLabelColor: "#d5d7da",
      splitLineShow: false,
      splitLineColor: ["#E0E6F1"],
      splitAreaShow: false,
      splitAreaColor: ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]
    },
    {
      type: "value",
      axisLineShow: true,
      axisLineColor: "#6E7079",
      axisTickShow: true,
      axisTickColor: "#6E7079",
      axisLabelShow: true,
      axisLabelColor: "#d5d7da",
      splitLineShow: false,
      splitLineColor: ["#6e7079"],
      splitAreaShow: false,
      splitAreaColor: ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]
    },
    {
      type: "log",
      axisLineShow: true,
      axisLineColor: "#6e7079",
      axisTickShow: true,
      axisTickColor: "#6e7079",
      axisLabelShow: true,
      axisLabelColor: "#d5d7da",
      splitLineShow: false,
      splitLineColor: ["#ff0000"],
      splitAreaShow: false,
      splitAreaColor: ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]
    },
    {
      type: "time",
      axisLineShow: true,
      axisLineColor: "#6e7079",
      axisTickShow: true,
      axisTickColor: "#6e7079",
      axisLabelShow: true,
      axisLabelColor: "#d5d7da",
      splitLineShow: false,
      splitLineColor: ["#E0E6F1"],
      splitAreaShow: false,
      splitAreaColor: ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]
    }
  ],
  axisSeperateSetting: false,
  toolboxColor: "#f6f6f6",
  toolboxEmphasisColor: "#efefef",
  tooltipAxisColor: "#f6f6f6",
  tooltipAxisWidth: 1,
  timelineLineColor: "#8a9fea",
  timelineLineWidth: "2",
  timelineItemColor: "#a3cbf3",
  timelineItemColorE: "#5ba7f1",
  timelineCheckColor: "#1087ff",
  timelineCheckBorderColor: "#f6f6f6",
  timelineItemBorderWidth: "1",
  timelineControlColor: "#1087ff",
  timelineControlBorderColor: "#1087ff",
  timelineControlBorderWidth: 1,
  timelineLabelColor: "#f6f6f6",
  textStyle: {},
  title: {
    textStyle: {
      color: "#f0f1f2"
    },
    subtextStyle: {
      color: "#e6e8ea"
    }
  },
  line: {
    itemStyle: {
      borderWidth: 1
    },
    lineStyle: {
      width: 2
    },
    symbolSize: 4,
    symbol: "emptyCircle",
    smooth: false
  },
  radar: {
    itemStyle: {
      borderWidth: 1
    },
    lineStyle: {
      width: 2
    },
    symbolSize: 4,
    symbol: "emptyCircle",
    smooth: false
  },
  bar: {
    label: {
      color: "#eee",
      textBorderWidth: "0"
    },
    itemStyle: {
      barBorderWidth: "0",
      barBorderColor: "#ccc"
    },
    barMaxWidth: 30
  },
  pie: {
    itemStyle: {
      borderWidth: "0",
      borderColor: "#ccc"
    },
    label: {
      color: "#e6e8ea"
    }
  },
  scatter: {
    itemStyle: {
      borderWidth: "0",
      borderColor: "#ccc"
    }
  },
  boxplot: {
    itemStyle: {
      borderWidth: "0",
      borderColor: "#ccc"
    }
  },
  parallel: {
    itemStyle: {
      borderWidth: "0",
      borderColor: "#ccc"
    }
  },
  sankey: {
    itemStyle: {
      borderWidth: "0",
      borderColor: "#ccc"
    }
  },
  funnel: {
    itemStyle: {
      borderWidth: "0",
      borderColor: "#ccc"
    }
  },
  gauge: {
    axisLabel: {
      color: "#f5f5f5"
    },
    detail: {
      color: "#f5f5f5"
    },
    title: {
      color: "#f5f5f5"
    }
  },
  candlestick: {
    itemStyle: {
      color: "#eb5454",
      color0: "#47b262",
      borderColor: "#eb5454",
      borderColor0: "#47b262",
      borderWidth: 1
    }
  },
  graph: {
    itemStyle: {
      borderWidth: "0",
      borderColor: "#ccc"
    },
    lineStyle: {
      width: 1,
      color: "#6e7079"
    },
    symbolSize: 4,
    symbol: "emptyCircle",
    smooth: false,
    color: [
      "#0d86ff",
      "#19e9e9",
      "#26ec86",
      "#2485c0",
      "#8680f6",
      "#5cbbeb",
      "#659ded",
      "#e8e392",
      "#ffc83a",
      "#7befa3",
      "#84f4fa",
      "#116dd3",
      "#fba44b",
      "#e46262",
      "#9644d6",
      "#ba82e5",
      "#8471dd",
      "#5671e2",
      "#83afda",
      "#19aaaa"
    ],
    label: {
      color: "#e6e8ea"
    }
  },
  map: {
    itemStyle: {
      areaColor: "#eee",
      borderColor: "#444",
      borderWidth: 0.5
    },
    label: {
      color: "#000"
    },
    emphasis: {
      itemStyle: {
        areaColor: "rgba(255,215,0,0.8)",
        borderColor: "#444",
        borderWidth: 1
      },
      label: {
        color: "rgb(100,0,0)"
      }
    }
  },
  geo: {
    itemStyle: {
      areaColor: "#eee",
      borderColor: "#444",
      borderWidth: 0.5
    },
    label: {
      color: "#000"
    },
    emphasis: {
      itemStyle: {
        areaColor: "rgba(255,215,0,0.8)",
        borderColor: "#444",
        borderWidth: 1
      },
      label: {
        color: "rgb(100,0,0)"
      }
    }
  },
  categoryAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: "#e6e8ea"
      }
    },
    axisTick: {
      show: true,
      lineStyle: {
        color: "#e6e8ea"
      }
    },
    axisLabel: {
      show: true,
      color: "#e6e8ea"
    },
    splitLine: {
      show: false,
      lineStyle: {
        color: ["#E0E6F1"]
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]
      }
    }
  },
  valueAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: "#e6e8ea"
      }
    },
    axisTick: {
      show: true,
      lineStyle: {
        color: "#e6e8ea"
      }
    },
    axisLabel: {
      show: true,
      color: "#e6e8ea"
    },
    splitLine: {
      show: false,
      lineStyle: {
        color: ["#E0E6F1"]
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]
      }
    }
  },
  logAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: "#e6e8ea"
      }
    },
    axisTick: {
      show: true,
      lineStyle: {
        color: "#e6e8ea"
      }
    },
    axisLabel: {
      show: true,
      color: "#e6e8ea"
    },
    splitLine: {
      show: false,
      lineStyle: {
        color: ["#E0E6F1"]
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]
      }
    }
  },
  timeAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: "#e6e8ea"
      }
    },
    axisTick: {
      show: true,
      lineStyle: {
        color: "#e6e8ea"
      }
    },
    axisLabel: {
      show: true,
      color: "#e6e8ea"
    },
    splitLine: {
      show: false,
      lineStyle: {
        color: ["#E0E6F1"]
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]
      }
    }
  },
  toolbox: {
    iconStyle: {
      borderColor: "#e6e8e9"
    },
    emphasis: {
      iconStyle: {
        borderColor: "#ced2de"
      }
    },
    feature: {
      saveAsImage: {
        backgroundColor: "#1f3087"
      }
    }
  },
  legend: {
    textStyle: {
      color: "#e6e8ea"
    },
    pageTextStyle: {
      color: "#eee"
    },
    pageIconColor: "rgba(209, 214, 217, 1)",
    pageIconInactiveColor: "rgba(133, 162, 181, 1)"
  },
  tooltip: {
    axisPointer: {
      lineStyle: {
        color: "#f0f1ff",
        width: 1
      },
      crossStyle: {
        color: "#f0f1ff",
        width: 1
      }
    }
  },
  timeline: {
    lineStyle: {
      color: "#8abaea",
      width: "2"
    },
    itemStyle: {
      color: "#a3cbf3",
      borderWidth: "1"
    },
    controlStyle: {
      color: "#0d86ff",
      borderColor: "#0d86ff",
      borderWidth: "1"
    },
    checkpointStyle: {
      color: "#0d86ff",
      borderColor: "#0d86ff"
    },
    label: {
      color: "#e6e8ea"
    },
    emphasis: {
      itemStyle: {
        color: "#5ba7f1"
      },
      controlStyle: {
        color: "#0d86ff",
        borderColor: "#0d86ff",
        borderWidth: "1"
      },
      label: {
        color: "#e6e8ea"
      }
    }
  },
  visualMap: {
    color: ["#bf444c", "#d88273", "#f6efa6"],
    textStyle: {
      color: "#f5f5f5"
    }
  },
  dataZoom: {
    handleSize: "undefined%",
    textStyle: {}
  },
  markPoint: {
    label: {
      color: "#e6e8ea"
    },
    emphasis: {
      label: {
        color: "#e6e8ea"
      }
    }
  }
};
