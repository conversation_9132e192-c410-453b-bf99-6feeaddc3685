package com.cet.electric.ngapserver.service.impl;

import com.cet.electric.ngapserver.dao.LineCodeDao;
import com.cet.electric.ngapserver.dto.ErrorMsg;
import com.cet.electric.ngapserver.entity.LineCode;
import com.cet.electric.ngapserver.service.LineCodeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class LineCodeServiceImpl implements LineCodeService {

    private static final Logger log = LoggerFactory.getLogger(LineCodeServiceImpl.class);

    private final Object lineCodeLock = new Object();

    @Autowired
    private LineCodeDao lineCodeDao;

    @Override
    public LineCode createLineCode(LineCode lineCode) {
        synchronized (lineCodeLock) {
            // 检查线路代码是否已存在
            if (lineCode.getLineCode() != null && !lineCode.getLineCode().isEmpty()) {
                LineCode existingLineCodeByCode = lineCodeDao.findByLineCode(lineCode.getLineCode().trim());
                if (existingLineCodeByCode != null) {
                    log.error("线路代码创建失败：线路代码已存在: {}", lineCode.getLineCode());
                    throw new ErrorMsg(-1, "线路代码创建失败：线路代码已存在.");
                }
            }
            // 保存线路代码
            int result = lineCodeDao.createLineCode(lineCode);
            if (result <= 0) {
                log.error("线路代码创建失败: {}", lineCode);
                throw new ErrorMsg(-1, "数据库中线路代码创建失败.");
            }
            log.info("线路代码创建成功: {}", lineCode);
            // 返回创建的线路代码
            return lineCode;
        }
    }

    @Override
    public List<LineCode> getLineCodes(Integer page, Integer size, String sortBy, String sortOrder, String keyword) {
        log.info("开始查询线路代码列表, page: {}, size: {}, sortBy: {}, sortOrder: {}, keyword: {}",
                page, size, sortBy, sortOrder, keyword);

        // 参数校验
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }

        // 验证排序字段
        List<String> validSortFields = Arrays.asList("lineCodeId", "lineCodeName", "lineCode");
        if (sortBy == null || !validSortFields.contains(sortBy)) {
            log.warn("无效的排序字段: {}，使用默认排序字段 lineCodeId", sortBy);
            sortBy = "lineCodeId";
        }

        // 验证排序方向
        if ((!"asc".equalsIgnoreCase(sortOrder) && !"desc".equalsIgnoreCase(sortOrder))) {
            log.warn("无效的排序方向: {}，使用默认排序方向 desc", sortOrder);
            sortOrder = "desc";
        }

        // 计算分页参数
        int offset = (page - 1) * size;

        // 查询数据列表
        List<LineCode> lineCodes = lineCodeDao.getLineCodes(offset, size, sortBy, sortOrder, keyword);

        log.info("查询线路代码列表成功，查询到 {} 条数据", lineCodes.size());

        return lineCodes;
    }

    @Override
    public Long countLineCodes(String keyword) {
        log.info("开始查询线路代码总数, keyword: {}", keyword);

        // 调用DAO层查询总数
        Long count = lineCodeDao.countLineCodes(keyword);

        log.info("查询线路代码总数完成，共 {} 条记录", count);
        return count;
    }

    @Override
    public LineCode getLineCodeById(Long lineCodeId) {
        if (lineCodeId == null) {
            log.error("查询线路代码失败：ID不能为空");
            throw new ErrorMsg(-1, "查询线路代码失败：ID不能为空.");
        }

        LineCode lineCode = lineCodeDao.findById(lineCodeId);
        if (lineCode == null) {
            log.error("查询线路代码失败：指定ID的线路代码不存在: {}", lineCodeId);
            throw new ErrorMsg(-1, "查询线路代码失败：指定ID的线路代码不存在.");
        }

        log.info("查询线路代码成功: {}", lineCode);
        return lineCode;
    }

    @Override
    public LineCode updateLineCode(LineCode lineCode) {
        synchronized (lineCodeLock) {
            if (lineCode.getLineCodeId() == null) {
                log.error("线路代码更新失败：ID不能为空");
                throw new ErrorMsg(-1, "线路代码更新失败：ID不能为空.");
            }
            // 获取当前数据库中的线路代码信息
            LineCode existingLineCode = lineCodeDao.findById(lineCode.getLineCodeId());
            if (existingLineCode == null) {
                log.error("线路代码更新失败：指定ID的线路代码不存在: {}", lineCode.getLineCodeId());
                throw new ErrorMsg(-1, "线路代码更新失败：指定ID的线路代码不存在.");
            }

            // 更新线路代码
            int result = lineCodeDao.updateLineCode(lineCode);
            if (result <= 0) {
                log.error("线路代码更新失败: {}", lineCode);
                throw new ErrorMsg(-1, "数据库中线路代码更新失败.");
            }
            log.info("线路代码更新成功: {}", lineCode);
            // 返回更新后的线路代码
            return lineCodeDao.findById(lineCode.getLineCodeId());
        }
    }

    @Override
    public boolean deleteLineCode(Long lineCodeId) {
        if (lineCodeId == null) {
            log.error("线路代码删除失败：ID不能为空");
            throw new ErrorMsg(-1, "线路代码删除失败：ID不能为空.");
        }

        // 获取当前数据库中的线路代码信息
        LineCode existingLineCode = lineCodeDao.findById(lineCodeId);
        if (existingLineCode == null) {
            log.error("线路代码删除失败：指定ID的线路代码不存在: {}", lineCodeId);
            throw new ErrorMsg(-1, "线路代码删除失败：指定ID的线路代码不存在.");
        }
        synchronized (lineCodeLock) {

            // 删除线路代码
            int result = lineCodeDao.deleteLineCode(lineCodeId);
            boolean success = result > 0;

            if (success) {
                log.info("线路代码删除成功: {}", lineCodeId);
            } else {
                log.error("线路代码删除失败: {}", lineCodeId);
            }

            return success;
        }
    }
}
