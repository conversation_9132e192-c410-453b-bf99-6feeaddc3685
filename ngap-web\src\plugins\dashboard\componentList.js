/*
 * @Author: your name
 * @Date: 2021-08-04 15:30:24
 * @LastEditTime: 2023-11-21 16:33:17
 * @LastEditors: soft\luwei <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: \frame\src\plugins\dashboard\componentList.js
 */
/**
 * dashboard预定义组件定义, 插件化运行
 * id为唯一标识
 * name为展示名称
 * viewCmp为显示组件
 * cfgCmp为配置组件
 * route可以配置组件跳转到的功能页面路由
 * paltform 配置是否属于平台的预定义组件, 和用于项目的预定义组件区分
 */
export default [
  // {
  //   id: "energyTrend",
  //   viewCmp: () => import(`./energyTrend/view`),
  //   cfgCmp: () => import(`./energyTrend/config`),
  //   name: "能耗趋势",
  //   route: "/energyOverview",
  //   tag: "能耗相关"
  // }
];
