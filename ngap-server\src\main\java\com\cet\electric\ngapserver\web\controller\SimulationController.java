package com.cet.electric.ngapserver.web.controller;

import com.cet.electric.ngapserver.dto.*;
import com.cet.electric.ngapserver.entity.*;
import com.cet.electric.ngapserver.service.SimulationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@Api(value = "SimulationController API", tags = "Simulation")
@RequestMapping("/ngap-server/api/simulations")
public class SimulationController {

    private static final Logger log = LoggerFactory.getLogger(SimulationController.class);

    @Autowired
    private SimulationService simulationService;

    @PostMapping("/query")
    @ApiOperation(value = "分页查询仿真任务列表")
    public ResponseDTO<List<Simulation>> getSimulations(@RequestBody SimulationQueryDTO queryDTO) {
        log.info("接收到分页查询仿真任务请求: {}", queryDTO);

        // 从DTO中提取参数
        Integer page = queryDTO.getPage();
        Integer size = queryDTO.getSize();
        String sortBy = queryDTO.getSortBy();
        String sortOrder = queryDTO.getSortOrder();
        Long projectId = queryDTO.getProjectId();
        String keyword = queryDTO.getKeyword();
        Long startTime = queryDTO.getStartTime();
        Long endTime = queryDTO.getEndTime();

        // 调用原有服务方法进行查询
        List<Simulation> simulations = simulationService.getSimulations(page, size, sortBy, sortOrder, projectId, keyword, startTime, endTime);
        // 获取总数
        Long total = simulationService.countSimulations(projectId, keyword, startTime, endTime);
        log.info("仿真任务查询成功，总数量: {}", total);
        return new ResponseDTO<>(0, "查询成功", simulations, total);
    }

    @PostMapping
    @ApiOperation(value = "创建仿真任务")
    public ResponseDTO<Simulation> createSimulation(
            @RequestParam(value = "projectId") Long projectId,
            @RequestParam(value = "simulationName") String simulationName) {

        log.info("接收到创建仿真任务请求: projectId={}, simulationName={}", projectId, simulationName);

        // 创建仿真任务实体
        Simulation simulation = Simulation.builder()
                .projectId(projectId)
                .simulationName(simulationName.trim())
                .createdAt(System.currentTimeMillis())
                .updatedAt(System.currentTimeMillis())
                .isDeleted(0)
                .build();

        Simulation result = simulationService.createSimulation(simulation);

        log.info("仿真任务创建成功，ID: {}", result.getSimulationId());
        return new ResponseDTO<>(0, "仿真任务创建成功", result);
    }

    @PutMapping("/{simulationId}/rename")
    @ApiOperation(value = "重命名仿真任务")
    public ResponseDTO<Simulation> renameSimulation(
            @PathVariable(value = "simulationId") Long simulationId,
            @RequestParam(value = "newName") String newName) {
        log.info("接收到重命名仿真任务请求: simulationId={}, newName={}", simulationId, newName);
        // 调用服务进行重命名
        Simulation result = simulationService.renameSimulation(simulationId, newName);

        log.info("仿真任务重命名成功，ID: {}, 新名称: {}", simulationId, result.getSimulationName());
        return new ResponseDTO<>(0, "仿真任务重命名成功", result);
    }

    @PutMapping("/{simulationId}/update")
    @ApiOperation(value = "更新仿真任务")
    public ResponseDTO<Simulation> updateSimulation(
            @PathVariable(value = "simulationId") Long simulationId,
            @RequestBody SimulationUpdateDTO simulationUpdateDTO) {

        String simulationModel = simulationUpdateDTO.getSimulationModel();
        String simulationDraft = simulationUpdateDTO.getSimulationDraft();
        String simulationScript = simulationUpdateDTO.getSimulationScript().getSimulationScript();
        String nodes = simulationUpdateDTO.getSimulationScript().getNodes();
        String controlStrategy = simulationUpdateDTO.getControlStrategy();

        log.info("接收到更新仿真任务请求: simulationId={}, simulationModel={}, simulationDraft={}, simulationScript={}, nodes={}, controlStrategy={}",
                simulationId, simulationModel, simulationDraft, simulationScript, nodes, controlStrategy);

        // 调用服务进行更新
        Simulation result = simulationService.updateSimulation(simulationId, simulationModel, simulationDraft, simulationScript, nodes, controlStrategy);

        log.info("仿真任务更新成功，ID: {}, 更新后模型: {}, 更新后脚本: {}",
                simulationId, result.getSimulationModel(), result.getSimulationScript());
        return new ResponseDTO<>(0, "仿真任务更新成功", result);
    }


    @DeleteMapping("/{simulationId}")
    @ApiOperation(value = "删除仿真任务")
    public ResponseDTO<Void> deleteSimulation(
            @PathVariable(value = "simulationId") Long simulationId) {
        log.info("接收到删除仿真任务请求: simulationId={}", simulationId);
        // 调用服务进行删除
        simulationService.deleteSimulation(simulationId);

        log.info("仿真任务删除成功，ID: {}", simulationId);
        return new ResponseDTO<>(0, "仿真任务删除成功", null);
    }

    @GetMapping("/{simulationId}")
    @ApiOperation(value = "获取仿真任务详情")
    public ResponseDTO<Simulation> getSimulation(
            @PathVariable(value = "simulationId") Long simulationId) {
        log.info("接收到获取仿真任务详情请求: simulationId={}", simulationId);
        // 调用服务获取详情
        Simulation result = simulationService.getSimulationById(simulationId);

        log.info("获取仿真任务详情成功，ID: {}", simulationId);
        return new ResponseDTO<>(0, "获取仿真任务详情成功", result);
    }

    @PostMapping("/{simulationId}/copy")
    @ApiOperation(value = "复制仿真任务")
    public ResponseDTO<Simulation> copySimulation(
            @PathVariable(value = "simulationId") Long simulationId) {
        log.info("接收到复制仿真任务请求: simulationId={}", simulationId);
        // 调用服务执行复制
        Simulation newSimulation = simulationService.copySimulation(simulationId);

        log.info("仿真任务复制成功，新ID: {}", newSimulation.getSimulationId());
        return new ResponseDTO<>(0, "仿真任务复制成功", newSimulation);
    }

    @PostMapping("/{simulationId}/upload")
    @ApiOperation(value = "上传文件")
    public ResponseDTO<String> uploadFile(
            @PathVariable(value = "simulationId") Long simulationId,
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "type") String fileType) {
        log.info("接收到上传文件请求，文件名：{}, 文件类型：{}, 文件大小：{}",
                file.getOriginalFilename(), fileType, file.getSize());

        // 直接调用服务层处理上传
        String filePath = simulationService.uploadFile(simulationId, file, fileType);

        log.info("文件上传成功，保存路径：{}", filePath);
        return new ResponseDTO<>(0, "文件上传成功", filePath);
    }

    @PostMapping("/{simulationId}/run")
    @ApiOperation(value = "运行仿真任务")
    public ResponseDTO<Void> runSimulation(
            @PathVariable(value = "simulationId") Long simulationId) {
        log.info("接收到运行仿真任务请求: simulationId={}",
                simulationId);

        // 调用服务执行仿真任务
        simulationService.runSimulation(simulationId);

        log.info("仿真任务运行成功，任务ID: {}",simulationId);
        return new ResponseDTO<>(0, "仿真任务运行成功", null);
    }

    @GetMapping("/strategies")
    @ApiOperation(value = "获取仿真策略列表")
    public ResponseDTO<Map<String, String>> getSimulationStrategies() {
        log.info("接收到获取仿真策略列表请求");
        // 调用服务层获取仿真策略
        Map<String, String> strategies = simulationService.getSimulationStrategies();
        log.info("仿真策略列表获取成功，总数量: {}", strategies.size());
        return new ResponseDTO<>(0, "获取仿真策略成功", strategies);
    }

    @GetMapping("/{simulationId}/export")
    @ApiOperation(value = "导出仿真任务")
    public void exportSimulation(
            @PathVariable(value = "simulationId") Long simulationId,
            HttpServletResponse response) {
        log.info("接收到导出仿真任务请求: simulationId={}", simulationId);

        // 调用服务层处理导出
        simulationService.exportSimulation(simulationId, response);
    }

    @PostMapping("/import")
    @ApiOperation(value = "导入仿真任务")
    public ResponseDTO<Simulation> importProject(
            @RequestParam(value = "projectId") Long projectId,
            @RequestParam("file") MultipartFile file) {
        log.info("接收到导入仿真任务请求，文件名：{}, 文件大小：{}",
                file.getOriginalFilename(), file.getSize());
        // 直接调用服务层处理
        Simulation result = simulationService.importSimulation(projectId, file);
        log.info("仿真任务导入成功，仿真任务ID：{}", result.getSimulationId());
        return new ResponseDTO<>(0, "仿真任务导入成功", result);
    }

    @GetMapping("/{simulationId}/path")
    @ApiOperation(value = "获取仿真任务输出路径")
    public ResponseDTO<String> getSimulationOutputPath(
            @PathVariable(value = "simulationId") Long simulationId) {
        log.info("接收到获取仿真任务输出路径请求: simulationId={}", simulationId);

        // 调用服务获取输出路径
        String outputPath = simulationService.getSimulationOutputPath(simulationId);

        log.info("获取仿真任务输出路径成功，任务ID: {}, 输出路径: {}", simulationId, outputPath);
        return new ResponseDTO<>(0, "获取仿真任务输出路径成功", outputPath);
    }

    @GetMapping("/{simulationId}/metrics")
    @ApiOperation(value = "获取仿真任务指标列表")
    public ResponseDTO<List<Metric>> getSimulationMetrics(
            @PathVariable(value = "simulationId") Long simulationId) {
        log.info("接收到获取仿真任务指标列表请求: simulationId={}", simulationId);
        // 调用服务获取指标列表
        List<Metric> metrics = simulationService.getSimulationMetrics(simulationId);

        log.info("获取仿真任务指标列表成功，任务ID: {}, 指标数量: {}", simulationId, metrics.size());
        return new ResponseDTO<>(0, "获取仿真任务指标列表成功", metrics);
    }

    @GetMapping("/{simulationId}/metrics/{metricId}")
    @ApiOperation(value = "获取仿真任务指标数据")
    public ResponseDTO<List<MetricData>> getSimulationMetricData(
            @PathVariable(value = "simulationId") Long simulationId,
            @PathVariable(value = "metricId") String metricId) {
        log.info("接收到获取仿真任务指标数据请求: simulationId={}, metricName={}",
                simulationId, metricId);

        // 调用服务获取指标数据
        List<MetricData> metricData = simulationService.getSimulationMetricData(simulationId, metricId, null);
        log.info("获取仿真任务指标数据成功，任务ID: {}, 指标名称: {}, 数据点数量: {}",
                simulationId, metricId, metricData.get(0).getDataPoints().size());
        return new ResponseDTO<>(0, "获取仿真任务指标数据成功", metricData);
    }

    @PostMapping("/{simulationId}/metrics/batch")
    @ApiOperation(value = "批量获取仿真任务指标数据")
    public ResponseDTO<List<MetricData>> batchGetSimulationMetricData(
            @PathVariable(value = "simulationId") Long simulationId,
            @RequestBody BatchMetricQueryDTO queryDTO) {
        log.info("接收到批量获取仿真任务指标数据请求: simulationId={}, queryDTO={}",
                simulationId, queryDTO);

        // 调用服务获取多个指标数据
        List<MetricData> metricData = simulationService.getMultipleSimulationMetricData(
                simulationId, queryDTO.getMetricIds(), queryDTO.getStartTimestamp());
        log.info("批量获取仿真任务指标数据成功，任务ID: {}, 指标数量: {}",
                simulationId, metricData.size());
        return new ResponseDTO<>(0, "批量获取仿真任务指标数据成功", metricData);
    }

    @PostMapping("/{simulationId}/metrics/export")
    @ApiOperation(value = "导出仿真任务指标数据")
    public void exportSimulationMetricData(
            @PathVariable(value = "simulationId") Long simulationId,
            @RequestBody BatchMetricExportDTO queryDTO,
            HttpServletResponse response) {
        log.info("接收到导出仿真任务指标数据请求: simulationId={}, queryDTO={}",
                simulationId, queryDTO);

        // 调用服务导出多个指标数据
        simulationService.exportMultipleSimulationMetricData(
                simulationId, queryDTO.getPic(), queryDTO.getMetricIds(), queryDTO.getStartTimestamp(), response);
    }



    @GetMapping("/{simulationId}/report")
    @ApiOperation(value = "获取台区电压统计报表")
    public ResponseDTO<CourtsStatistics> getCourtsVoltageReport(
            @PathVariable(value = "simulationId") Long simulationId) {
        log.info("接收到获取台区电压统计报表请求: simulationId={}", simulationId);

        // 调用服务获取台区电压报表
        CourtsStatistics voltageReport = simulationService.getCourtsVoltageReport(simulationId);
        log.info("获取台区电压统计报表成功，台区ID: {}, 用户总数: {}, 合格率: {}",
                simulationId, voltageReport.getTotalUsers(), voltageReport.getQualifiedRate());

        return new ResponseDTO<>(0, "获取台区电压统计报表成功", voltageReport);
    }

    @PostMapping("/{simulationId}/user-report")
    @ApiOperation(value = "获取用户电压合格率统计报表（支持分页和关键字查询）")
    public ResponseDTO<List<UserStatistics>> getUserVoltageReport(
            @PathVariable(value = "simulationId") Long simulationId,
            @RequestBody UserVoltageReportQueryDTO queryDTO) {
        log.info("接收到获取用户电压合格率统计报表请求: simulationId={}, queryDTO={}",
                simulationId, queryDTO);

        // 调用服务获取用户电压合格率报表
        List<UserStatistics> userVoltageReport = simulationService.getUserVoltageReport(
                simulationId,
                queryDTO.getKeyword(),
                queryDTO.getPageNum(),
                queryDTO.getPageSize());

        long total = simulationService.countUserVoltageReport(simulationId, queryDTO.getKeyword());

        log.info("获取用户电压合格率统计报表成功，仿真任务ID: {}, 用户数量: {}",
                simulationId, total);

        return new ResponseDTO<>(0, "获取用户电压合格率统计报表成功", userVoltageReport, total);
    }

    @GetMapping("/{simulationId}/simulation-report")
    @ApiOperation(value = "计算仿真数据的电压合格率统计")
    public ResponseDTO<CourtsStatistics> calculateSimulationVoltageQualityRate(
            @PathVariable(value = "simulationId") Long simulationId) {
        log.info("接收到计算仿真电压合格率请求: simulationId={}", simulationId);

        try {
            // 调用服务计算电压合格率
            CourtsStatistics voltageQualityRate = simulationService.calculateSimulationVoltageQualityRate(simulationId);

            log.info("计算仿真电压合格率成功，仿真任务ID: {}, 总数据点: {}, 合格数据点: {}, 合格率: {}%",
                    simulationId, voltageQualityRate.getTotalReadings(),
                    voltageQualityRate.getQualifiedReadings(), String.format("%.2f", voltageQualityRate.getQualifiedRate()));

            return new ResponseDTO<>(0, "计算仿真电压合格率成功", voltageQualityRate);

        } catch (ErrorMsg e) {
            log.error("计算仿真电压合格率失败: {}", e.getMessage());
            return new ResponseDTO<>(e.getCode(), e.getMessage(), null);
        } catch (Exception e) {
            log.error("计算仿真电压合格率时发生未知错误", e);
            return new ResponseDTO<>(-1, "计算仿真电压合格率失败：" + e.getMessage(), null);
        }
    }

}


