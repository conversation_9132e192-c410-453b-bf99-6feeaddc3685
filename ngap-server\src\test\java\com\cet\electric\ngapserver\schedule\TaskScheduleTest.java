package com.cet.electric.ngapserver.schedule;

import com.cet.electric.ngapserver.dao.ProjectDao;
import com.cet.electric.ngapserver.dao.SimulationDao;
import com.cet.electric.ngapserver.entity.Project;
import com.cet.electric.ngapserver.entity.Simulation;
import com.cet.electric.ngapserver.util.FileUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.whenNew;

@RunWith(PowerMockRunner.class)
@PrepareForTest({TaskSchedule.class, FileUtils.class})
public class TaskScheduleTest {

    @InjectMocks
    private TaskSchedule taskSchedule;

    @Mock
    private ProjectDao projectDao;

    @Mock
    private SimulationDao simulationDao;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        mockStatic(FileUtils.class);
    }

    @Test
    public void testDelete_Success() throws Exception {
        // 准备测试数据
        List<Project> deletedProjects = Arrays.asList(
                Project.builder().projectId(1L).projectName("项目1").isDeleted(1).build(),
                Project.builder().projectId(2L).projectName("项目2").isDeleted(1).build()
        );

        List<Simulation> deletedSimulations = Arrays.asList(
                Simulation.builder().simulationId(1L).projectId(1L).simulationName("仿真1").isDeleted(1).build(),
                Simulation.builder().simulationId(2L).projectId(2L).simulationName("仿真2").isDeleted(1).build()
        );

        // Mock DAO 行为
        when(projectDao.findAllDeleted()).thenReturn(deletedProjects);
        when(projectDao.deleteAllMarkedAsDeleted()).thenReturn(2);
        when(simulationDao.findAllDeleted()).thenReturn(deletedSimulations);
        when(simulationDao.deleteAllMarkedAsDeleted()).thenReturn(2);

        // Mock FileUtils 行为
        when(FileUtils.getCurrentPath()).thenReturn("/test/path");

        // Mock File 对象
        File mockProjectFolder1 = mock(File.class);
        File mockProjectFolder2 = mock(File.class);
        File mockSimulationFolder1 = mock(File.class);
        File mockSimulationFolder2 = mock(File.class);

        when(mockProjectFolder1.exists()).thenReturn(true);
        when(mockProjectFolder2.exists()).thenReturn(true);
        when(mockSimulationFolder1.exists()).thenReturn(true);
        when(mockSimulationFolder2.exists()).thenReturn(true);

        when(mockProjectFolder1.getAbsolutePath()).thenReturn("/test/path/data/1");
        when(mockProjectFolder2.getAbsolutePath()).thenReturn("/test/path/data/2");
        when(mockSimulationFolder1.getAbsolutePath()).thenReturn("/test/path/data/1/1");
        when(mockSimulationFolder2.getAbsolutePath()).thenReturn("/test/path/data/2/2");

        // Mock File 构造函数
        whenNew(File.class).withArguments("/test/path/data/1").thenReturn(mockProjectFolder1);
        whenNew(File.class).withArguments("/test/path/data/2").thenReturn(mockProjectFolder2);
        whenNew(File.class).withArguments("/test/path/data/1/1").thenReturn(mockSimulationFolder1);
        whenNew(File.class).withArguments("/test/path/data/2/2").thenReturn(mockSimulationFolder2);

        // 执行测试
        taskSchedule.delete();

        // 验证项目相关调用
        verify(projectDao).findAllDeleted();
        verify(projectDao).deleteAllMarkedAsDeleted();

        // 验证仿真任务相关调用
        verify(simulationDao).findAllDeleted();
        verify(simulationDao).deleteAllMarkedAsDeleted();

        // 验证FileUtils调用 - 静态方法已在setUp中mock，这里不需要额外验证
    }

    @Test
    public void testDelete_NoDeletedProjects() {
        // Mock DAO 行为 - 没有已删除的项目
        when(projectDao.findAllDeleted()).thenReturn(Collections.emptyList());
        when(simulationDao.findAllDeleted()).thenReturn(Collections.emptyList());

        // 执行测试
        taskSchedule.delete();

        // 验证调用
        verify(projectDao).findAllDeleted();
        verify(projectDao, never()).deleteAllMarkedAsDeleted();
        verify(simulationDao).findAllDeleted();
        verify(simulationDao, never()).deleteAllMarkedAsDeleted();
    }

    @Test
    public void testDelete_NullDeletedProjects() {
        // Mock DAO 行为 - 返回null
        when(projectDao.findAllDeleted()).thenReturn(null);
        when(simulationDao.findAllDeleted()).thenReturn(null);

        // 执行测试
        taskSchedule.delete();

        // 验证调用
        verify(projectDao).findAllDeleted();
        verify(projectDao, never()).deleteAllMarkedAsDeleted();
        verify(simulationDao).findAllDeleted();
        verify(simulationDao, never()).deleteAllMarkedAsDeleted();
    }

    @Test
    public void testDelete_ProjectFolderNotExists() throws Exception {
        // 准备测试数据
        List<Project> deletedProjects = Arrays.asList(
                Project.builder().projectId(1L).projectName("项目1").isDeleted(1).build()
        );

        // Mock DAO 行为
        when(projectDao.findAllDeleted()).thenReturn(deletedProjects);
        when(projectDao.deleteAllMarkedAsDeleted()).thenReturn(1);
        when(simulationDao.findAllDeleted()).thenReturn(Collections.emptyList());

        // Mock FileUtils 行为
        when(FileUtils.getCurrentPath()).thenReturn("/test/path");

        // Mock File 对象 - 文件夹不存在
        File mockProjectFolder = mock(File.class);
        when(mockProjectFolder.exists()).thenReturn(false);
        when(mockProjectFolder.getAbsolutePath()).thenReturn("/test/path/data/1");

        // Mock File 构造函数
        whenNew(File.class).withArguments("/test/path/data/1").thenReturn(mockProjectFolder);

        // 执行测试
        taskSchedule.delete();

        // 验证调用
        verify(projectDao).findAllDeleted();
        verify(projectDao).deleteAllMarkedAsDeleted();
    }

    @Test
    public void testDelete_FileDeleteException() throws Exception {
        // 准备测试数据
        List<Project> deletedProjects = Arrays.asList(
                Project.builder().projectId(1L).projectName("项目1").isDeleted(1).build()
        );

        // Mock DAO 行为
        when(projectDao.findAllDeleted()).thenReturn(deletedProjects);
        when(projectDao.deleteAllMarkedAsDeleted()).thenReturn(1);
        when(simulationDao.findAllDeleted()).thenReturn(Collections.emptyList());

        // Mock FileUtils 行为
        when(FileUtils.getCurrentPath()).thenReturn("/test/path");

        // Mock File 对象
        File mockProjectFolder = mock(File.class);
        when(mockProjectFolder.exists()).thenReturn(true);
        when(mockProjectFolder.getAbsolutePath()).thenReturn("/test/path/data/1");

        // Mock File 构造函数
        whenNew(File.class).withArguments("/test/path/data/1").thenReturn(mockProjectFolder);

        // 执行测试 - 不应该抛出异常，应该捕获并记录日志
        taskSchedule.delete();

        // 验证调用
        verify(projectDao).findAllDeleted();
        verify(projectDao).deleteAllMarkedAsDeleted();
    }

    @Test
    public void testDelete_SimulationFolderSuccess() throws Exception {
        // 准备测试数据
        List<Simulation> deletedSimulations = Arrays.asList(
                Simulation.builder().simulationId(1L).projectId(1L).simulationName("仿真1").isDeleted(1).build()
        );

        // Mock DAO 行为
        when(projectDao.findAllDeleted()).thenReturn(Collections.emptyList());
        when(simulationDao.findAllDeleted()).thenReturn(deletedSimulations);
        when(simulationDao.deleteAllMarkedAsDeleted()).thenReturn(1);

        // Mock FileUtils 行为
        when(FileUtils.getCurrentPath()).thenReturn("/test/path");

        // Mock File 对象
        File mockSimulationFolder = mock(File.class);
        when(mockSimulationFolder.exists()).thenReturn(true);
        when(mockSimulationFolder.getAbsolutePath()).thenReturn("/test/path/data/1/1");

        // Mock File 构造函数
        whenNew(File.class).withArguments("/test/path/data/1/1").thenReturn(mockSimulationFolder);

        // 执行测试
        taskSchedule.delete();

        // 验证调用
        verify(simulationDao).findAllDeleted();
        verify(simulationDao).deleteAllMarkedAsDeleted();
        // 验证FileUtils调用 - 静态方法已在setUp中mock，这里不需要额外验证
    }

    @Test
    public void testDelete_SimulationFolderNotExists() throws Exception {
        // 准备测试数据
        List<Simulation> deletedSimulations = Arrays.asList(
                Simulation.builder().simulationId(1L).projectId(1L).simulationName("仿真1").isDeleted(1).build()
        );

        // Mock DAO 行为
        when(projectDao.findAllDeleted()).thenReturn(Collections.emptyList());
        when(simulationDao.findAllDeleted()).thenReturn(deletedSimulations);
        when(simulationDao.deleteAllMarkedAsDeleted()).thenReturn(1);

        // Mock FileUtils 行为
        when(FileUtils.getCurrentPath()).thenReturn("/test/path");

        // Mock File 对象 - 文件夹不存在
        File mockSimulationFolder = mock(File.class);
        when(mockSimulationFolder.exists()).thenReturn(false);
        when(mockSimulationFolder.getAbsolutePath()).thenReturn("/test/path/data/1/1");

        // Mock File 构造函数
        whenNew(File.class).withArguments("/test/path/data/1/1").thenReturn(mockSimulationFolder);

        // 执行测试
        taskSchedule.delete();

        // 验证调用
        verify(simulationDao).findAllDeleted();
        verify(simulationDao).deleteAllMarkedAsDeleted();
    }

    @Test
    public void testDelete_SimulationFileDeleteException() throws Exception {
        // 准备测试数据
        List<Simulation> deletedSimulations = Arrays.asList(
                Simulation.builder().simulationId(1L).projectId(1L).simulationName("仿真1").isDeleted(1).build()
        );

        // Mock DAO 行为
        when(projectDao.findAllDeleted()).thenReturn(Collections.emptyList());
        when(simulationDao.findAllDeleted()).thenReturn(deletedSimulations);
        when(simulationDao.deleteAllMarkedAsDeleted()).thenReturn(1);

        // Mock FileUtils 行为
        when(FileUtils.getCurrentPath()).thenReturn("/test/path");

        // Mock File 对象
        File mockSimulationFolder = mock(File.class);
        when(mockSimulationFolder.exists()).thenReturn(true);
        when(mockSimulationFolder.getAbsolutePath()).thenReturn("/test/path/data/1/1");

        // Mock File 构造函数
        whenNew(File.class).withArguments("/test/path/data/1/1").thenReturn(mockSimulationFolder);

        // 执行测试 - 不应该抛出异常，应该捕获并记录日志
        taskSchedule.delete();

        // 验证调用
        verify(simulationDao).findAllDeleted();
        verify(simulationDao).deleteAllMarkedAsDeleted();
    }

    @Test
    public void testDelete_MixedScenario() throws Exception {
        // 准备测试数据 - 混合场景：有些文件夹存在，有些不存在，有些删除失败
        List<Project> deletedProjects = Arrays.asList(
                Project.builder().projectId(1L).projectName("项目1").isDeleted(1).build(),
                Project.builder().projectId(2L).projectName("项目2").isDeleted(1).build()
        );

        List<Simulation> deletedSimulations = Arrays.asList(
                Simulation.builder().simulationId(1L).projectId(1L).simulationName("仿真1").isDeleted(1).build(),
                Simulation.builder().simulationId(2L).projectId(2L).simulationName("仿真2").isDeleted(1).build()
        );

        // Mock DAO 行为
        when(projectDao.findAllDeleted()).thenReturn(deletedProjects);
        when(projectDao.deleteAllMarkedAsDeleted()).thenReturn(2);
        when(simulationDao.findAllDeleted()).thenReturn(deletedSimulations);
        when(simulationDao.deleteAllMarkedAsDeleted()).thenReturn(2);

        // Mock FileUtils 行为
        when(FileUtils.getCurrentPath()).thenReturn("/test/path");

        // Mock File 对象
        File mockProjectFolder1 = mock(File.class);
        File mockProjectFolder2 = mock(File.class);
        File mockSimulationFolder1 = mock(File.class);
        File mockSimulationFolder2 = mock(File.class);

        when(mockProjectFolder1.exists()).thenReturn(true);
        when(mockProjectFolder2.exists()).thenReturn(true);
        when(mockSimulationFolder1.exists()).thenReturn(true);
        when(mockSimulationFolder2.exists()).thenReturn(true);

        when(mockProjectFolder1.getAbsolutePath()).thenReturn("/test/path/data/1");
        when(mockProjectFolder2.getAbsolutePath()).thenReturn("/test/path/data/2");
        when(mockSimulationFolder1.getAbsolutePath()).thenReturn("/test/path/data/1/1");
        when(mockSimulationFolder2.getAbsolutePath()).thenReturn("/test/path/data/2/2");

        // Mock File 构造函数
        whenNew(File.class).withArguments("/test/path/data/1").thenReturn(mockProjectFolder1);
        whenNew(File.class).withArguments("/test/path/data/2").thenReturn(mockProjectFolder2);
        whenNew(File.class).withArguments("/test/path/data/1/1").thenReturn(mockSimulationFolder1);
        whenNew(File.class).withArguments("/test/path/data/2/2").thenReturn(mockSimulationFolder2);

        // 执行测试
        taskSchedule.delete();

        // 验证调用
        verify(projectDao).findAllDeleted();
        verify(projectDao).deleteAllMarkedAsDeleted();
        verify(simulationDao).findAllDeleted();
        verify(simulationDao).deleteAllMarkedAsDeleted();
        // 验证FileUtils调用 - 静态方法已在setUp中mock，这里不需要额外验证
    }

    @Test
    public void testDelete_WithNullDirectories_DataDirExists() throws Exception {
        // Mock DAO 行为 - 没有已删除的项目和仿真任务
        when(projectDao.findAllDeleted()).thenReturn(Collections.emptyList());
        when(simulationDao.findAllDeleted()).thenReturn(Collections.emptyList());

        // Mock FileUtils 行为
        when(FileUtils.getCurrentPath()).thenReturn("/test/path");
        when(FileUtils.isPathSafe(any(File.class))).thenReturn(true);
        when(FileUtils.isWithinDirectory(any(File.class), any(File.class))).thenReturn(true);
        when(FileUtils.isSymbolicLink(any(File.class))).thenReturn(false);

        // Mock data目录
        File mockDataDir = mock(File.class);
        when(mockDataDir.exists()).thenReturn(true);
        when(mockDataDir.isDirectory()).thenReturn(true);
        when(mockDataDir.getAbsolutePath()).thenReturn("/test/path/data");

        // Mock null目录
        File mockNullDir = mock(File.class);
        when(mockNullDir.isDirectory()).thenReturn(true);
        when(mockNullDir.getName()).thenReturn("null");
        when(mockNullDir.getAbsolutePath()).thenReturn("/test/path/data/null");

        File[] files = {mockNullDir};
        when(mockDataDir.listFiles()).thenReturn(files);

        // Mock File 构造函数
        whenNew(File.class).withArguments("/test/path/data").thenReturn(mockDataDir);

        // 执行测试
        taskSchedule.delete();

        // 验证调用
        verify(projectDao).findAllDeleted();
        verify(simulationDao).findAllDeleted();
    }

    @Test
    public void testDelete_WithNullDirectories_DataDirNotExists() throws Exception {
        // Mock DAO 行为 - 没有已删除的项目和仿真任务
        when(projectDao.findAllDeleted()).thenReturn(Collections.emptyList());
        when(simulationDao.findAllDeleted()).thenReturn(Collections.emptyList());

        // Mock FileUtils 行为
        when(FileUtils.getCurrentPath()).thenReturn("/test/path");

        // Mock data目录 - 不存在
        File mockDataDir = mock(File.class);
        when(mockDataDir.exists()).thenReturn(false);
        when(mockDataDir.isDirectory()).thenReturn(false);

        // Mock File 构造函数
        whenNew(File.class).withArguments("/test/path/data").thenReturn(mockDataDir);

        // 执行测试
        taskSchedule.delete();

        // 验证调用
        verify(projectDao).findAllDeleted();
        verify(simulationDao).findAllDeleted();
        // 注意：FileUtils.getCurrentPath() 是静态方法，已在setUp中mock，无需额外验证
    }

    @Test
    public void testDelete_WithNullDirectories_UnsafePath() throws Exception {
        // Mock DAO 行为 - 没有已删除的项目和仿真任务
        when(projectDao.findAllDeleted()).thenReturn(Collections.emptyList());
        when(simulationDao.findAllDeleted()).thenReturn(Collections.emptyList());

        // Mock FileUtils 行为
        when(FileUtils.getCurrentPath()).thenReturn("/test/path");
        when(FileUtils.isPathSafe(any(File.class))).thenReturn(false); // 路径不安全

        // Mock data目录
        File mockDataDir = mock(File.class);
        when(mockDataDir.exists()).thenReturn(true);
        when(mockDataDir.isDirectory()).thenReturn(true);

        // Mock File 构造函数
        whenNew(File.class).withArguments("/test/path/data").thenReturn(mockDataDir);

        // 执行测试
        taskSchedule.delete();

        // 验证调用
        verify(projectDao).findAllDeleted();
        verify(simulationDao).findAllDeleted();
        // 注意：FileUtils.getCurrentPath() 是静态方法，已在setUp中mock，无需额外验证
    }

    @Test
    public void testDelete_WithSymbolicLinkDirectory() throws Exception {
        // Mock DAO 行为 - 没有已删除的项目和仿真任务
        when(projectDao.findAllDeleted()).thenReturn(Collections.emptyList());
        when(simulationDao.findAllDeleted()).thenReturn(Collections.emptyList());

        // Mock FileUtils 行为
        when(FileUtils.getCurrentPath()).thenReturn("/test/path");
        when(FileUtils.isPathSafe(any(File.class))).thenReturn(true);
        when(FileUtils.isWithinDirectory(any(File.class), any(File.class))).thenReturn(true);
        when(FileUtils.isSymbolicLink(any(File.class))).thenReturn(true); // 是符号链接

        // Mock data目录
        File mockDataDir = mock(File.class);
        when(mockDataDir.exists()).thenReturn(true);
        when(mockDataDir.isDirectory()).thenReturn(true);

        // Mock 符号链接目录
        File mockSymLinkDir = mock(File.class);
        when(mockSymLinkDir.isDirectory()).thenReturn(true);
        when(mockSymLinkDir.getName()).thenReturn("symlink_dir");
        when(mockSymLinkDir.getAbsolutePath()).thenReturn("/test/path/data/symlink_dir");

        File[] files = {mockSymLinkDir};
        when(mockDataDir.listFiles()).thenReturn(files);

        // Mock File 构造函数
        whenNew(File.class).withArguments("/test/path/data").thenReturn(mockDataDir);

        // 执行测试
        taskSchedule.delete();

        // 验证调用
        verify(projectDao).findAllDeleted();
        verify(simulationDao).findAllDeleted();
    }
}
