<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog class="wave-dialog" v-bind="CetDialog_pagedialog" v-on="CetDialog_pagedialog.event">
      <template v-slot:footer>
        <span>
          <!-- preserve按钮组件 -->
          <!-- <CetButton v-bind="CetButton_preserve" v-on="CetButton_preserve.event"></CetButton> -->
          <!-- cancel按钮组件 -->
          <CetButton v-bind="CetButton_cancel" v-on="CetButton_cancel.event"></CetButton>
        </span>
      </template>
      <waveApp :data="inputData_in"></waveApp>
    </CetDialog>
  </div>
</template>
<script>
import common from "@/utils/common";
import waveApp from "@/projects/components/waveApp.vue";
import { mhCONST } from "@/config/const";
export default {
  name: "WaveDialog",
  components: { waveApp },
  computed: {},
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    title: {
      type: String
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: "",
        top: "5vh",
        modalAppendToBody: false,
        width: mhCONST("dialogSize.large"),
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: "确定",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {},
    inputData_in(val) {}
  },
  methods: {
    CetDialog_pagedialog_openTrigger_out(val) {
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_preserve_statusTrigger_out(val) {},
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.wave-dialog {
  ::v-deep .el-dialog {
    margin-bottom: 0;
    .el-dialog__header {
      display: none;
    }
  }
}
</style>
