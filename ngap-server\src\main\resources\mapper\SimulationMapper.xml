<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cet.electric.ngapserver.dao.SimulationDao">
    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.cet.electric.ngapserver.entity.Simulation">
        <id property="simulationId" column="simulation_id"/>
        <result property="projectId" column="project_id"/>
        <result property="projectName" column="project_name"/>
        <result property="simulationName" column="simulation_name"/>
        <result property="simulationModel" column="simulation_model"/>
        <result property="simulationDraft" column="simulation_draft"/>
        <result property="inputData" column="input_data"/>
        <result property="outputData" column="output_data"/>
        <result property="measuredData" column="measured_data"/>
        <result property="simulationScript" column="simulation_script"/>
        <result property="nodes" column="nodes"/>
        <result property="controlStrategy" column="control_strategy"/>
        <result property="runStatus" column="run_status"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>

    <!-- 通用列定义 -->
    <sql id="Base_Column_List">
        simulation_id, project_id, project_name, simulation_name, simulation_model, simulation_draft,
        input_data, output_data, measured_data, simulation_script, nodes, control_strategy, run_status,
        created_at, updated_at, is_deleted
    </sql>

    <!-- 基础查询条件 -->
    <sql id="Simulation_Where_Clause">
        WHERE is_deleted = 0
        AND project_id = #{projectId}
        <if test="keyword != null and keyword != ''">
            AND simulation_name LIKE '%' || #{keyword} || '%'
        </if>
        <if test="startTime != null">
            AND created_at >= #{startTime}
        </if>
        <if test="endTime != null">
            AND created_at &lt; #{endTime}
        </if>
    </sql>

    <!-- 分页查询仿真任务 -->
    <select id="getSimulations" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        simulation
        <include refid="Simulation_Where_Clause"/>
        ORDER BY
        <choose>
            <when test="sortBy == 'updated_at'">updated_at</when>
            <otherwise>created_at</otherwise>
        </choose>
        <choose>
            <when test="sortOrder.equalsIgnoreCase('asc')">ASC</when>
            <otherwise>DESC</otherwise>
        </choose>
        LIMIT #{size} OFFSET #{offset}
    </select>

    <!-- 统计仿真任务数量 -->
    <select id="countSimulations" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM simulation
        <include refid="Simulation_Where_Clause"/>
    </select>

    <!-- 插入新仿真任务 -->
    <insert id="createSimulation" parameterType="com.cet.electric.ngapserver.entity.Simulation" useGeneratedKeys="true" keyProperty="simulationId">
        INSERT INTO simulation (
            project_id,
            project_name,
            simulation_name,
            simulation_model,
            simulation_draft,
            input_data,
            output_data,
            measured_data,
            simulation_script,
            nodes,
            control_strategy,
            run_status,
            created_at,
            updated_at,
            is_deleted
        ) VALUES (
                     #{projectId},
                     #{projectName},
                     #{simulationName},
                     #{simulationModel},
                     #{simulationDraft},
                     #{inputData},
                     #{outputData},
                     #{measuredData},
                     #{simulationScript},
                     #{nodes},
                     #{controlStrategy},
                     #{runStatus},
                     #{createdAt},
                     #{updatedAt},
                     #{isDeleted}
                 )
    </insert>

    <!-- 根据项目ID和仿真名称查询仿真任务 -->
    <select id="findByProjectIdAndName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        simulation
        WHERE
        project_id = #{projectId}
        AND simulation_name = #{simulationName}
        AND is_deleted = 0
        LIMIT 1
    </select>

    <!-- 根据ID查询仿真任务 -->
    <select id="findById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        simulation
        WHERE
        simulation_id = #{simulationId}
        LIMIT 1
    </select>

    <!-- 更新仿真任务 -->
    <update id="updateSimulation" parameterType="com.cet.electric.ngapserver.entity.Simulation">
        UPDATE simulation
        SET
            project_id = #{projectId},
            project_name = #{projectName},
            simulation_name = #{simulationName},
            simulation_model = #{simulationModel},
            simulation_draft = #{simulationDraft},
            input_data = #{inputData},
            output_data = #{outputData},
            measured_data = #{measuredData},
            simulation_script = #{simulationScript},
            nodes = #{nodes},
            control_strategy = #{controlStrategy},
            run_status = #{runStatus},
            updated_at = #{updatedAt},
            is_deleted = #{isDeleted}
        WHERE
            simulation_id = #{simulationId}
    </update>

    <!-- 查询所有已标记为删除的模拟 -->
    <select id="findAllDeleted" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        simulation
        WHERE
        is_deleted = 1
    </select>

    <!-- 物理删除所有已标记为删除的模拟 -->
    <delete id="deleteAllMarkedAsDeleted">
        DELETE FROM simulation
        WHERE is_deleted = 1
    </delete>

</mapper>
