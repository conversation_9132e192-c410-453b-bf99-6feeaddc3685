package com.cet.electric.ngapserver.util;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * 结果集辅助工具类
 */
public class ResultSetUtils {

    /**
     * 将ResultSet转换为List<Map<String, Object>>
     * @param rs 结果集
     * @return 结果列表
     * @throws SQLException SQL异常
     */
    public static List<Map<String, Object>> resultSetToList(ResultSet rs) throws SQLException {
        ResultSetMetaData md = rs.getMetaData();
        int columns = md.getColumnCount();
        List<Map<String, Object>> results = new ArrayList<>();

        while (rs.next()) {
            Map<String, Object> row = new HashMap<>(columns);
            for (int i = 1; i <= columns; ++i) {
                row.put(md.getColumnName(i), rs.getObject(i));
            }
            results.add(row);
        }

        return results;
    }
}
