import fetch from "@/utils/fetch";
import axios from "axios";
function processResponse(response) {
  return response;
}

// 分页查询仿真任务列表
export function querySimulationTaskList(data) {
  return fetch({
    url: `/ngap-server/api/simulations/query`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: data
  });
}

// 重命名仿真任务
export function putSimulationTaskRename(data) {
  return fetch({
    url: `/ngap-server/api/simulations/${data.simulationId}/rename?newName=${data.taskName}`,
    method: "PUT",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

// 复制仿真任务
export function copySimulationTask(simulationId) {
  return fetch({
    url: `/ngap-server/api/simulations/${simulationId}/copy`,
    method: "POST",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

// 删除仿真任务
export function deleteSimulationTask(simulationId) {
  return fetch({
    url: `/ngap-server/api/simulations/${simulationId}`,
    method: "DELETE",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

// 获取仿真任务详情
export function querySimulationTaskDetail(simulationId) {
  return fetch({
    url: `/ngap-server/api/simulations/${simulationId}`,
    method: "GET",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

// 创建仿真任务
export function createSimulationTask(data) {
  return fetch({
    url: `/ngap-server/api/simulations?projectId=${data.projectId}&simulationName=${data.simulationName}`,
    method: "POST",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

// 获取通用仿真指标列表
export function querySimulationMetrics(simulationId) {
  return fetch({
    url: `/ngap-server/api/simulations/${simulationId}/metrics`,
    method: "GET",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

// 获取电压合格率指标列表
export function querySimulationVoltageMetrics(simulationId) {
  return fetch({
    url: `/ngap-server/api/simulations/${simulationId}/voltage-metrics`,
    method: "GET",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

// 获取单个仿真任务指标数据
export function querySimulationMetricsResult(data) {
  return fetch({
    url: `/ngap-server/api/simulations/${data.simulationId}/metrics/${data.metricId}`,
    method: "GET",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

// 获取多个仿真任务指标数据
export function queryBatchSimulationMetricsResult(data) {
  return fetch({
    url: `/ngap-server/api/simulations/${data.simulationId}/metrics/batch`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: data.params
  });
}

// 导出仿真任务
export function simulationsExport(simulationId, hideNotice) {
  return axios.get(`/ngap-server/api/simulations/${simulationId}/export`, {
    timeout: 5 * 1e3 * 60,
    responseType: "arraybuffer"
  });
}

// 判断是否可以下一步
export function runNextStep(simulationId, hideNotice) {
  return fetch({
    url: `/ngap-server/api/simulations/${simulationId}/run`,
    method: "POST",
    headers: { hideNotice },
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

// 获取仿真任务输出路径
export function queryTaskFilePath(simulationId) {
  return fetch({
    url: `/ngap-server/api/simulations/${simulationId}/path`,
    method: "GET",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

// 获取仿真报表指标
export function viewReportByIndex(simulationId) {
  return fetch({
    url: `/ngap-server/api/simulations/${simulationId}/report`,
    method: "GET",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

// 获取仿真报表指标
export function viewReportByTable(data) {
  return fetch({
    url: `/ngap-server/api/simulations/${data?.simulationId}/user-report`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data
  });
}

// 计算仿真数据的电压合格率统计
export function querySimulationReport(simulationId) {
  return fetch({
    url: `/ngap-server/api/simulations/${simulationId}/simulation-report`,
    method: "GET",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}
