#!/bin/bash

# NGAP Server 启动脚本
# 基于现有 Dockerfile CMD 配置

set -e

# 默认环境变量设置
JAVA_OPTS=${JAVA_OPTS:-""}
JAR_FILE_NAME=${JAR_FILE_NAME:-"ngap-server"}
SKY_COLLECTOR=${SKY_COLLECTOR:-"127.0.0.1:11800"}
SKY_NAME=${SKY_NAME:-"ngap-server"}
SKY_AGENT=${SKY_AGENT:-""}
SKY_IGNORE_PATH=${SKY_IGNORE_PATH:-"/eureka/**,/swagger-resources/**,/webjars/springfox-swagger-ui/**"}

# 打印启动信息
echo "=========================================="
echo "NGAP Server 启动中..."
echo "JAR文件: /${JAR_FILE_NAME}.jar"
echo "Java选项: ${JAVA_OPTS}"
echo "SkyWalking服务名: ${SKY_NAME}"
echo "SkyWalking收集器: ${SKY_COLLECTOR}"
echo "=========================================="

# 构建 Java 启动命令
JAVA_CMD="java"

# 添加 SkyWalking Agent（如果配置了）
if [ -n "$SKY_AGENT" ]; then
    JAVA_CMD="$JAVA_CMD $SKY_AGENT"
fi

# 添加 SkyWalking 配置
JAVA_CMD="$JAVA_CMD -Dskywalking.trace.ignore_path=$SKY_IGNORE_PATH"
JAVA_CMD="$JAVA_CMD -Dskywalking.agent.service_name=$SKY_NAME"
JAVA_CMD="$JAVA_CMD -Dskywalking.collector.backend_service=$SKY_COLLECTOR"

# 添加其他 Java 选项
JAVA_CMD="$JAVA_CMD $JAVA_OPTS"

# 添加安全随机数配置
JAVA_CMD="$JAVA_CMD -Djava.security.egd=file:/dev/./urandom"

# 添加 JAR 文件
JAVA_CMD="$JAVA_CMD -jar"

# 添加远程调试配置
JAVA_CMD="$JAVA_CMD -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005"

# 添加应用 JAR 文件路径
JAVA_CMD="$JAVA_CMD /${JAR_FILE_NAME}.jar"

# 打印最终命令（用于调试）
echo "执行命令: $JAVA_CMD"
echo "=========================================="

# 执行启动命令
exec $JAVA_CMD
