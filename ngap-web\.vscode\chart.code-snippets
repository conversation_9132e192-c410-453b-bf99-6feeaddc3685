{
  // Place your V2.x 工作区 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  // "Print to console": {
  // 	"scope": "javascript,typescript",
  // 	"prefix": "log",
  // 	"body": [
  // 		"console.log('$1');",
  // 		"$2"
  // 	],
  // 	"description": "Log output to console"
  // }

  //cet-chart的代码片段
  "cet-chart-template": {
    "prefix": "cet-chart-template",
    "body": [
      " <CetChart                                                    ",
      "   v-bind=\"CetChart_$1\"                        ",
      " ></CetChart>                                 "
    ],
    "description": ""
  },
  "cet-chart-data": {
    "prefix": "cet-chart-data",
    "body": [
      "// ${1:设置组件唯一识别字段}组件                                  ",
      " CetChart_$1: {                               ",
      "   //组件输入项                                        ",
      "   inputData_in: null,                                   ",
      "   options: {}                                   ",
      " },   "
    ],
    "description": ""
  }
}
