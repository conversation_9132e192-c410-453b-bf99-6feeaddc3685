<template>
  <div class="login-normal">
    <el-form :model="login" :rules="rules" ref="form">
      <FormLineItem icon-class="account-lin" :label="$T('账号')">
        <el-form-item prop="userName">
          <el-input :placeholder="$T('请输入账号')" v-model="login.userName" />
        </el-form-item>
      </FormLineItem>
      <FormLineItem icon-class="password-lin" :label="$T('密码')">
        <el-form-item prop="passWord">
          <el-input
            :placeholder="$T('请输入密码')"
            v-model="login.passWord"
            show-password
          />
        </el-form-item>
      </FormLineItem>
      <FormLineItem
        icon-class="project_login_captcha"
        :label="$T('图形验证')"
        v-if="isNewAuthService"
      >
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item prop="captchaCode">
              <el-input
                :placeholder="$T('图形验证码')"
                v-model="login.captchaCode"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" @click.native="refreshCode()">
            <!-- <SIdentify
              :identifyCode="identifyCode"
              @click.native="refreshCode()"
              class="pointer"
              title="点击刷新"
            ></SIdentify> -->
            <img
              :src="identifyCode"
              alt=""
              width="116"
              height="32"
              class="pointer"
              title="点击刷新"
            />
          </el-col>
        </el-row>
      </FormLineItem>
      <el-button
        class="login-btn"
        type="primary"
        size="medium"
        @click="evLoginBtnClick"
      >
        {{ $T("登录") }}
      </el-button>
    </el-form>
  </div>
</template>

<script>
// import { mhEvent } from "@/base/modules/event";
import omegaAuth from "@omega/auth";
import { httping } from "@omega/auth/auth/request";
import FormLineItem from "./formLineItem.vue";
import { mhCONST } from "@/config/const";
import customApi from "@/api/custom";
import AES from "@/utils/aes.js";
import md5 from "crypto-js/md5";

const pageId = Object.freeze(
  new Date().getTime() + Math.floor(Math.random() * 1000)
);

export default {
  name: "LoginNormal",
  components: { FormLineItem },
  data() {
    return {
      isNewAuthService: mhCONST("isNewAuthService"),
      identifyCode: "",
      login: {
        userName: "",
        passWord: "",
        captchaCode: ""
      },
      rules: {
        userName: [
          {
            required: true,
            message: $T("账号不能为空"),
            trigger: "change"
          }
        ],
        passWord: [
          {
            required: true,
            message: $T("密码不能为空"),
            trigger: "change"
          }
        ],
        captchaCode: [
          { required: true, message: "验证码不能为空", trigger: "blur" }
        ]
      }
    };
  },
  methods: {
    async evLoginBtnClick() {
      await this.$refs.form.validate();
      if (
        this.login.userName === mhCONST("loginAccount&Password")[0] &&
        this.login.passWord === mhCONST("loginAccount&Password")[1]
      ) {
        this.$router.push({ path: "/projectmanage" });

        const nowTime = new Date().getTime();
        const appName = mhCONST("app.name");
        const passwordDigest = md5(
          `${appName}#${this.login.passWord}#${nowTime}`
        ).toString();
        window.sessionStorage.setItem("omega_token", passwordDigest);
      } else {
        this.$message.error("账号或密码错误");
      }
      // const param = {
      //   userName: this.login.userName,
      //   password: this.login.passWord,
      //   captchaCode: this.login.captchaCode,
      //   pageId: pageId
      // };

      // if (!this.isNewAuthService) omegaAuth.login(param);
      // else {
      //   omegaAuth.login(param, { type: "security" }).catch(err => {
      //     this.login.captchaCode = "";
      //     this.refreshCode();
      //   });
      // }
    },
    refreshCode() {
      customApi.getLoginCaptchaBase64(pageId).then(res => {
        this.identifyCode = res.data || "";
      });
    }
  },
  mounted() {
    if (this.isNewAuthService) this.refreshCode();
  }
};
</script>

<style lang="scss" scoped>
.login-btn {
  @include margin_top(J4);
  @include font_color(T5);
  width: 100%;
}
</style>
