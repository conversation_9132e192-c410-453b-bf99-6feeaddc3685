package com.cet.electric.ngapserver.util;

import com.cet.electric.ngapserver.dto.ErrorMsg;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class JsonUtils {

    /**
     * 将对象转换为JSON字符串
     *
     * @param obj 要转换的对象
     * @return JSON字符串
     */
    public static String convertObjectToJsonString(Object obj) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(obj);
        } catch (Exception e) {
            throw new ErrorMsg(-1,"对象转JSON失败: " + e.getMessage());
        }
    }

    /**
     * 解析JSON文件为Map对象
     *
     * @param jsonFile JSON文件
     * @return 解析后的Map对象
     * @throws ErrorMsg 如果解析失败
     */
    public static Map<String, Object> convertJsonFileToMap(File jsonFile) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(jsonFile,
                    new TypeReference<Map<String, Object>>(){});
        } catch (Exception e) {
            throw new ErrorMsg(-1, "解析配置文件失败，请检查文件格式");
        }
    }
}
