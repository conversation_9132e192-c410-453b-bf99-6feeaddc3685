//自定义接口命名规范, 项目名_模块名_接口名 例:eem_device_getWaterDevice
//api内部的分组文件夹命名和自定义接口命名保持一致, 便于查找
//获取枚举
import { queryModel, queryEnum } from "@/api/baseApi/model";

// 项目管理
import {
  queryProjectManageList,
  queryProjectScenarios,
  putProjectRename,
  createProject,
  copyProject,
  deleteProject
} from "@/api/projectManage/index.js";

// 仿真任务
import simulationTask from "@/api/simulationTask/index.js";

export default {
  queryProjectManageList,
  queryProjectScenarios,
  putProjectRename,
  createProject,
  copyProject,
  deleteProject,
  ...simulationTask,
  queryModel,
  queryEnum // 查询枚举的接口
};
