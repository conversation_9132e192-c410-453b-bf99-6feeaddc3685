<template>
  <div class="login">
    <div class="login-main">
      <div class="login-main-right">
        <div class="login-logo">
          <div class="login-logo-main">
            <div class="login-logo-img" />
          </div>
        </div>
        <div class="login-form">
          <LoginNormal />
        </div>
      </div>
      <div class="login-main-left"></div>
    </div>
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import LoginNormal from "./components/loginNormal.vue";
import OmegaTheme from "@omega/theme";
import omegaI18n from "@omega/i18n";
import $ from "jquery";

const languageObj = {
  zh_cn: "简体中文",
  en: "English"
};
export default {
  name: "Login",
  components: {
    LoginNormal
  },
  computed: {},
  data() {
    return {};
  },
  methods: {},
  mounted() {
    const cb = evt => {
      if (evt.key === "Enter") {
        $(this.$el).find(".login-form .login-btn").click();
      }
    };

    const $document = $(window.document);
    $document.on("keyup", cb);
    this.$on("hook:beforeDestroy", () => $document.off("keyup", cb));
  }
};
</script>

<style lang="scss" scoped>
.login {
  width: 100%;
  height: 100%;
  @include background(LOGIN_BG);
  &-lang {
    position: absolute;
    top: 20px;
    right: 20px;
  }
  &-main {
    position: absolute;
    top: 50%;
    margin-top: -300px;
    left: 50%;
    margin-left: -700px;
    width: 1400px;
    height: 600px;
    &-right {
      position: absolute;
      width: 400px;
      height: 100%;
      top: 0px;
      right: 0px;
      background-repeat: no-repeat;
      border-radius: 4px;
      @include background_color(BG1);
      @include box_shadow(S1);
      background-size: 100% 100%;
      z-index: 1000;
    }
    &-left {
      position: absolute;
      height: 100%;
      top: 0px;
      right: 500px;
      left: 0px;
      background-size: 100% 100%;
      @include background_image_static(LOGIN_CAROUSEL_IMG);
    }
  }
}

.login-logo {
  position: relative;
  height: 180px;
  display: flex;
  justify-content: center;
  align-items: center;
  &-main {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  &-img {
    background-size: contain;
    background-repeat: no-repeat;
    width: 280px;
    height: 60px;
    @include background_image_static(LOGIN_LOGO_IMG);
    // @include margin_top(J4);
    margin-top: 80px;
  }
  &-text {
    @include font_size(H1);
    @include line_height(H1);
    @include font_color(T3);
  }
}
.login-form {
  @include margin_top(J4);
  height: calc(100% - 280px);
  @include padding(J2);
}
.login-form::v-deep .el-tabs {
  .el-tabs__nav {
    width: 100%;
  }
  .el-tabs__item {
    width: 33%;
    text-align: center;
    padding-right: 0;
    &:not(.is-active) {
      @include font_color(T3);
    }
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }
}
</style>
