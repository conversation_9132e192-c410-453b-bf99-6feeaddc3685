import opendssdirect as dss
from typing import TypedDict
import matplotlib.pyplot as plt
import csv

class StorageStrategyParams(TypedDict):
    """
    储能电压调控策略参数

    :param storage_name: 储能标识
    :param v_upper_limit: 电压上限
    :param v_lower_limit: 电压下限
    :param SOC_start：初始SOC值（百分比%）
    :param SOC：当前SOC值
    ......
    ......
    """
    storage_name: str
    v_upper_limit: float
    v_lower_limit: float
    SOC_start: float  # 初始SOC百分比%
    SOC_min: float  # 放电深度%
    SOC_max: float  # 充电限值%
    ESS_capacity: float  # kWh
    ESS_ratepower: float  # 额定功率kW
    efficiency_charge: float  # 充电效率
    efficiency_discharge: float  # 放电效率


# 储能设备的策略参数组，后续改为读取json文件
# params_list: list[StorageStrategyParams] = [
#     {
#         "storage_name": "Storage_1",
#         "v_upper_limit": 225.0,
#         "v_lower_limit": 215.0,
#         "SOC_start": 20,
#         "SOC_min": 10,
#         "SOC_max": 100,
#         "ESS_capacity": 100.0,
#         "ESS_ratepower": 50,
#         "efficiency_charge": 0.9,
#         "efficiency_discharge": 0.9
#
#     }
# ]


def get_bus_v_base(bus_name: str) -> float:
    """
    获取指定并网点的额定电压

    :param bus_name: 并网点标识
    :return: 额定电压(V)
    """

    dss.Circuit.SetActiveBus(bus_name)
    kVBase = dss.Bus.kVBase()
    return kVBase * 1000


def get_bus_voltage(bus_name: str) -> float:
    """
    获取指定并网点的电压

    :param bus_name: 并网点标识
    :return: 电压(V)
    """

    dss.Circuit.SetActiveBus(bus_name)
    voltages = dss.Bus.VMagAngle()
    return voltages[0]


def set_storage_charge_p(storage_name: str, p_value: float):
    """
    设置储能设备的充电有功功率

    :param storage_name: 储能设备的名称（字符串）
    :param active_power_value: 储能设备需要调节的有功功率值（单位：千瓦 kW）
    """
    # 设置储能设备有功功率
    dss.Circuit.SetActiveElement(f'Storage.{storage_name}')
    dss.Text.Command(f"Edit Storage.{storage_name} kW={p_value}")


def set_storage_discharge_p(storage_name: str, p_value: float):
    """
    设置储能设备的放电有功功率

    :param storage_name: 储能设备的名称（字符串）
    :param active_power_value: 储能设备需要调节的有功功率值（单位：千瓦 kW）
    """
    # 设置储能设备有功功率
    dss.Circuit.SetActiveElement(f'Storage.{storage_name}')
    dss.Text.Command(f"Edit Storage.{storage_name} kW={p_value}")


def regulate_voltage(params: StorageStrategyParams, bus_voltage, SOC):
    """
    利用储能充放电进行电压调控
    """

    storage_name = params["name"]

    v_upper_limit = params["v_upper_limit"]
    v_lower_limit = params["v_lower_limit"]
    SOC_start = params["SOC_start"]
    SOC_min = params["SOC_min"]  # : 20.0, # 放电深度%
    SOC_max = params["SOC_max"]  # : 100.0, # 充电限值%
    # ESS_capacity=params["ESS_capacity"]# : 100.0,  # kWh
    ESS_ratepower = params["ESS_ratepower"]  # : 50,  # 额定功率kW
    efficiency_charge = params["efficiency_charge"]/100.0  # : 0.9, #充电效率
    efficiency_discharge = params["efficiency_discharge"]/100.0  # : 0.9 #放电效率
    # 设置当前活动元素为指定的储能系统
    dss.Circuit.SetActiveElement(f'Storage.{storage_name}')
    if not dss.CktElement.Name():
        raise ValueError(f"Storage '{storage_name}' not found in the circuit.")

    # 获取储能系统连接的母线名称
    bus_name = dss.CktElement.BusNames()[0].split('.')[0]

    # bus_voltage = get_bus_voltage(storage_name)
    # bus_v_base = get_bus_v_base(bus_name)

    v_upper_limit = params["v_upper_limit"]
    v_lower_limit = params["v_lower_limit"]

    # 获取当前母线电压
    dss.Circuit.SetActiveBus(bus_name)
    # bus_vmag = dss.Bus.VMagAngle()[0]  # 电压幅值

    # Voltage_bus.append(voltage)
    # 读取当前SOC的电量（%kWh）
    # dss.Storages.Name(storage_name)
    # SOC = dss.Storages.puSOC()
    action = "idle"

    if bus_voltage > v_upper_limit and SOC < SOC_max:
        # 充电功率为正
        # efficiency = efficiency_charge * (1 - 0.01 * (SOC - SOC_start))  # 充电效率随SOC变化
        bess_p = ESS_ratepower * efficiency_charge
        set_storage_charge_p(storage_name, bess_p)
        new_kwh = min(0.01 * SOC_max * params['ESS_capacity'], 0.01 * SOC * params['ESS_capacity'] + bess_p * 0.25)
        new_SOC = 100 * new_kwh / params['ESS_capacity']
        print(new_kwh)
        dss.Text.Command(f"Edit Storage.{storage_name} State=charging kWh={new_kwh}")
        action = "charge"
        # dss.Storages.Name(storage_name)



    elif bus_voltage < v_lower_limit and SOC > SOC_min:
        # 放电功率为负
        # efficiency = efficiency_discharge * (1 - 0.01 * (SOC_start- SOC))  # 放电效率随SOC变化
        bess_p = ESS_ratepower * efficiency_discharge
        set_storage_discharge_p(storage_name, bess_p)
        new_kwh = max(0.01 * SOC_min * params['ESS_capacity'], 0.01 * SOC * params['ESS_capacity'] - bess_p * 0.25)
        new_SOC = 100 * new_kwh / params['ESS_capacity']
        dss.Text.Command(f"Edit Storage.{storage_name} State=discharging kWh={new_kwh}")
        action = "discharge"

    else:
        # 待机
        bess_p = 0
        action = "idle"
        # dss.Storages.Name(storage_name)
        # SOC = dss.Storages.puSOC()
        new_SOC = SOC

    return bess_p, action, new_SOC


# In[]

storage_name = params_list[0]["name"]

# 调控前电压
dss.Monitors.Name(f'monitor_{storage_name}_VI')
voltages_before = dss.Monitors.Channel(1)

dss.Text.Command("Set mode=time number=1 stepsize=15m")
voltages_after = []
bess_p_list = []
SOC_list = []
action_list = []
new_SOC_list = []
SOC = params_list[0]['SOC_start']
# 获取储能设备连接的母线名称
dss.Circuit.SetActiveElement(f'Storage.{storage_name}')
bus_name = dss.CktElement.BusNames()[0].split('.')[0]

# 执行控制策略并记录控制后的电压
for t in range(96):
    for i in dss.Loads.AllNames():
        dss.Circuit.SetActiveElement(i)
        dss.LoadShape.Name(f'LoadShape_{i}')
        # 放大负荷
        load_kw = dss.LoadShape.PMult()[t]
        dss.Text.Command(f"Edit load.{i} kw={load_kw}")
        # dss.Text.Command(f"Edit load.{i} kvar=0.2")

    for i in dss.Generators.AllNames():
        name = dss.Generators.Name()
        rated_power_kva = dss.Generators.kVARated()
        dss.LoadShape.Name(f'LoadShape_{i}')
        pv_t = dss.LoadShape.PMult()[t] * rated_power_kva
        dss.Text.Command(f"Edit Generator.{i} kw={pv_t}")

    for i in dss.Storages.AllNames():
        if i == 'storage_1':
            for params in params_list:
                bess_p, action, new_SOC = regulate_voltage(params, voltages_before[t], SOC)
                # print(bess_p)

    dss.Text.Command("Calcv")
    dss.Solution.Solve()
    dss.Monitors.SaveAll()

    # dss.Storages.Name(storage_name)
    # SOC = dss.Storages.puSOC()
    # print(SOC)

    bess_p_list.append(bess_p)
    # SOC_list.append(SOC)
    action_list.append(action)
    new_SOC_list.append(new_SOC)
    SOC = new_SOC
    dss.Monitors.Name(f'monitor_{storage_name}_VI')
    voltages = dss.Monitors.Channel(1)[-1]
    voltages_after.append(voltages)

# In[]
# 绘制对比图
time_steps = list(range(96))
plt.figure(figsize=(10, 6))

# print(voltages_before)
# print(voltages_after)

plt.plot(time_steps, voltages_before, label='Before Control')
plt.plot(time_steps, voltages_after, label='After Control')
plt.xlabel('Time Step')
plt.ylabel('Voltage (V)')
plt.title('Voltage Comparison Before and After Storage Control')
plt.legend()
plt.grid(True)
plt.show()

# In[]
time_steps = list(range(96))
plt.figure(figsize=(10, 6))

plt.plot(time_steps, new_SOC_list)
plt.show()
# raise ValueError('wrong')

# 输出仿真结果
full_path = output_dir + f'output.csv'

# 创建新的时间序列，每小时4个时间点：900, 1800, 2700, 0（下一小时的0点）
new_data = []
for hour in range(24):
    for seconds in [900, 1800, 2700, 0]:  # 每小时的4个时间点
        if seconds == 0 and hour < 23:
            # 对于0-22小时，0秒对应下一小时
            actual_hour = hour + 1
        elif seconds == 0 and hour == 23:
            # 对于23小时，0秒对应24小时（而不是0小时）
            actual_hour = 24
        else:
            # 其他情况保持当前小时
            actual_hour = hour

        new_data.append([actual_hour, float(f"{seconds:.5f}")])

# 提取小时和秒数
hours = [item[0] for item in new_data]
t_values = [item[1] for item in new_data]

combined_data = [
    [hour, t, v_before, v_after, SOC]
    for hour, t, v_before, v_after, SOC in zip(hours, t_values, voltages_before, voltages_after, new_SOC_list)
]

with open(full_path, 'w', newline='', encoding='utf-8') as f:
    writer = csv.writer(f)

    # 写入表头
    writer.writerow(['Hour', 't(sec)', 'Voltage Before Control', 'Voltage After Control', 'SOC'])

    # 写入数据行
    writer.writerows(combined_data)




