import fetch from "@/utils/fetch";

//模型版本
const modelVersion = "v1";

function processDownloadResponse(response) {
  //TODO 处理数据中模型和主模型是一对一关系时, 拉平为对象, 而不是数组, 通过接口给的一个字段来判断.

  // 判断是不是Blob数据
  if (!(response instanceof Blob)) {
    return response;
  }

  if (response.type.indexOf("application/json") > -1) {
    return {
      code: -1,
      msg: "文件不存在"
    };
  }

  return {
    code: 0,
    data: response
  };
}

export function upload(file, hideNotice) {
  const url = `/filemanager/${modelVersion}/file`;

  const formData = new FormData();
  formData.append("file", file);

  return fetch.post(url, formData, {
    headers: { hideNotice: hideNotice },
    timeout: 60000
  });
}

export function download(data, hideNotice) {
  let fileName = data.toString();
  if (!fileName) {
    return Promise.reject();
  }

  return new Promise((resolve, reject) => {
    fileName = encodeURIComponent(fileName);
    fetch({
      url: `/filemanager/${modelVersion}/file/downLoad?fileName=${fileName}`,
      method: "GET",
      responseType: "blob",
      headers: { hideNotice: hideNotice },
      transformResponse: [processDownloadResponse], //对接口返回的数据结构进行处理
      timeout: 60000
    })
      .then(response => {
        const blobData = response.data;

        // FileReader主要用于将文件内容读入内存;
        const reader = new FileReader();
        reader.readAsDataURL(blobData);
        // onload当读取操作成功完成时调用
        reader.onload = function (e) {
          resolve(e.target.result);
        };
      })
      .catch(() => {
        reject();
      });
  });
}
