import { mhCONST } from "@/config/const";

class Enums {
  /** 初始化 */
  init() {
    this._enums = mhCONST().get("ENUMS", true);
  }

  get(key) {
    return this._enums[key];
  }

  /**
   * 获取指定属性枚举值的ID
   *
   * @param {String} path [modelLabel].[propertyLabel]
   */
  val(path) {
    const [enumKey, prop] = path.split(".");
    return this.get(enumKey)[prop];
  }

  /**
   * 获取枚举值列表
   *
   * @param {String} 枚举值模型
   * @param {Array} 过滤属性
   */
  list(enumKey) {
    const list = [];
    const enums = this.get(enumKey);
    enums.keys().forEach(prop => {
      list.push(
        Object.assign(
          {
            prop
          },
          this._parseItem(enums[prop])
        )
      );
    });
    return list;
  }

  _parseItem(exp) {
    const [id, label] = exp.split("|");
    return {
      id,
      label
    };
  }
}

export default new Enums();
