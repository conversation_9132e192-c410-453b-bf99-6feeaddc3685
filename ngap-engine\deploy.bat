@echo off
REM ========================================
REM NGAP Engine Complete Build and Deploy Script
REM ========================================

REM Version Configuration - Centralized Management
set VERSION=1.0.2-SNAPSHOT
set REGISTRY_URL=*************
set PROJECT_NAME=base/ngap-engine
set IMAGE_NAME=%REGISTRY_URL%/%PROJECT_NAME%:%VERSION%

echo ========================================
echo NGAP Engine Complete Deployment
echo ========================================
echo Registry: %REGISTRY_URL%
echo Project: %PROJECT_NAME%
echo Version: %VERSION%
echo Full Image: %IMAGE_NAME%
echo ========================================
echo.

REM Step 1: Build Docker Image
echo [Step 1/3] Building Docker image...
echo Command: docker build -t %IMAGE_NAME% .
echo.

docker build -t %IMAGE_NAME% .

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERROR: Docker build failed!
    echo Please check the Dockerfile and dependencies.
    echo.
    pause
    exit /b 1
)

echo.
echo Build successful!
echo Image: %IMAGE_NAME%
echo.

REM Step 2: Login to Private Registry
echo [Step 2/3] Logging in to private registry...
echo Registry: %REGISTRY_URL%
echo Please enter your credentials when prompted.
echo.

docker login %REGISTRY_URL%

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERROR: Login to registry failed!
    echo Please check your credentials and network connectivity.
    echo.
    pause
    exit /b 1
)

echo.
echo Login successful!
echo.

REM Step 3: Push Image to Registry
echo [Step 3/3] Pushing image to registry...
echo Command: docker push %IMAGE_NAME%
echo.

docker push %IMAGE_NAME%

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERROR: Push failed!
    echo Please check network connectivity and registry permissions.
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Deployment completed successfully!
echo ========================================
echo Image %IMAGE_NAME% has been built and pushed to registry.
echo.
echo You can now use this image in your deployments:
echo   docker run -p 5000:5000 %IMAGE_NAME%
echo.
echo Or update your docker-compose.yml to use:
echo   image: %IMAGE_NAME%
echo ========================================
echo.

REM Display final image information
echo Final image information:
docker images %REGISTRY_URL%/%PROJECT_NAME% --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

echo.
pause
