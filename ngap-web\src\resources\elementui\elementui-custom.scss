/*一些全局性的对elementui的css扩展*/

// .el-dialog__body {
//   line-height: normal;
//   padding: 20px;
// }

// 兼容IE10和IE11下container布局，避免flex不兼容导致问题
.el-container,
.el-main {
  -ms-flex: 1;
}
// .el-header {
//   padding: 0 0 0 10px;
// }
// .el-main {
//   padding: 10px 0 10px 10px;
// }

.el-tabs {
  &--border-card {
    .el-tabs__content {
      box-sizing: border-box;
      padding: 10px;
      .el-tab-pane {
        height: 100%;
      }
    }
  }
}

.el-calendar {
  .el-calendar__body {
    height: calc(100% - 53px);
    .el-calendar-table {
      height: 100%;
      .el-calendar-day {
        height: 100%;
      }
    }
  }
}
.el-input-number {
  vertical-align: top !important;
}
.el-input-number .el-input__inner {
  text-align: left !important;
}
.el-table--small {
  font-size: 14px;
}
.el-table thead {
  // color: #74788d;
  font-weight: 500;
}
.el-button--small.el-button--text {
  font-size: 14px;
}
// .el-tag--mini {
//   height: 22px;
//   padding: 0 4px;
//   line-height: 20px;
// }
.el-dialog {
  margin: 0px auto;
}
.el-dialog__title {
  // line-height: 24px;
  font-size: 16px;
  // color: #343a40;
}
.el-menu.el-menu--horizontal {
  border-bottom: 0px;
  & > .el-menu-item {
    height: 32px;
    line-height: 32px;
    &:not(.is-disabled):focus,
    &:not(.is-disabled):hover {
      @include background(BG2);
    }
  }
  & > .el-submenu .el-submenu__title {
    &:hover {
      @include background(BG2);
    }
  }
}

.el-table thead.is-group th {
  @include background(BG1);
}
.el-form-item__label {
  line-height: 30px !important;
}
.el-picker-panel__footer .el-picker-panel__link-btn.el-button--text {
  display: none;
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  background: rgba(0, 0, 0, 0.05) !important;
}

.el-range-input {
  background: transparent !important;
}
.el-picker-panel *[slot="sidebar"],
.el-picker-panel__sidebar {
  width: 115px;
  left: -3px;
}

.notify-zindex {
  z-index: 999 !important;
}

.el-cascader__tags .el-tag {
  @include background_color(B2);
}

.subSystem .el-cascader-menu__wrap {
  height: 400px;
}

.el-loading-mask {
  background-color: rgba(255, 255, 255, 0) !important;
  z-index: 9999 !important;
}

.el-upload-list--picture .el-upload-list__item {
  @include background_color(B2);
}
