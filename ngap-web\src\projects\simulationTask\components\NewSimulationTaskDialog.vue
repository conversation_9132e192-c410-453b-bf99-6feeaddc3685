<template>
  <div>
    <CetDialog v-bind="CetDialog_pagedialog" v-on="CetDialog_pagedialog.event">
      <template v-slot:footer>
        <span>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
        </span>
      </template>

      <div class="custom-form">
        <el-form :model="formData" :rules="rules" ref="form" style="width: 65%">
          <el-form-item
            class="custom-form-item gray-label mb30"
            label="仿真任务名称"
            prop="simulationName"
            for="simulationName"
          >
            <el-input
              id="simulationName"
              style="width: 250px"
              v-model="formData.simulationName"
              placeholder="请输入任务名称"
            />
          </el-form-item>
        </el-form>
      </div>
    </CetDialog>
  </div>
</template>
<script>
import common from "@/utils/common";
import { mhCONST } from "@/config/const";
import customApi from "@/api/custom";

export default {
  name: "NewOrEditProjectDialog",
  components: {},
  computed: {},
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      formData: {
        simulationName: ""
      },
      rules: {
        simulationName: [
          {
            required: true,
            message: "名称不能为空，且不能带有特殊符号。",
            trigger: ["blur", "change"]
          },
          {
            required: true,
            message: "名称不能为空，且不能带有特殊符号。",
            pattern: /^((?![`~!@$%^&*()_+=\[\]{}\\|;:\'"<,>.?\/]).)*$/
          }
        ]
      },
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        width: "650px",
        title: "新建任务",
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },

      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: "确定",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      if (this.$refs["form"]) {
        this.formData = {
          simulationName: ""
        };
      }

      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {},
    inputData_in(val) {}
  },
  methods: {
    CetDialog_pagedialog_openTrigger_out(val) {
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    async CetButton_preserve_statusTrigger_out(val) {
      const { form } = this.$refs;
      await form.validate();
      let params = {
        projectId: this.inputData_in.projectId,
        simulationName: this.formData.simulationName
      };
      customApi["createSimulationTask"](params).then(res => {
        if (res && res.code == 0) {
          this.$message.success($T("保存成功"));
          this.$emit("saveData_out");
          this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
        }
      });
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    no() {}
  },
  created: function () {}
};
</script>
<style lang="scss" scoped></style>
