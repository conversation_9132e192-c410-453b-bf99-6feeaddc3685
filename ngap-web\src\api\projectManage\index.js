import fetch from "@/utils/fetch";

function processResponse(response) {
  return response;
}

// 分页查询项目列表
export function queryProjectManageList(data) {
  return fetch({
    url: `/ngap-server/api/projects/query`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: data
  });
}

// 获取项目场景类型
export function queryProjectScenarios() {
  return fetch({
    url: `/ngap-server/api/projects/scenarios`,
    method: "GET",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

// 修改项目名称
export function putProjectRename(data) {
  return fetch({
    url: `/ngap-server/api/projects/${data.projectId}/rename?newName=${data.projectName}`,
    method: "PUT",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

// 创建项目
export function createProject(data) {
  return fetch({
    url: `/ngap-server/api/projects?projectName=${data.projectName}&projectType=${data.projectType}`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: data
  });
}

// 复制项目
export function copyProject(projectId) {
  return fetch({
    url: `/ngap-server/api/projects/${projectId}/copy`,
    method: "POST",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

// 删除项目
export function deleteProject(projectId) {
  return fetch({
    url: `/ngap-server/api/projects/${projectId}`,
    method: "DELETE",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}
