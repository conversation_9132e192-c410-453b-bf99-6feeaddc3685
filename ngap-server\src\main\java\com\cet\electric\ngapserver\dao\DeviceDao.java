package com.cet.electric.ngapserver.dao;

import com.cet.electric.ngapserver.entity.Device;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 设备数据访问层
 * <AUTHOR>
 */
@Mapper
@Component
public interface DeviceDao {

    /**
     * 创建新设备
     *
     * @param device 设备实体
     * @return 受影响的行数
     */
    int createDevice(Device device);

    /**
     * 分页查询设备
     *
     * @param offset    偏移量
     * @param size      每页大小
     * @param sortBy    排序字段
     * @param sortOrder 排序方向
     * @param keyword   搜索关键词(可选)
     * @return 设备列表
     */
    List<Device> getDevices(@Param("offset") int offset,
                           @Param("size") int size,
                           @Param("sortBy") String sortBy,
                           @Param("sortOrder") String sortOrder,
                           @Param("keyword") String keyword);

    /**
     * 查询设备总数
     *
     * @param keyword 搜索关键词(可选)
     * @return 总数
     */
    Long countDevices(@Param("keyword") String keyword);

    /**
     * 根据参数代码查询设备
     *
     * @param parameterCode 参数代码
     * @return 设备实体，如果不存在返回null
     */
    Device findByParameterCode(String parameterCode);

    /**
     * 根据ID查询设备
     *
     * @param parameterId 参数ID
     * @return 设备实体，如果不存在返回null
     */
    Device findById(Long parameterId);

    /**
     * 更新设备信息
     *
     * @param device 更新的设备实体
     * @return 受影响的行数
     */
    int updateDevice(Device device);

    /**
     * 删除设备
     *
     * @param parameterId 参数ID
     * @return 受影响的行数
     */
    int deleteDevice(Long parameterId);
}
