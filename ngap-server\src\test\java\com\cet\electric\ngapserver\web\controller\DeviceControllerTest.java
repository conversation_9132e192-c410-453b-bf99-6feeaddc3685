package com.cet.electric.ngapserver.web.controller;

import com.cet.electric.ngapserver.dto.ErrorMsg;
import com.cet.electric.ngapserver.dto.DeviceQueryDTO;
import com.cet.electric.ngapserver.entity.Device;
import com.cet.electric.ngapserver.service.DeviceService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@RunWith(SpringRunner.class)
public class DeviceControllerTest {

    @InjectMocks
    private DeviceController deviceController;

    @Mock
    private DeviceService deviceService;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(deviceController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    public void testSaveDevice_CreateSuccess() throws Exception {
        // 准备测试数据 - 创建设备（不设置版本，由系统自动设置）
        List<Device> inputDevices = Arrays.asList(
                Device.builder().deviceType("LINE").parameterCode("PARAM001").build(),
                Device.builder().deviceType("TRANSFORMER").parameterCode("PARAM002").build()
        );

        List<Device> resultDevices = Arrays.asList(
                Device.builder().parameterId(1L).deviceType("LINE").parameterCode("PARAM001").version("1.0").build(),
                Device.builder().parameterId(2L).deviceType("TRANSFORMER").parameterCode("PARAM002").version("1.0").build()
        );

        // Mock 服务层行为
        when(deviceService.createDevice(any(Device.class)))
                .thenReturn(resultDevices.get(0))
                .thenReturn(resultDevices.get(1));

        // 执行测试
        mockMvc.perform(post("/ngap-server/api/devices/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(inputDevices)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("操作成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].parameterId").value(1))
                .andExpect(jsonPath("$.data[1].parameterId").value(2));

        // 验证服务层调用
        verify(deviceService, times(2)).createDevice(any(Device.class));
    }

    @Test
    public void testSaveDevice_UpdateSuccess() throws Exception {
        // 准备测试数据 - 更新设备
        List<Device> inputDevices = Arrays.asList(
                Device.builder().parameterId(1L).deviceType("LINE").parameterCode("UPDATED_PARAM001").version("1.1").build(),
                Device.builder().parameterId(2L).deviceType("TRANSFORMER").parameterCode("UPDATED_PARAM002").version("2.1").build()
        );

        // Mock 服务层行为
        when(deviceService.updateDevice(any(Device.class)))
                .thenReturn(inputDevices.get(0))
                .thenReturn(inputDevices.get(1));

        // 执行测试
        mockMvc.perform(post("/ngap-server/api/devices/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(inputDevices)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("操作成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2));

        // 验证服务层调用
        verify(deviceService, times(2)).updateDevice(any(Device.class));
    }

    @Test
    public void testSaveDevice_WithErrors() throws Exception {
        // 准备测试数据
        List<Device> inputDevices = Arrays.asList(
                Device.builder().deviceType("LINE").parameterCode("PARAM001").version("1.0").build(),
                Device.builder().deviceType("").parameterCode("PARAM002").version("2.0").build() // 无效设备类型
        );

        Device resultDevice = Device.builder().parameterId(1L).deviceType("LINE").parameterCode("PARAM001").version("1.0").build();

        // Mock 服务层行为
        when(deviceService.createDevice(any(Device.class)))
                .thenReturn(resultDevice)
                .thenThrow(new ErrorMsg(-1, "设备类型不能为空."));

        // 执行测试
        mockMvc.perform(post("/ngap-server/api/devices/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(inputDevices)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(-1))
                .andExpect(jsonPath("$.msg").value("设备类型不能为空."))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1)); // 只有一个成功

        // 验证服务层调用
        verify(deviceService, times(2)).createDevice(any(Device.class));
    }

    @Test
    public void testGetDevices_Success() throws Exception {
        // 准备测试数据
        List<Device> mockDevices = Arrays.asList(
                Device.builder().parameterId(1L).deviceType("LINE").parameterCode("PARAM001").version("1.0").build(),
                Device.builder().parameterId(2L).deviceType("TRANSFORMER").parameterCode("PARAM002").version("2.0").build()
        );

        DeviceQueryDTO queryDTO = new DeviceQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setSize(10);
        queryDTO.setSortBy("parameterId");
        queryDTO.setSortOrder("desc");
        queryDTO.setKeyword("test");

        // Mock 服务层行为
        when(deviceService.getDevices(1, 10, "parameterId", "desc", "test")).thenReturn(mockDevices);
        when(deviceService.countDevices("test")).thenReturn(2L);

        // 执行测试
        mockMvc.perform(post("/ngap-server/api/devices/query")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(queryDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("查询成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.total").value(2));

        // 验证服务层调用
        verify(deviceService).getDevices(1, 10, "parameterId", "desc", "test");
        verify(deviceService).countDevices("test");
    }

    @Test
    public void testGetDeviceById_Success() throws Exception {
        // 准备测试数据
        Device mockDevice = Device.builder()
                .parameterId(1L)
                .deviceType("LINE")
                .parameterCode("PARAM001")
                .version("1.0")
                .build();

        // Mock 服务层行为
        when(deviceService.getDeviceById(1L)).thenReturn(mockDevice);

        // 执行测试
        mockMvc.perform(get("/ngap-server/api/devices/detail")
                        .param("parameterId", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("查询成功"))
                .andExpect(jsonPath("$.data.parameterId").value(1))
                .andExpect(jsonPath("$.data.deviceType").value("LINE"))
                .andExpect(jsonPath("$.data.parameterCode").value("PARAM001"))
                .andExpect(jsonPath("$.data.version").value("1.0"));

        // 验证服务层调用
        verify(deviceService).getDeviceById(1L);
    }

    @Test
    public void testDeleteDevice_Success() throws Exception {
        // Mock 服务层行为
        when(deviceService.deleteDevice(1L)).thenReturn(true);

        // 执行测试
        mockMvc.perform(delete("/ngap-server/api/devices/delete")
                        .param("parameterId", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("设备删除成功"))
                .andExpect(jsonPath("$.data").value(true));

        // 验证服务层调用
        verify(deviceService).deleteDevice(1L);
    }

    @Test
    public void testDeleteDevice_Failed() throws Exception {
        // Mock 服务层行为
        when(deviceService.deleteDevice(1L)).thenReturn(false);

        // 执行测试
        mockMvc.perform(delete("/ngap-server/api/devices/delete")
                        .param("parameterId", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("设备删除失败"))
                .andExpect(jsonPath("$.data").value(false));

        // 验证服务层调用
        verify(deviceService).deleteDevice(1L);
    }

    @Test
    public void testSaveDevice_WithTrimmedFields() throws Exception {
        // 准备测试数据 - 包含前后空格的字段
        List<Device> inputDevices = Arrays.asList(
                Device.builder()
                        .deviceType("  LINE  ")
                        .parameterCode("  PARAM001  ")
                        .version("  1.0  ") // 这个版本会被清空
                        .build()
        );

        Device resultDevice = Device.builder()
                .parameterId(1L)
                .deviceType("LINE")
                .parameterCode("PARAM001")
                .version("1.0")
                .build();

        // Mock 服务层行为
        when(deviceService.createDevice(any(Device.class))).thenReturn(resultDevice);

        // 执行测试
        mockMvc.perform(post("/ngap-server/api/devices/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(inputDevices)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0));

        // 验证服务层调用时字段已被trim，版本被清空（由系统自动设置）
        verify(deviceService).createDevice(argThat(device ->
                "LINE".equals(device.getDeviceType()) &&
                "PARAM001".equals(device.getParameterCode()) &&
                device.getVersion() == null // 创建时版本应该被清空
        ));
    }

    @Test
    public void testSaveDevice_VersionAutoManagement() throws Exception {
        // 准备测试数据 - 用户传入版本信息，但应该被系统忽略
        List<Device> inputDevices = Arrays.asList(
                Device.builder()
                        .deviceType("LINE")
                        .parameterCode("PARAM001")
                        .version("2.0") // 用户传入的版本，应该被忽略
                        .build()
        );

        Device resultDevice = Device.builder()
                .parameterId(1L)
                .deviceType("LINE")
                .parameterCode("PARAM001")
                .version("1.0") // 系统自动设置为1.0
                .build();

        // Mock 服务层行为
        when(deviceService.createDevice(any(Device.class))).thenReturn(resultDevice);

        // 执行测试
        mockMvc.perform(post("/ngap-server/api/devices/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(inputDevices)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data[0].version").value("1.0")); // 返回的版本应该是1.0

        // 验证服务层调用时版本被清空，由系统自动管理
        verify(deviceService).createDevice(argThat(device ->
                "LINE".equals(device.getDeviceType()) &&
                "PARAM001".equals(device.getParameterCode()) &&
                device.getVersion() == null // 用户传入的版本被清空
        ));
    }
}
