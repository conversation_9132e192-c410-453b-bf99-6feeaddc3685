<template>
  <el-image :src="src" :alt="alt" ref="img" v-bind="$attrs">
    <div slot="error" class="image-slot">
      <i class="el-icon-picture-outline" />
    </div>
  </el-image>
</template>
<script>
import { HttpBase } from "@omega/http";
// 带 header 的IMG组件
export default {
  name: "FileImage",
  data() {
    return {
      src: "",
      alt: ""
    };
  },
  props: {
    source: String,
    onHandleImgLoaded: {
      type: Function,
      default(data) {
        return data.data;
      }
    },
    isHandleImgSrc: {
      type: Boolean,
      default: false
    },
    onHandleImgSrc: {
      type: Function,
      default(fileName) {
        if (fileName) {
          return `/filemanager/v1/file/downLoad?fileName=${fileName}`;
        }
      }
    }
  },
  watch: {
    source() {
      this.update();
    }
  },
  mounted() {
    this.http = new HttpBase({ hasLoading: false, handlerJSON: false });

    this.update();
  },
  methods: {
    update() {
      if (this.src) {
        // 释放之前的图片占用的内存
        window.URL.revokeObjectURL(this.src);
      }

      let source = this.$props && this.$props.source;

      source = this.isHandleImgSrc ? this.onHandleImgSrc(source) : source;
      this.alt = source;
      if (source) {
        this.http({
          url: source,
          method: "GET",
          responseType: "blob"
        }).then(res => {
          this.src = window.URL.createObjectURL(this.onHandleImgLoaded(res));
        });
      }
    }
  },
  beforeDestroy() {
    window.URL.revokeObjectURL(this.src);
  }
};
</script>
