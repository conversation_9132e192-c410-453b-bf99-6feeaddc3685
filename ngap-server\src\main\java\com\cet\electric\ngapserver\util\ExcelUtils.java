package com.cet.electric.ngapserver.util;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import com.cet.electric.ngapserver.entity.DataPoint;
import com.cet.electric.ngapserver.entity.Metric;
import com.cet.electric.ngapserver.entity.MetricData;
import com.cet.electric.ngapserver.entity.MetricStatistics;
import com.cet.electric.ngapserver.util.excel.OptimizedExcelProcessor;
import com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ExcelUtils {

    private static final Logger log = LoggerFactory.getLogger(ExcelUtils.class);

    // 优化处理器实例
    private static final OptimizedExcelProcessor optimizedProcessor = new OptimizedExcelProcessor();

    public static void main(String[] args) {
        String filePath = "C:\\Users\\<USER>\\Desktop\\Cet\\工作记录\\2025.06\\2025-06-16~2025-06-20\\工作簿1.xlsx";
        List<Metric> jsonResult = extractMetrics(filePath);
        System.out.println(jsonResult);
    }

    /**
     * 从Excel文件中提取指标结构（优化版本）
     *
     * @param filePath Excel文件路径
     * @return 指标列表，包含根指标及其子指标
     */
    public static List<Metric> extractMetrics(String filePath) {
        // 检查文件大小，决定使用哪种处理方式
        java.io.File file = new java.io.File(filePath);
        long fileSize = file.length();

        if (fileSize > 1024 * 1024) { // 1MB以上使用优化处理器
            log.info("检测到大文件({}MB)，使用优化处理器", String.format("%.2f", fileSize / 1024.0 / 1024.0));
            return optimizedProcessor.extractMetricsOptimized(filePath);
        } else {
            // 小文件使用原有逻辑
            return extractMetricsLegacy(filePath);
        }
    }

    /**
     * 从Excel文件中提取指标结构（传统方式）
     *
     * @param filePath Excel文件路径
     * @return 指标列表，包含根指标及其子指标
     */
    private static List<Metric> extractMetricsLegacy(String filePath) {
        List<Metric> rootMetrics = new ArrayList<>();
        Metric rootMetric = createRootMetric();
        rootMetrics.add(rootMetric);

        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {

            for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
                processSheet(workbook.getSheetAt(sheetIndex), rootMetric);
            }

            return rootMetrics;

        } catch (Exception e) {
            log.error("提取指标失败: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 创建根指标
     *
     * @return 根指标对象
     */
    private static Metric createRootMetric() {
        return Metric.builder()
                .metricId("measuredDataMetric")
                .metricName("实测数据指标")
                .children(new ArrayList<>())
                .build();
    }

    /**
     * 处理Excel工作表，提取指标信息
     *
     * @param sheet     Excel工作表
     * @param rootMetric 根指标节点
     */
    private static void processSheet(Sheet sheet, Metric rootMetric) {
        if (sheet == null) return;

        String sheetName = sheet.getSheetName();
        Metric sheetMetric = createSheetMetric(sheetName);
        rootMetric.getChildren().add(sheetMetric);

        Map<String, Integer> columnIndexMap = new HashMap<>();
        List<String> metricColumns = analyzeHeader(sheet.getRow(0), columnIndexMap);

        for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
            processDataRow(sheet.getRow(rowNum), metricColumns, columnIndexMap, sheetMetric);
        }

        cleanupEmptyChildren(sheetMetric);
    }

    /**
     * 创建工作表指标
     *
     * @param sheetName 工作表名称
     * @return 工作表指标对象
     */
    private static Metric createSheetMetric(String sheetName) {
        return Metric.builder()
                .metricId(sheetName)
                .metricName(sheetName)
                .children(new ArrayList<>())
                .build();
    }

    /**
     * 分析表头，识别指标列
     *
     * @param headerRow       表头行
     * @param columnIndexMap  列名到索引的映射
     * @return 指标列名列表
     */
    private static List<String> analyzeHeader(Row headerRow, Map<String, Integer> columnIndexMap) {
        List<String> metricColumns = new ArrayList<>();

        if (headerRow == null) return metricColumns;

        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            Cell cell = headerRow.getCell(i);
            if (cell == null) continue;

            String columnName = cell.getStringCellValue().trim();
            columnIndexMap.put(columnName, i);

            if (isMetricColumn(columnName)) {
                metricColumns.add(columnName);
            }
        }
        return metricColumns;
    }

    /**
     * 判断列名是否为指标列
     *
     * @param columnName 列名
     * @return 如果是指标列返回true，否则返回false
     */
    private static boolean isMetricColumn(String columnName) {
        return !"日期".equals(columnName) &&
                !(columnName.startsWith("p") && isNumeric(columnName.substring(1))) &&
                !(columnName.startsWith("u") && isNumeric(columnName.substring(1)));
    }

    /**
     * 处理数据行，构建指标树
     *
     * @param dataRow        数据行
     * @param metricColumns  指标列名列表
     * @param columnIndexMap 列名到索引的映射
     * @param currentParent  当前父指标
     */
    private static void processDataRow(Row dataRow, List<String> metricColumns, Map<String, Integer> columnIndexMap, Metric currentParent) {
        if (dataRow == null) return;

        StringBuilder idBuilder = new StringBuilder(currentParent.getMetricId());

        for (String columnName : metricColumns) {
            int columnIndex = columnIndexMap.get(columnName);
            String cellValue = getCellValueAsString(dataRow.getCell(columnIndex));

            if (cellValue.isEmpty()) continue;

            String metricName = columnName + "-" + cellValue;
            idBuilder.append(".").append(metricName);
            String metricId = idBuilder.toString();

            Metric existingMetric = findChildByMetricName(currentParent, metricName);
            currentParent = (existingMetric != null) ? existingMetric : createAndAddNewMetric(currentParent, metricId, metricName);
        }
    }

    /**
     * 创建并添加新指标到父指标
     *
     * @param parent    父指标
     * @param metricId  新指标ID
     * @param metricName 新指标名称
     * @return 新创建的指标对象
     */
    private static Metric createAndAddNewMetric(Metric parent, String metricId, String metricName) {
        Metric newMetric = Metric.builder()
                .metricId(metricId)
                .metricName(metricName)
                .children(new ArrayList<>())
                .build();
        parent.getChildren().add(newMetric);
        return newMetric;
    }

    /**
     * 在父节点的直接子节点中查找指定名称的节点
     *
     * @param parent     父节点
     * @param metricName 要查找的指标名称
     * @return 找到的子节点，如果不存在则返回null
     */
    private static Metric findChildByMetricName(Metric parent, String metricName) {
        if (parent.getChildren() == null) return null;

        for (Metric child : parent.getChildren()) {
            if (child.getMetricName().equals(metricName)) {
                return child;
            }
        }
        return null;
    }

    /**
     * 递归清理空的子节点列表
     *
     * @param metric 指标对象
     */
    private static void cleanupEmptyChildren(Metric metric) {
        if (metric.getChildren() != null) {
            if (metric.getChildren().isEmpty()) {
                metric.setChildren(null);
            } else {
                for (Metric child : metric.getChildren()) {
                    cleanupEmptyChildren(child);
                }
            }
        }
    }

    /**
     * 获取单元格的值并转换为字符串
     *
     * @param cell 单元格对象
     * @return 单元格的字符串值
     */
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) return "";

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return getNumericCellValue(cell);
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 获取单元格的数值并转换为字符串
     *
     * @param cell 单元格对象
     * @return 数值的字符串表示
     */
    private static String getNumericCellValue(Cell cell) {
        try {
            if (cell.getCellType() == CellType.NUMERIC) {
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getLocalDateTimeCellValue().toString();
                }
                double value = cell.getNumericCellValue();
                long longValue = (long) value;
                return value == longValue ? String.valueOf(longValue) : String.valueOf(value);
            } else if (cell.getCellType() == CellType.STRING) {
                // 如果是字符串类型，直接返回字符串值
                return cell.getStringCellValue().trim();
            } else {
                log.debug("不支持的单元格类型: {}", cell.getCellType());
                return "";
            }
        } catch (Exception e) {
            log.debug("无法获取单元格数值，返回空字符串: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 判断字符串是否为数字
     *
     * @param str 要判断的字符串
     * @return 如果是数字返回true，否则返回false
     */
    private static boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        for (char c : str.toCharArray()) {
            if (!Character.isDigit(c)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取指定指标的数据（优化版本）
     *
     * @param filePath Excel文件路径
     * @param metric   需要获取数据的指标
     * @return 指标数据对象，包括数据点和统计信息
     */
    public static MetricData getMetricData(String filePath, Metric metric) {
        // 检查文件大小，决定使用哪种处理方式
        java.io.File file = new java.io.File(filePath);
        long fileSize = file.length();

        if (fileSize > 1024 * 1024) { // 1MB以上使用优化处理器
            log.info("检测到大文件({}MB)，使用优化处理器获取指标数据", String.format("%.2f", fileSize / 1024.0 / 1024.0));
            return optimizedProcessor.getMetricDataOptimized(filePath, metric);
        } else {
            // 小文件使用原有逻辑
            return getMetricDataLegacy(filePath, metric);
        }
    }

    /**
     * 获取指定指标的数据（传统方式）
     *
     * @param filePath Excel文件路径
     * @param metric   需要获取数据的指标
     * @return 指标数据对象，包括数据点和统计信息
     */
    private static MetricData getMetricDataLegacy(String filePath, Metric metric) {
        log.info("开始获取指标数据, 指标ID: {}", metric.getMetricId());

        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {

            String metricId = metric.getMetricId();
            String[] parts = metricId.split("\\.");
            if (parts.length < 1) {
                log.error("指标ID格式不正确: {}", metricId);
                return null;
            }

            Sheet sheet = workbook.getSheet(parts[0]);
            if (sheet == null) {
                log.error("找不到指定的sheet: {}", parts[0]);
                return null;
            }

            Map<String, Integer> columnIndexMap = new HashMap<>();
            List<String> pColumns = new ArrayList<>();
            List<String> uColumns = new ArrayList<>();
            int dateColumnIndex = analyzeDataHeader(sheet.getRow(0), columnIndexMap, pColumns, uColumns);

            if (dateColumnIndex == -1) {
                log.error("找不到日期列");
                return null;
            }

            List<DataPoint> dataPoints = extractDataPoints(sheet, columnIndexMap, dateColumnIndex, pColumns, uColumns, parts);
            MetricStatistics statistics = calculateStatistics(dataPoints, metric);

            return MetricData.builder()
                    .metric(metric)
                    .xUnit(determineUnit(metric, uColumns))
                    .dataPoints(dataPoints)
                    .statistics(statistics)
                    .build();

        } catch (Exception e) {
            log.error("获取指标数据失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 分析数据表头，识别日期列和指标列
     *
     * @param headerRow       表头行
     * @param columnIndexMap  列名到索引的映射
     * @param pColumns        存储p列的列表
     * @param uColumns        存储u列的列表
     * @return 日期列的索引，如果未找到返回-1
     */
    private static int analyzeDataHeader(Row headerRow, Map<String, Integer> columnIndexMap, List<String> pColumns, List<String> uColumns) {
        int dateColumnIndex = -1;

        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            Cell cell = headerRow.getCell(i);
            if (cell == null) continue;

            String columnName = cell.getStringCellValue().trim();
            columnIndexMap.put(columnName, i);

            if ("日期".equals(columnName)) {
                dateColumnIndex = i;
            } else if (columnName.startsWith("p") && isNumeric(columnName.substring(1))) {
                pColumns.add(columnName);
            } else if (columnName.startsWith("u") && isNumeric(columnName.substring(1))) {
                uColumns.add(columnName);
            }
        }
        return dateColumnIndex;
    }

    /**
     * 提取数据点
     *
     * @param sheet           Excel工作表
     * @param columnIndexMap  列名到索引的映射
     * @param dateColumnIndex 日期列的索引
     * @param pColumns        存储p列的列表
     * @param uColumns        存储u列的列表
     * @param parts           指标ID的各部分
     * @return 数据点列表
     */
    private static List<DataPoint> extractDataPoints(Sheet sheet, Map<String, Integer> columnIndexMap, int dateColumnIndex, List<String> pColumns, List<String> uColumns, String[] parts) {
        List<DataPoint> dataPoints = new ArrayList<>();
        Map<String, String> pathCriteria = extractPathCriteria(parts);
        Set<LocalDate> processedDates = new HashSet<>(); // 用于存储已处理的日期

        for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
            Row dataRow = sheet.getRow(rowNum);
            if (dataRow == null) continue;

            if (matchesPathCriteria(dataRow, columnIndexMap, pathCriteria)) {
                String dateStr = getCellValueAsString(dataRow.getCell(dateColumnIndex));
                if (dateStr.isEmpty()) continue;

                LocalDate date = parseDate(dateStr);
                if (date != null && processedDates.add(date)) { // 如果日期不重复，则处理数据
                    processDataPoints(dataRow, pColumns, date, columnIndexMap, dataPoints, null, null);
                    processDataPoints(dataRow, uColumns, date, columnIndexMap, dataPoints, null, null);
                }
            }
        }

        // 按时间戳排序数据点
        dataPoints.sort((dp1, dp2) -> {
            try {
                // 解析时间戳进行比较
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
                LocalDateTime time1 = LocalDateTime.parse(dp1.getTimestamp(), formatter);
                LocalDateTime time2 = LocalDateTime.parse(dp2.getTimestamp(), formatter);
                return time1.compareTo(time2);
            } catch (Exception e) {
                // 如果时间解析失败，按字符串排序
                log.debug("时间戳解析失败，使用字符串排序: {}, {}", dp1.getTimestamp(), dp2.getTimestamp());
                return dp1.getTimestamp().compareTo(dp2.getTimestamp());
            }
        });

        log.debug("数据点按时间排序完成，共 {} 个数据点", dataPoints.size());
        return dataPoints;
    }

    /**
     * 提取路径标准
     *
     * @param parts 指标ID的各部分
     * @return 路径标准映射
     */
    private static Map<String, String> extractPathCriteria(String[] parts) {
        Map<String, String> pathCriteria = new HashMap<>();
        for (int i = 1; i < parts.length; i++) {
            String[] kvPair = parts[i].split("-", 2);
            if (kvPair.length == 2) {
                pathCriteria.put(kvPair[0], kvPair[1]);
            }
        }
        return pathCriteria;
    }

    /**
     * 检查当前行是否匹配路径标准
     *
     * @param dataRow        数据行
     * @param columnIndexMap 列名到索引的映射
     * @param pathCriteria   路径标准映射
     * @return 如果匹配返回true，否则返回false
     */
    private static boolean matchesPathCriteria(Row dataRow, Map<String, Integer> columnIndexMap, Map<String, String> pathCriteria) {
        for (Map.Entry<String, String> entry : pathCriteria.entrySet()) {
            String columnName = entry.getKey();
            String expectedValue = entry.getValue();

            Integer columnIndex = columnIndexMap.get(columnName);
            if (columnIndex == null) {
                return false;
            }

            String actualValue = getCellValueAsString(dataRow.getCell(columnIndex));
            if (!expectedValue.equals(actualValue)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 解析日期字符串为LocalDate对象
     *
     * @param dateStr 日期字符串
     * @return 解析后的LocalDate对象，如果解析失败返回null
     */
    private static LocalDate parseDate(String dateStr) {
        try {
            return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
        } catch (Exception e) {
            log.warn("无法解析日期: {}", dateStr);
            return null;
        }
    }

    /**
     * 计算数据点的统计信息
     *
     * @param dataPoints 数据点列表
     * @param metric    指标对象
     * @return 指标统计信息对象
     */
    public static MetricStatistics calculateStatistics(List<DataPoint> dataPoints, Metric metric) {
        if (dataPoints.isEmpty()) return null;

        double minValue = Double.MAX_VALUE;
        double maxValue = Double.MIN_VALUE;
        int qualifiedCount = 0;
        int totalCount = 0;


        if (metric.getMetricId().contains("电压") || metric.getMetricId().contains("V")) {
            minValue = 198.0;
            maxValue = 242.0;

            for (DataPoint point : dataPoints) {
                if (point.getValue() != null) {
                    totalCount++;

                    if (isWithinBounds(point.getValue(), metric)) {
                        qualifiedCount++;
                    }
                }
            }
        } else if (metric.getMetricId().contains("功率")) {

            return null;

        }

        return MetricStatistics.builder()
                .min(minValue)
                .max(maxValue)
                .rate(calculateQualificationRate(qualifiedCount, totalCount))
                .build();
    }

    /**
     * 判断值是否在指标的合格范围内
     *
     * @param value  指标值
     * @param metric 指标对象
     * @return 如果在合格范围内返回true，否则返回false
     */
    private static boolean isWithinBounds(Double value, Metric metric) {
        if (metric.getMetricId().contains("电压") || metric.getMetricId().contains("V")) {
            return value >= 198.0 && value <= 242.0;
        }
        return false;
    }

    /**
     * 计算合格率
     *
     * @param qualifiedCount 合格数据点数量
     * @param totalCount     数据点总数量
     * @return 合格率
     */
    private static double calculateQualificationRate(int qualifiedCount, int totalCount) {
        return totalCount > 0 ? (double) qualifiedCount / totalCount * 100 : 0.0;
    }

    /**
     * 确定指标的单位
     *
     * @param metric   指标对象
     * @param uColumns u列列表
     * @return 指标单位
     */
    private static String determineUnit(Metric metric, List<String> uColumns) {
        if (metric.getMetricId().contains("电压") || !uColumns.isEmpty()) {
            return ""; // 根据需求设置单位
        }
        return "";
    }

    /**
     * 处理数据点（p列或u列）
     *
     * @param dataRow        数据行
     * @param columns        列名列表（p1-p96或u1-u96）
     * @param date           日期
     * @param columnIndexMap 列名到索引的映射
     * @param dataPoints     数据点列表
     * @param minLimit       最小限制值（可选）
     * @param maxLimit       最大限制值（可选）
     */
    private static void processDataPoints(Row dataRow, List<String> columns, LocalDate date,
                                          Map<String, Integer> columnIndexMap, List<DataPoint> dataPoints,
                                          Double minLimit, Double maxLimit) {
        for (String column : columns) {
            Integer columnIndex = columnIndexMap.get(column);
            if (columnIndex == null) continue;
            String valueStr = getCellValueAsString(dataRow.getCell(columnIndex));
            if (valueStr.isEmpty()) continue;
            Double value = parseValue(valueStr);
            if (value == null) continue;
            int index = parseIndex(column);
            if (index == -1) continue;
            LocalDateTime dateTime = date.atStartOfDay().plusMinutes((index - 1) * 15L);
            String formattedDateTime = dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
            if (minLimit != null && maxLimit != null) {
                value = applyLimits(value, minLimit, maxLimit);
            }
            DataPoint dataPoint = DataPoint.builder()
                    .timestamp(formattedDateTime)
                    .value(value)
                    .build();
            dataPoints.add(dataPoint);
        }
    }
    /**
     * 解析字符串为数值
     *
     * @param valueStr 要解析的字符串
     * @return 解析后的数值，如果解析失败返回null
     */
    private static Double parseValue(String valueStr) {
        try {
            return Double.parseDouble(valueStr);
        } catch (NumberFormatException e) {
            return null;
        }
    }
    /**
     * 解析列名中的索引
     *
     * @param column 列名
     * @return 列索引，如果解析失败返回-1
     */
    private static int parseIndex(String column) {
        try {
            return Integer.parseInt(column.substring(1));
        } catch (NumberFormatException e) {
            return -1;
        }
    }
    /**
     * 应用限制条件到数值
     *
     * @param value     原始数值
     * @param minLimit  最小限制值
     * @param maxLimit  最大限制值
     * @return 应用限制后的数值
     */
    private static Double applyLimits(Double value, Double minLimit, Double maxLimit) {
        if (value < minLimit) return minLimit;
        if (value > maxLimit) return maxLimit;
        return value;
    }

    /**
     * 生成指标数据Excel文件，每个指标有独立的时间列和值列
     *
     * @param metricDataList 指标数据列表
     * @param pic           图表Base64字符串，可为null
     * @param outputStream  输出流
     * @throws IOException  IO异常
     */
    public static void generateMetricExcel(List<MetricData> metricDataList, String pic, OutputStream outputStream) throws IOException {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            // 创建数据工作表
            XSSFSheet dataSheet = workbook.createSheet("指标数据");
            int rowNum = 0;

            // 创建标题行
            Row headerRow = dataSheet.createRow(rowNum++);
            int colIndex = 0;

            // 为每个指标创建两列标题（时间和值）
            for (MetricData metricData : metricDataList) {
                String metricName = metricData.getMetric().getMetricName();
                headerRow.createCell(colIndex++).setCellValue(metricName + " - 时间");
                headerRow.createCell(colIndex++).setCellValue(metricName + " - 值");
            }

            // 确定所有指标数据中的最大数据点数量
            int maxDataPoints = 0;
            for (MetricData metricData : metricDataList) {
                if (metricData.getDataPoints() != null && metricData.getDataPoints().size() > maxDataPoints) {
                    maxDataPoints = metricData.getDataPoints().size();
                }
            }

            // 填充数据行
            for (int i = 0; i < maxDataPoints; i++) {
                Row dataRow = dataSheet.createRow(rowNum++);
                colIndex = 0;

                // 对于每个指标，添加时间和值两列
                for (MetricData metricData : metricDataList) {
                    List<DataPoint> dataPoints = metricData.getDataPoints();

                    if (i < dataPoints.size()) {
                        // 添加时间戳
                        dataRow.createCell(colIndex++).setCellValue(dataPoints.get(i).getTimestamp());
                        // 添加指标值
                        dataRow.createCell(colIndex++).setCellValue(dataPoints.get(i).getValue());
                    } else {
                        // 如果该指标没有这么多数据点，则留空
                        dataRow.createCell(colIndex++).setCellValue("");
                        dataRow.createCell(colIndex++).setCellValue("");
                    }
                }
            }

            // 如果提供了图表数据，创建图表工作表
            if (pic != null && !pic.isEmpty()) {
                XSSFSheet chartSheet = workbook.createSheet("图表");

                // 解码Base64图片
                byte[] imageBytes = Base64.getDecoder().decode(pic.split(",")[1]);
                int pictureIdx = workbook.addPicture(imageBytes, Workbook.PICTURE_TYPE_PNG);

                // 创建绘图对象
                XSSFDrawing drawing = chartSheet.createDrawingPatriarch();
                XSSFClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, 0, 0, 15, 20);

                // 添加图片
                drawing.createPicture(anchor, pictureIdx);
            }

            // 自动调整列宽
            for (int i = 0; i < metricDataList.size() * 2; i++) {
                dataSheet.autoSizeColumn(i);
            }

            // 写入输出流
            workbook.write(outputStream);
            outputStream.flush();
        }
    }

}
