package com.cet.electric.ngapserver.util;

import com.cet.electric.ngapserver.entity.DataPoint;
import com.cet.electric.ngapserver.entity.Metric;
import com.cet.electric.ngapserver.entity.MetricData;
import com.cet.electric.ngapserver.entity.MetricStatistics;
import com.cet.electric.ngapserver.util.excel.ExcelCacheManager;
import com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.*;
import java.nio.file.Files;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ExcelUtils.class})
public class ExcelUtilsTest {

    private File tempDir;
    private File testExcelFile;

    @Before
    public void setUp() throws IOException {
        // 创建临时目录和文件用于测试
        tempDir = Files.createTempDirectory("excelutils_test").toFile();
        testExcelFile = new File(tempDir, "test.xlsx");
        
        // 创建测试Excel文件
        createTestExcelFile();
    }

    @After
    public void tearDown() throws IOException {
        // 清理测试文件和目录
        if (testExcelFile.exists()) {
            testExcelFile.delete();
        }
        if (tempDir.exists()) {
            deleteDirectory(tempDir);
        }

        // 清理缓存和性能统计
        if (ExcelCacheManager.getInstance() != null) {
            ExcelCacheManager.getInstance().clear();
        }
        if (VoltagePerformanceMonitor.getInstance() != null) {
            VoltagePerformanceMonitor.getInstance().reset();
        }
    }

    /**
     * 递归删除目录
     */
    private void deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }

    private void createTestExcelFile() throws IOException {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            // 创建测试工作表
            Sheet sheet = workbook.createSheet("测试数据");
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("日期");
            headerRow.createCell(1).setCellValue("设备类型");
            headerRow.createCell(2).setCellValue("设备编号");
            headerRow.createCell(3).setCellValue("p1");
            headerRow.createCell(4).setCellValue("p2");
            headerRow.createCell(5).setCellValue("u1");
            headerRow.createCell(6).setCellValue("u2");
            
            // 创建数据行
            Row dataRow1 = sheet.createRow(1);
            dataRow1.createCell(0).setCellValue("2023-01-01");
            dataRow1.createCell(1).setCellValue("变压器");
            dataRow1.createCell(2).setCellValue("001");
            dataRow1.createCell(3).setCellValue(100.5);
            dataRow1.createCell(4).setCellValue(200.3);
            dataRow1.createCell(5).setCellValue(220.0);
            dataRow1.createCell(6).setCellValue(230.0);
            
            Row dataRow2 = sheet.createRow(2);
            dataRow2.createCell(0).setCellValue("2023-01-02");
            dataRow2.createCell(1).setCellValue("变压器");
            dataRow2.createCell(2).setCellValue("001");
            dataRow2.createCell(3).setCellValue(105.2);
            dataRow2.createCell(4).setCellValue(195.8);
            dataRow2.createCell(5).setCellValue(225.0);
            dataRow2.createCell(6).setCellValue(235.0);
            
            // 写入文件
            try (FileOutputStream fos = new FileOutputStream(testExcelFile)) {
                workbook.write(fos);
            }
        }
    }

    @Test
    public void testExtractMetrics_Success() {
        // 执行测试
        List<Metric> metrics = ExcelUtils.extractMetrics(testExcelFile.getAbsolutePath());

        // 验证结果
        assertNotNull(metrics);
        assertFalse(metrics.isEmpty());
        
        // 验证根指标
        Metric rootMetric = metrics.get(0);
        assertEquals("measuredDataMetric", rootMetric.getMetricId());
        assertEquals("实测数据指标", rootMetric.getMetricName());
        assertNotNull(rootMetric.getChildren());
        assertFalse(rootMetric.getChildren().isEmpty());
        
        // 验证工作表指标
        Metric sheetMetric = rootMetric.getChildren().get(0);
        assertEquals("测试数据", sheetMetric.getMetricName());
        assertNotNull(sheetMetric.getChildren());
    }

    @Test
    public void testExtractMetrics_FileNotFound() {
        // 执行测试 - 文件不存在
        List<Metric> metrics = ExcelUtils.extractMetrics("/non/existing/file.xlsx");

        // 验证结果
        assertNotNull(metrics);
        assertTrue(metrics.isEmpty());
    }

    @Test
    public void testExtractMetrics_EmptyFile() throws IOException {
        // 创建空的Excel文件
        File emptyFile = new File(tempDir, "empty.xlsx");
        try (XSSFWorkbook workbook = new XSSFWorkbook();
             FileOutputStream fos = new FileOutputStream(emptyFile)) {
            workbook.write(fos);
        }

        // 执行测试
        List<Metric> metrics = ExcelUtils.extractMetrics(emptyFile.getAbsolutePath());

        // 验证结果
        assertNotNull(metrics);
        assertFalse(metrics.isEmpty());
        
        Metric rootMetric = metrics.get(0);
        assertEquals("measuredDataMetric", rootMetric.getMetricId());
        
        // 清理
        emptyFile.delete();
    }

    @Test
    public void testGetMetricData_Success() {
        // 准备测试数据
        Metric testMetric = Metric.builder()
                .metricId("测试数据.设备类型-变压器.设备编号-001")
                .metricName("测试指标")
                .build();

        // 执行测试
        MetricData metricData = ExcelUtils.getMetricData(testExcelFile.getAbsolutePath(), testMetric);

        // 验证结果
        assertNotNull(metricData);
        assertEquals(testMetric, metricData.getMetric());
        assertNotNull(metricData.getDataPoints());
        assertTrue(metricData.getDataPoints().isEmpty());
    }

    @Test
    public void testGetMetricData_InvalidMetricId() {
        // 准备测试数据 - 无效的指标ID
        Metric invalidMetric = Metric.builder()
                .metricId("invalid")
                .metricName("无效指标")
                .build();

        // 执行测试
        MetricData metricData = ExcelUtils.getMetricData(testExcelFile.getAbsolutePath(), invalidMetric);

        // 验证结果
        assertNull(metricData);
    }

    @Test
    public void testGetMetricData_SheetNotFound() {
        // 准备测试数据 - 工作表不存在
        Metric testMetric = Metric.builder()
                .metricId("不存在的工作表.设备类型-变压器")
                .metricName("测试指标")
                .build();

        // 执行测试
        MetricData metricData = ExcelUtils.getMetricData(testExcelFile.getAbsolutePath(), testMetric);

        // 验证结果
        assertNull(metricData);
    }

    @Test
    public void testCalculateStatistics_VoltageMetric() {
        // 准备测试数据 - 电压指标
        Metric voltageMetric = Metric.builder()
                .metricId("电压指标")
                .metricName("电压")
                .build();

        List<DataPoint> dataPoints = Arrays.asList(
                DataPoint.builder().value(220.0).timestamp("2023-01-01 00:00").build(),
                DataPoint.builder().value(230.0).timestamp("2023-01-01 01:00").build(),
                DataPoint.builder().value(190.0).timestamp("2023-01-01 02:00").build(), // 不合格
                DataPoint.builder().value(250.0).timestamp("2023-01-01 03:00").build()  // 不合格
        );

        // 执行测试
        MetricStatistics statistics = ExcelUtils.calculateStatistics(dataPoints, voltageMetric);

        // 验证结果
        assertNotNull(statistics);
        assertEquals(198.0, statistics.getMin(), 0.01);
        assertEquals(242.0, statistics.getMax(), 0.01);
        assertEquals(50.0, statistics.getRate(), 0.01); // 2/4 = 50%
    }

    @Test
    public void testCalculateStatistics_PowerMetric() {
        // 准备测试数据 - 功率指标
        Metric powerMetric = Metric.builder()
                .metricId("功率指标")
                .metricName("功率")
                .build();

        List<DataPoint> dataPoints = Arrays.asList(
                DataPoint.builder().value(100.0).timestamp("2023-01-01 00:00").build(),
                DataPoint.builder().value(200.0).timestamp("2023-01-01 01:00").build()
        );

        // 执行测试
        MetricStatistics statistics = ExcelUtils.calculateStatistics(dataPoints, powerMetric);

        // 验证结果 - 功率指标返回null
        assertNull(statistics);
    }

    @Test
    public void testCalculateStatistics_EmptyDataPoints() {
        // 准备测试数据 - 空数据点列表
        Metric testMetric = Metric.builder()
                .metricId("测试指标")
                .metricName("测试")
                .build();

        List<DataPoint> emptyDataPoints = new ArrayList<>();

        // 执行测试
        MetricStatistics statistics = ExcelUtils.calculateStatistics(emptyDataPoints, testMetric);

        // 验证结果
        assertNull(statistics);
    }

    @Test
    public void testGenerateMetricExcel_Success() throws IOException {
        // 准备测试数据
        List<MetricData> metricDataList = Arrays.asList(
                MetricData.builder()
                        .metric(Metric.builder().metricName("电压").build())
                        .dataPoints(Arrays.asList(
                                DataPoint.builder().timestamp("2023-01-01 00:00").value(220.0).build(),
                                DataPoint.builder().timestamp("2023-01-01 01:00").value(230.0).build()
                        ))
                        .build(),
                MetricData.builder()
                        .metric(Metric.builder().metricName("电流").build())
                        .dataPoints(Arrays.asList(
                                DataPoint.builder().timestamp("2023-01-01 00:00").value(10.0).build(),
                                DataPoint.builder().timestamp("2023-01-01 01:00").value(15.0).build()
                        ))
                        .build()
        );

        // 执行测试
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtils.generateMetricExcel(metricDataList, null, outputStream);

        // 验证结果
        byte[] excelBytes = outputStream.toByteArray();
        assertTrue(excelBytes.length > 0);

        // 验证生成的Excel文件结构
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(excelBytes);
             XSSFWorkbook workbook = new XSSFWorkbook(inputStream)) {
            
            assertEquals(1, workbook.getNumberOfSheets());
            Sheet dataSheet = workbook.getSheetAt(0);
            assertEquals("指标数据", dataSheet.getSheetName());
            
            // 验证表头
            Row headerRow = dataSheet.getRow(0);
            assertNotNull(headerRow);
            assertEquals("电压 - 时间", headerRow.getCell(0).getStringCellValue());
            assertEquals("电压 - 值", headerRow.getCell(1).getStringCellValue());
            assertEquals("电流 - 时间", headerRow.getCell(2).getStringCellValue());
            assertEquals("电流 - 值", headerRow.getCell(3).getStringCellValue());
            
            // 验证数据行
            Row dataRow1 = dataSheet.getRow(1);
            assertNotNull(dataRow1);
            assertEquals("2023-01-01 00:00", dataRow1.getCell(0).getStringCellValue());
            assertEquals(220.0, dataRow1.getCell(1).getNumericCellValue(), 0.01);
        }
    }

    @Test
    public void testGenerateMetricExcel_WithChart() throws IOException {
        // 准备测试数据 - 包含图表
        List<MetricData> metricDataList = Arrays.asList(
                MetricData.builder()
                        .metric(Metric.builder().metricName("测试指标").build())
                        .dataPoints(Arrays.asList(
                                DataPoint.builder().timestamp("2023-01-01 00:00").value(100.0).build()
                        ))
                        .build()
        );

        // 创建简单的Base64图片数据（1x1像素的PNG）
        String base64Image = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";

        // 执行测试
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtils.generateMetricExcel(metricDataList, base64Image, outputStream);

        // 验证结果
        byte[] excelBytes = outputStream.toByteArray();
        assertTrue(excelBytes.length > 0);

        // 验证生成的Excel文件包含图表工作表
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(excelBytes);
             XSSFWorkbook workbook = new XSSFWorkbook(inputStream)) {
            
            assertEquals(2, workbook.getNumberOfSheets());
            assertEquals("指标数据", workbook.getSheetAt(0).getSheetName());
            assertEquals("图表", workbook.getSheetAt(1).getSheetName());
        }
    }

    @Test
    public void testGenerateMetricExcel_EmptyMetricList() throws IOException {
        // 准备测试数据 - 空指标列表
        List<MetricData> emptyMetricDataList = new ArrayList<>();

        // 执行测试
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtils.generateMetricExcel(emptyMetricDataList, null, outputStream);

        // 验证结果
        byte[] excelBytes = outputStream.toByteArray();
        assertTrue(excelBytes.length > 0);

        // 验证生成的Excel文件结构
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(excelBytes);
             XSSFWorkbook workbook = new XSSFWorkbook(inputStream)) {
            
            assertEquals(1, workbook.getNumberOfSheets());
            Sheet dataSheet = workbook.getSheetAt(0);
            assertEquals("指标数据", dataSheet.getSheetName());
            
            // 验证只有表头行
            assertEquals(1, dataSheet.getLastRowNum() + 1);
        }
    }

    // ========== ExcelUtils优化功能测试 ==========

    @Test
    public void testOptimizedProcessorThreshold() throws IOException {
        // 创建一个大于1MB的测试文件
        File largeFile = createLargeTestFile();

        // 测试优化的指标提取
        long startTime = System.currentTimeMillis();
        List<Metric> metrics = ExcelUtils.extractMetrics(largeFile.getAbsolutePath());
        long duration = System.currentTimeMillis() - startTime;

        assertNotNull("优化处理器提取的指标不应为空", metrics);
        assertTrue("应该有指标数据", metrics.size() > 0);
        System.out.println("优化处理器指标提取耗时: " + duration + "ms");

        // 验证缓存效果
        ExcelCacheManager.CacheStats cacheStats = ExcelCacheManager.getInstance().getStats();
        assertTrue("应该有缓存数据", cacheStats.size >= 0);

        // 清理测试文件
        largeFile.delete();
    }

    @Test
    public void testFileSizeThreshold() throws IOException {
        // 创建小文件（小于1MB）
        File smallFile = new File(tempDir, "small_test.xlsx");
        createSmallTestFile(smallFile);

        // 创建大文件（大于1MB）
        File largeFile = createLargeTestFile();

        // 测试小文件使用传统方式
        List<Metric> smallMetrics = ExcelUtils.extractMetrics(smallFile.getAbsolutePath());
        assertNotNull("小文件指标不应为空", smallMetrics);

        // 测试大文件使用优化方式
        List<Metric> largeMetrics = ExcelUtils.extractMetrics(largeFile.getAbsolutePath());
        assertNotNull("大文件指标不应为空", largeMetrics);

        // 验证两种方式都能正常工作
        assertTrue("小文件应该有指标", smallMetrics.size() > 0);
        assertTrue("大文件应该有指标", largeMetrics.size() > 0);

        // 清理测试文件
        smallFile.delete();
        largeFile.delete();
    }

    @Test
    public void testCacheEfficiencyWithOptimization() throws IOException {
        File testFile = createLargeTestFile();
        String filePath = testFile.getAbsolutePath();

        // 清空缓存
        ExcelCacheManager.getInstance().clear();

        // 第一次提取（无缓存）
        long startTime1 = System.currentTimeMillis();
        List<Metric> metrics1 = ExcelUtils.extractMetrics(filePath);
        long duration1 = System.currentTimeMillis() - startTime1;

        // 第二次提取（有缓存）
        long startTime2 = System.currentTimeMillis();
        List<Metric> metrics2 = ExcelUtils.extractMetrics(filePath);
        long duration2 = System.currentTimeMillis() - startTime2;

        assertNotNull("第一次提取结果不应为空", metrics1);
        assertNotNull("第二次提取结果不应为空", metrics2);
        assertEquals("两次提取结果应该相同", metrics1.size(), metrics2.size());

        // 第二次应该更快（缓存效果）
        assertTrue("缓存应该提升性能", duration2 <= duration1);

        System.out.println("缓存效果测试 - 第一次: " + duration1 + "ms, 第二次: " + duration2 + "ms");

        // 验证缓存统计
        ExcelCacheManager.CacheStats stats = ExcelCacheManager.getInstance().getStats();
        assertTrue("应该有缓存命中", stats.hits >= 0);

        // 清理测试文件
        testFile.delete();
    }

    /**
     * 创建大于1MB的测试文件
     */
    private File createLargeTestFile() throws IOException {
        File file = new File(tempDir, "large_test.xlsx");

        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            // 创建多个工作表，每个工作表有大量数据
            for (int sheetIndex = 0; sheetIndex < 3; sheetIndex++) {
                Sheet sheet = workbook.createSheet("测试数据" + (sheetIndex + 1));

                // 创建表头
                Row headerRow = sheet.createRow(0);
                headerRow.createCell(0).setCellValue("日期");
                headerRow.createCell(1).setCellValue("设备类型");
                headerRow.createCell(2).setCellValue("设备编号");
                headerRow.createCell(3).setCellValue("位置");

                // 添加大量u列和p列
                for (int i = 4; i < 100; i++) {
                    if (i % 2 == 0) {
                        headerRow.createCell(i).setCellValue("u" + (i - 3));
                    } else {
                        headerRow.createCell(i).setCellValue("p" + (i - 3));
                    }
                }

                // 创建大量数据行
                for (int rowIndex = 1; rowIndex <= 500; rowIndex++) {
                    Row row = sheet.createRow(rowIndex);
                    row.createCell(0).setCellValue("2024010" + String.format("%02d", rowIndex % 31 + 1));
                    row.createCell(1).setCellValue("变压器");
                    row.createCell(2).setCellValue("T" + String.format("%03d", rowIndex % 100 + 1));
                    row.createCell(3).setCellValue("位置" + (rowIndex % 10 + 1));

                    // 填充电压和功率数据
                    for (int colIndex = 4; colIndex < 100; colIndex++) {
                        if (colIndex % 2 == 0) {
                            // 电压值 (198-242V)
                            row.createCell(colIndex).setCellValue(198 + Math.random() * 44);
                        } else {
                            // 功率值
                            row.createCell(colIndex).setCellValue(Math.random() * 1000);
                        }
                    }
                }
            }

            try (FileOutputStream fos = new FileOutputStream(file)) {
                workbook.write(fos);
            }
        }

        System.out.println("创建大文件: " + file.getName() + ", 大小: " + file.length() / 1024 + "KB");
        return file;
    }

    /**
     * 创建小测试文件
     */
    private void createSmallTestFile(File file) throws IOException {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("测试数据");

            // 创建简单的表头和数据
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("日期");
            headerRow.createCell(1).setCellValue("设备");
            headerRow.createCell(2).setCellValue("u1");
            headerRow.createCell(3).setCellValue("p1");

            // 创建少量数据行
            for (int i = 1; i <= 10; i++) {
                Row row = sheet.createRow(i);
                row.createCell(0).setCellValue("20240101");
                row.createCell(1).setCellValue("设备" + i);
                row.createCell(2).setCellValue(220.0);
                row.createCell(3).setCellValue(100.0);
            }

            try (FileOutputStream fos = new FileOutputStream(file)) {
                workbook.write(fos);
            }
        }
    }
}
