<template>
  <div class="input-wrap">
    <div
      v-if="label"
      class="input-label"
      :class="{ require: isRequire }"
      :style="{ minWidth: labelWidth, textAlign: textAlign }"
    >
      {{ label }}
    </div>
    <div class="input-main"><slot></slot></div>
  </div>
</template>

<script>
export default {
  name: "inputWrap",
  components: {},
  props: {
    label: {
      default: "",
      type: String
    },
    isRequire: {
      default: false,
      type: Boolean
    },

    labelWidth: {
      default: "auto",
      type: [String, Number]
    },
    textAlign: {
      default: "left",
      type: String
    }
  },
  data() {
    return {};
  },
  watch: {},
  methods: {}
};
</script>

<style lang="scss" scoped>
.input-wrap {
  display: flex;
}

.input-label {
  font-size: 14px;
  border: 1px solid;
  @include background_color(BG2);
  @include border_color(B1);
  @include font_color(T2);
  // background-color: #f5f7fa;
  // color: #909399;
  border-radius: 4px;
  padding: 0 8px;
  white-space: nowrap;
  line-height: 30px;
  border-right: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  box-sizing: border-box;
}
.input-main {
  flex: 1;
  // line-height: 28px;
  & > div:first-child {
    width: 100%;
  }
  ::v-deep input {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
.require::before {
  content: "*";
  @include font_color(Sta3);
  margin-right: 4px;
}
</style>
