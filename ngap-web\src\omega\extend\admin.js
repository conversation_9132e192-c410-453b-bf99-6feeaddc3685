import omegaApp from "@omega/app";
import { OmegaAdminPlugin } from "@omega/admin";
import { DashboradPagePlugin } from "@omega/admin/plugins/dashborad";
import { GraphPagePlugin } from "@omega/admin/plugins/graph";
import { SodaPagePlugin } from "@omega/admin/plugins/soda";
import { LinkPagePlugin } from "@omega/admin/plugins/link";
import { IFramePagePlugin } from "@omega/admin/plugins/iframe";

omegaApp.plugin.register(OmegaAdminPlugin, {
  apiPrefix: {
    "device-data-service": "/device-data"
    // "bff-service": "/bff"
  }
  // apiRequestInterceptor: function(config) {return config}
});

// 注意：使用下述组件时相关依赖需要在项目中自己安装
// 注册dashborad组件
omegaApp.plugin.register(DashboradPagePlugin);
// 注册cetgraph组件
omegaApp.plugin.register(GraphPagePlugin);
omegaApp.plugin.register(SodaPagePlugin);
omegaApp.plugin.register(LinkPagePlugin);
omegaApp.plugin.register(IFramePagePlugin);
