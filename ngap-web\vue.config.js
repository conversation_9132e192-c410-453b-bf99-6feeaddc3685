const CompressionWebpackPlugin = require("compression-webpack-plugin");
const omegaCliDevserverHandler = require("@omega/cli-devserver");
module.exports = omegaCliDevserverHandler({
  productionSourceMap: false,
  transpileDependencies: ["@omega"],
  css: {
    loaderOptions: {
      scss: {
        additionalData: `@import "@/resources/var.scss";`
      },
      css: {
        url: {
          filter(url, resourcePath) {
            // 根路径静态文件特殊处理
            if (url.startsWith("/")) {
              return false;
            }
            return true;
          }
        }
      },
      postcss: {
        postcssOptions: {
          plugins: [
            require("postcss-import"),
            require("tailwindcss/nesting"),
            require("tailwindcss")
          ]
        }
      }
    }
  },
  configureWebpack: {
    plugins: [
      // 开发环境下开启缓存
      ...(process.env.NODE_ENV === "development"
        ? // ? [new HardSourceWebpackPlugin()]
          []
        : [new CompressionWebpackPlugin()])
    ]
  },
  chainWebpack(config) {
    const path = require("path");
    function resolve(dir) {
      return path.join(__dirname, dir);
    }
    /* svgicon支持 */
    config.module.rule("svg").exclude.add(resolve("src/icons")).end();
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/icons"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "[name]"
      })
      .end();
  },
  devServer: {
    client: {
      overlay: false
    },
    port: 9528,
    open: false,
    // writeToDisk: true,
    proxy: {
      "^/ngap-server": {
        target: `http://10.12.135.167:28080`,
        changeOrigin: true,
        pathRewrite: {
          "^/ngap-server": "/ngap-server"
        }
      },
      "^/bff": {
        target: `http://10.12.137.52:3005`,
        changeOrigin: true,
        pathRewrite: {
          // "^/workorder": "/workorder"
        }
      }
    }
  }
});
