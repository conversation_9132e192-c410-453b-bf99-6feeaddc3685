package com.cet.electric.ngapserver.util;

import com.cet.electric.ngapserver.entity.CourtsStatistics;
import com.cet.electric.ngapserver.entity.UserStatistics;
import com.cet.electric.ngapserver.util.excel.ExcelCacheManager;
import com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor;
import com.cet.electric.ngapserver.util.voltage.VoltageObjectPool;
import com.cet.electric.ngapserver.util.voltage.VoltageStatisticsCalculator;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.TemporaryFolder;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

/**
 * 电压合格率报表优化功能测试
 * 
 * <AUTHOR>
 */
public class VoltageQualityReportOptimizedTest {

    @Rule
    public TemporaryFolder tempFolder = new TemporaryFolder();

    private File testExcelFile;
    private VoltageDataProcessor dataProcessor;
    private ExcelCacheManager cacheManager;

    @Before
    public void setUp() throws IOException {
        // 创建测试Excel文件
        testExcelFile = createTestExcelFile();
        
        // 初始化组件
        dataProcessor = new VoltageDataProcessor();
        cacheManager = ExcelCacheManager.getInstance();
        
        // 清空缓存
        cacheManager.clear();
    }

    @After
    public void tearDown() {
        // 清理缓存
        if (cacheManager != null) {
            cacheManager.clear();
        }
    }

    @Test
    public void testVoltageDataProcessorOptimized() {
        // 测试优化的数据读取
        List<VoltageDataProcessor.VoltageRecord> records = 
            dataProcessor.readVoltageDataOptimized(testExcelFile.getAbsolutePath());
        
        assertNotNull("记录列表不应为空", records);
        assertTrue("应该读取到数据", records.size() > 0);
        
        // 验证第一条记录
        VoltageDataProcessor.VoltageRecord firstRecord = records.get(0);
        assertEquals("用户A", firstRecord.userName);
        assertNotNull("电压值列表不应为空", firstRecord.voltageValues);
        assertTrue("应该有电压数据", firstRecord.voltageValues.size() > 0);
    }

    @Test
    public void testCacheEffectiveness() {
        String filePath = testExcelFile.getAbsolutePath();
        
        // 第一次读取（无缓存）
        long startTime1 = System.currentTimeMillis();
        List<VoltageDataProcessor.VoltageRecord> records1 = 
            dataProcessor.readVoltageDataOptimized(filePath);
        long duration1 = System.currentTimeMillis() - startTime1;
        
        // 第二次读取（有缓存）
        long startTime2 = System.currentTimeMillis();
        List<VoltageDataProcessor.VoltageRecord> records2 = 
            dataProcessor.readVoltageDataOptimized(filePath);
        long duration2 = System.currentTimeMillis() - startTime2;
        
        assertNotNull("第一次读取结果不应为空", records1);
        assertNotNull("第二次读取结果不应为空", records2);
        assertEquals("两次读取结果应该相同", records1.size(), records2.size());
        
        // 第二次应该更快（缓存效果）
        assertTrue("缓存应该提升性能", duration2 <= duration1);
        
        // 验证缓存统计
        ExcelCacheManager.CacheStats stats = cacheManager.getStats();
        assertTrue("应该有缓存命中", stats.hits > 0);
    }

    @Test
    public void testVoltageStatisticsCalculator() {
        VoltageStatisticsCalculator calculator = new VoltageStatisticsCalculator();
        
        // 添加测试数据
        List<Double> voltages = Arrays.asList(220.0, 200.0, 250.0, 190.0, 230.0);
        calculator.addVoltageValues(voltages);
        
        // 验证统计结果
        assertEquals("总读数应该正确", 5, calculator.getTotalReadings());
        assertEquals("合格读数应该正确", 3, calculator.getQualifiedReadings()); // 220, 200, 230
        assertEquals("超上限读数应该正确", 1, calculator.getAboveMaxReadings()); // 250
        assertEquals("低于下限读数应该正确", 1, calculator.getBelowMinReadings()); // 190
        
        // 验证比率计算
        assertEquals("合格率应该正确", 60.0, calculator.getQualifiedRate(), 0.01);
        assertEquals("超上限率应该正确", 20.0, calculator.getAboveMaxRate(), 0.01);
        assertEquals("低于下限率应该正确", 20.0, calculator.getBelowMinRate(), 0.01);
        
        // 验证最值
        assertEquals("最大值应该正确", 250.0, calculator.getMaxVoltage(), 0.01);
        assertEquals("最小值应该正确", 190.0, calculator.getMinVoltage(), 0.01);
    }

    @Test
    public void testObjectPoolEfficiency() {
        VoltageObjectPool objectPool = VoltageObjectPool.getInstance();
        
        // 测试Double列表池
        List<Double> list1 = objectPool.borrowDoubleList();
        List<Double> list2 = objectPool.borrowDoubleList();
        
        assertNotNull("借用的列表不应为空", list1);
        assertNotNull("借用的列表不应为空", list2);
        assertNotSame("应该是不同的对象", list1, list2);
        
        // 归还对象
        objectPool.returnDoubleList(list1);
        objectPool.returnDoubleList(list2);
        
        // 测试统计计算器池
        VoltageStatisticsCalculator calc1 = objectPool.borrowCalculator();
        VoltageStatisticsCalculator calc2 = objectPool.borrowCalculator();
        
        assertNotNull("借用的计算器不应为空", calc1);
        assertNotNull("借用的计算器不应为空", calc2);
        
        objectPool.returnCalculator(calc1);
        objectPool.returnCalculator(calc2);
        
        // 验证池状态
        VoltageObjectPool.PoolStats stats = objectPool.getStats();
        assertTrue("应该创建了对象", stats.doubleListCreated > 0);
        assertTrue("应该创建了计算器", stats.calculatorCreated > 0);
    }

    @Test
    public void testOptimizedReportGeneration() {
        String inputPath = testExcelFile.getAbsolutePath();
        
        // 生成优化报表
        VoltageQualityReport.generateVoltageReport(inputPath);
        
        // 验证报表文件是否生成
        File reportFile = new File(testExcelFile.getParent(), "电压合格率报表.xlsx");
        assertTrue("报表文件应该存在", reportFile.exists());
        assertTrue("报表文件应该有内容", reportFile.length() > 0);
        
        // 验证缓存是否生效
        ExcelCacheManager.CacheStats stats = cacheManager.getStats();
        assertTrue("应该有缓存数据", stats.size > 0);
    }

    /**
     * 创建测试用的Excel文件
     */
    private File createTestExcelFile() throws IOException {
        File file = tempFolder.newFile("test_voltage_data.xlsx");
        
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("用户电压");
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("用户名称");
            for (int i = 1; i <= 96; i++) {
                headerRow.createCell(i).setCellValue("u" + i);
            }
            
            // 创建测试数据
            String[] userNames = {"用户A", "用户B", "用户C"};
            double[][] voltageData = {
                {220.0, 200.0, 250.0, 190.0, 230.0}, // 用户A
                {215.0, 205.0, 235.0, 195.0, 225.0}, // 用户B
                {210.0, 240.0, 245.0, 185.0, 220.0}  // 用户C
            };
            
            for (int i = 0; i < userNames.length; i++) {
                Row row = sheet.createRow(i + 1);
                row.createCell(0).setCellValue(userNames[i]);
                
                // 填充电压数据（重复使用测试数据）
                for (int j = 1; j <= 96; j++) {
                    double voltage = voltageData[i][(j - 1) % voltageData[i].length];
                    row.createCell(j).setCellValue(voltage);
                }
            }
            
            try (FileOutputStream fos = new FileOutputStream(file)) {
                workbook.write(fos);
            }
        }
        
        return file;
    }
}
