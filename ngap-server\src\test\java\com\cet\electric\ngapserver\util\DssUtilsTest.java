package com.cet.electric.ngapserver.util;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DssUtils 单元测试
 * 
 * <AUTHOR>
 * @since 2025-06-20
 */
@RunWith(MockitoJUnitRunner.class)
public class DssUtilsTest {

    @Mock
    private RestTemplate restTemplate;

    private DssUtils dssUtils;

    @Before
    public void setUp() {
        dssUtils = new DssUtils(restTemplate);
    }

    @Test
    public void testRunDssWithStrategy_Success() {
        // 准备测试数据
        String dssFilePath = "/test/path/test.dss";
        String jsonData = "{\"strategy\":\"test\"}";
        String dssUrl = "http://localhost:8080";

        // Mock 成功响应
        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("strategy_result", "success");
        responseBody.put("output_data", "simulation completed successfully");
        ResponseEntity<Map> mockResponse = new ResponseEntity<>(responseBody, HttpStatus.OK);

        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(Map.class)))
                .thenReturn(mockResponse);

        // 执行测试
        ResponseEntity<Map> result = dssUtils.runDssWithStrategy(dssFilePath, jsonData, dssUrl);

        // 验证结果
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertNotNull(result.getBody());
        assertEquals("success", result.getBody().get("strategy_result"));
        assertEquals("simulation completed successfully", result.getBody().get("output_data"));
    }

    @Test
    public void testRunDssWithStrategy_ServerError() {
        // 准备测试数据
        String dssFilePath = "/test/path/test.dss";
        String jsonData = "{\"strategy\":\"test\"}";
        String dssUrl = "http://localhost:8080";

        // Mock 500错误响应
        Map<String, Object> errorBody = new HashMap<>();
        errorBody.put("error", "DSS execution failed");
        ResponseEntity<Map> mockResponse = new ResponseEntity<>(errorBody, HttpStatus.INTERNAL_SERVER_ERROR);

        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(Map.class)))
                .thenReturn(mockResponse);

        // 执行测试
        ResponseEntity<Map> result = dssUtils.runDssWithStrategy(dssFilePath, jsonData, dssUrl);

        // 验证结果
        assertNotNull(result);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, result.getStatusCode());
        assertNotNull(result.getBody());
        assertEquals("DSS execution failed", result.getBody().get("error"));
        // 验证自动添加的 output_data 字段
        assertEquals("DSS execution failed", result.getBody().get("output_data"));
    }

    @Test
    public void testRunDssWithStrategy_ServerErrorWithEmptyBody() {
        // 准备测试数据
        String dssFilePath = "/test/path/test.dss";
        String jsonData = "{\"strategy\":\"test\"}";
        String dssUrl = "http://localhost:8080";

        // Mock 500错误响应，空响应体
        ResponseEntity<Map> mockResponse = new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);

        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(Map.class)))
                .thenReturn(mockResponse);

        // 执行测试
        ResponseEntity<Map> result = dssUtils.runDssWithStrategy(dssFilePath, jsonData, dssUrl);

        // 验证结果
        assertNotNull(result);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, result.getStatusCode());
        assertNotNull(result.getBody());
        assertTrue(result.getBody().containsKey("error"));
        assertTrue(result.getBody().containsKey("output_data"));
        assertEquals("DSS服务执行失败", result.getBody().get("output_data"));
    }

    @Test
    public void testRunDssWithStrategy_ClientError() {
        // 准备测试数据
        String dssFilePath = "/test/path/test.dss";
        String jsonData = "{\"strategy\":\"test\"}";
        String dssUrl = "http://localhost:8080";

        // Mock 400错误响应
        Map<String, Object> errorBody = new HashMap<>();
        errorBody.put("error", "Bad request");
        ResponseEntity<Map> mockResponse = new ResponseEntity<>(errorBody, HttpStatus.BAD_REQUEST);

        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(Map.class)))
                .thenReturn(mockResponse);

        // 执行测试
        ResponseEntity<Map> result = dssUtils.runDssWithStrategy(dssFilePath, jsonData, dssUrl);

        // 验证结果
        assertNotNull(result);
        assertEquals(HttpStatus.BAD_REQUEST, result.getStatusCode());
        assertNotNull(result.getBody());
        assertEquals("Bad request", result.getBody().get("error"));
        assertEquals("Bad request", result.getBody().get("output_data"));
    }

    @Test
    public void testConstructor_SetsErrorHandler() {
        // 验证构造函数设置了错误处理器
        verify(restTemplate, times(1)).setErrorHandler(any(DssResponseErrorHandler.class));
    }
}
