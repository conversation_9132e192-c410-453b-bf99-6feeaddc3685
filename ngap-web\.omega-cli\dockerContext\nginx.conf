
user  nginx;
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;


    limit_conn_zone $binary_remote_addr zone=two:1m;
    limit_req_zone  $binary_remote_addr  zone=one:1m  rate=1r/s;//限制请求频率


    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';



    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    #gzip  on;

    #include /etc/nginx/conf.d/*.conf;
	
	server {
		#定义网站的ip 端口号
        listen       80;
        server_name  localhost;

        #charset koi8-r;
        charset utf-8;
        charset_types text/xml text/plain text/vnd.wap.wml application/javascript application/rss+xml text/css;
        # 修复安全漏洞：CVE-2016-2183
        ssl_ciphers HIGH:!aNULL:!MD5:!3DES;
        #access_log  logs/host.access.log  main;
        #前端部署静态资源服务器
        location / {
            root   /usr/share/nginx/html;
            index  index.html index.htm index.php;
            client_max_body_size 20m;
        }

        # location /track-service/v.gif {
        #     limit_conn   two  1;//限制ip同时请求数
        #     limit_req zone=one burst=5 nodelay;
        #     log_subrequest on;
        #     #日志的输出路径
        #     access_log logs\auto_report.log auto_report;
        #     #返回一个空的文件
        #     add_header Expires "Fri, 01 Jan 1980 00:00:00 GMT";
        #     add_header Pragma "no-cache";
        #     add_header Cache-Control "no-cache, max-age=0, must-revalidate";
        #     #返回一个1×1的空gif图片
        #     empty_gif;
        # }

    #    #网关
    # location /gateway {
    #   root /usr/share/nginx/html;
    #   index index.html index.htm index.php;
    #   proxy_pass http://gateway1:4001/;
    #   proxy_redirect default;
    #   client_max_body_size 1000m;
    # }
  
    # location /sloth {
    #   root html;
    #   index index.html index.htm index.php;
    #   proxy_pass http://sloth:8078/sloth;
    #   proxy_redirect default;
    #   client_max_body_size 1000m;
    #   proxy_set_header Host $host;
    #   proxy_set_header X-Real-IP $remote_addr;
    #   proxy_set_header REMOTE-HOST $remote_addr;
    #   proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    # }
    # location /backup {
    #   root html;
    #   index index.html index.htm index.php;
    #   proxy_pass http://backup:7001/datacenter/db-backup;
    #   proxy_redirect default;
    #   client_max_body_size 1000m;
    #   proxy_set_header Host $host;
    #   proxy_set_header X-Real-IP $remote_addr;
    #   proxy_set_header REMOTE-HOST $remote_addr;
    #   proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    # }
    # location /messageServer {
    #   root html;
    #   index index.html index.htm index.php;
    #   proxy_pass http://notice1:5070/;
    #   proxy_http_version 1.1;
    #   proxy_set_header Upgrade $http_upgrade;
    #   proxy_set_header Connection "upgrade";
    # }

    location /ngap-server {
        proxy_pass http://*************:28080/ngap-server;
        client_max_body_size 1000m;
        proxy_set_header Host $host;
        proxy_set_header Authorization $http_authorization;
	
    }

    location /bff {
        proxy_pass http://bff-service:3005;
        client_max_body_size 1000m;
        proxy_set_header Host $host;
        proxy_set_header Authorization $http_authorization;
    }


        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   /usr/share/nginx/html;
        }
    }
}
