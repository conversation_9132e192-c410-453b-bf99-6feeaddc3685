-- NGAP Server 数据库初始化脚本
-- 创建日期：2025-06-17
-- 作者：张蒲龙

-- 启用外键约束
PRAGMA foreign_keys = ON;

-- 删除已存在的表（如果存在）
DROP TABLE IF EXISTS simulation;
DROP TABLE IF EXISTS project;
DROP TABLE IF EXISTS device;

-- 创建项目表
CREATE TABLE project (
    project_id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_name TEXT NOT NULL,
    project_type TEXT NOT NULL,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    is_deleted INTEGER DEFAULT 0 CHECK (is_deleted IN (0, 1)),
    CONSTRAINT idx_project_name_not_deleted UNIQUE(project_name, is_deleted)
);

-- 创建仿真表
CREATE TABLE simulation (
    simulation_id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    project_name TEXT NOT NULL,
    simulation_name TEXT NOT NULL,
    simulation_draft TEXT,
    simulation_model TEXT,
    input_data TEXT,
    output_data TEXT,
    measured_data TEXT,
    simulation_script TEXT,
    nodes TEXT,
    control_strategy TEXT,
    run_status TEXT CHECK (run_status IN ('READY', 'RUNNING', 'SUCCESS', 'FAILED')),
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    is_deleted INTEGER DEFAULT 0 CHECK (is_deleted IN (0, 1)),
    FOREIGN KEY (project_id) REFERENCES project(project_id) ON DELETE CASCADE
);

-- 创建设备管理表
CREATE TABLE IF NOT EXISTS device (
    parameter_id INTEGER PRIMARY KEY AUTOINCREMENT,
    device_type VARCHAR(50) NOT NULL,
    parameter_code TEXT NOT NULL,
    version TEXT NOT NULL
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_project_name ON project(project_name);
CREATE INDEX IF NOT EXISTS idx_project_type ON project(project_type);
CREATE INDEX IF NOT EXISTS idx_project_created_at ON project(created_at);
CREATE INDEX IF NOT EXISTS idx_simulation_project_id ON simulation(project_id);
CREATE INDEX IF NOT EXISTS idx_simulation_name ON simulation(simulation_name);
CREATE INDEX IF NOT EXISTS idx_simulation_created_at ON simulation(created_at);
CREATE INDEX IF NOT EXISTS idx_device_type ON device(device_type);
CREATE INDEX IF NOT EXISTS idx_device_parameter_code ON device(parameter_code);
