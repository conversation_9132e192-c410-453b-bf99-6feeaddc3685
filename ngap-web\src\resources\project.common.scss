.frame-main-container-innerwrap {
  position: relative;
}
body {
  font-family: "Microsoft YaHei", sans-serif !important;
  input,
  textarea {
    font-family: "Microsoft YaHei", sans-serif !important;
  }
}
.page {
  height: 100%;
  width: 100%;

  &-header {
    height: 40px;
  }
  &-body {
    height: calc(100% - 40px);
  }
}

.cet-graph {
  .tooltip {
    color: #000 !important;
  }
}

.cet-graph-loading {
  background-color: rgba(255, 255, 255, 0) !important;
}

// ztree的拖拽
body > ul.zTreeDragUL {
  margin: 0;
  padding: 0;
  position: absolute;
  overflow: hidden;
  background-color: #dedede;
  border: 1px #4fcbf0 dotted;
  border-radius: 4px;
  opacity: 0.7;
}

.pointer {
  cursor: pointer;
}

.text-ZS {
  @include font_color(ZS);
}
.color-ZS {
  @include font_color(ZS);
}
.color-F1 {
  @include font_color(F1);
}
.color-F2 {
  @include font_color(F2);
}
.color-T1 {
  @include font_color(T1);
}
.color-T2 {
  @include font_color(T2);
}
.color-T3 {
  @include font_color(T3);
}
.color-T4 {
  @include font_color(T4);
}

.color-Sta1 {
  @include font_color(Sta1);
}
.color-Sta2 {
  @include font_color(Sta2);
}
.color-Sta3 {
  @include font_color(Sta3);
}
.color-Sta4 {
  @include font_color(Sta4);
}
.color-Sta5 {
  @include font_color(Sta5);
}
.fs-H {
  @include font_size(H);
}
.fs-H1 {
  @include font_size(H1);
}
.fs-H2 {
  @include font_size(H2);
}
.fs-H3 {
  @include font_size(H3);
}
.fs-Aa {
  @include font_size(Aa);
}
.fs-Ab {
  @include font_size(Ab);
}

.fs-I1 {
  @include font_size(I1);
}
.fs-I2 {
  @include font_size(I2);
}
.fs-I3 {
  @include font_size(I3);
}
.fs-I4 {
  @include font_size(I4);
}
.fs-I5 {
  @include font_size(I5);
}
.border-B1 {
  border: 1px solid;
  @include border_color(B1);
}
.border-B2 {
  border: 1px solid;
  @include border_color(B1);
}
.alarm-tag-width {
  display: inline-block;
  vertical-align: middle;
  margin: 0 auto;
  width: 68px;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  @include font_size(Ab);
}
// 容器内单个元素 水平垂直居中
.single-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.indexbg-content {
  // background: url(/static/assets/bg.png) no-repeat center center;
  background-position: center center;
  background-repeat: no-repeat;
  display: block;
  background-size: contain;
}
.flex {
  display: flex;
}
.flex1 {
  flex: 1;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.space-between {
  display: flex;
  justify-content: space-between;
}
.space-around {
  display: flex;
  justify-content: space-around;
}
.center {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
}
.slot-wrap {
  display: flex;
}
.prepend {
  font-size: 14px;
  text-align: center;
  @include background_color(BG1);
  @include font_color(T1);
  border: 1px solid;
  @include border_color(B1);
  border-radius: 4px;
  padding: 0 5px;
  white-space: nowrap;
  line-height: 30px;
  border-right: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  width: 100px;
  box-sizing: border-box;
}
.slot-main {
  float: left;
  line-height: 28px;
  width: calc(100% - 65px);
  input {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }
}
.custom-form {
  display: flex;
  justify-content: center;
  align-items: center;
}
.custom-form-item {
  width: 100%;
  margin-bottom: 10px;
  &.is-required {
    .el-form-item__label {
      @include padding_left(J1);
    }
  }
  .el-input,
  .el-input__inner,
  .el-textarea__inner {
    // border-left: 0;
    margin-left: -1px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    vertical-align: top;
  }
  .el-form-item__label {
    @include padding_left(J2);
    @include padding_right(J2);
    @include font_color(T2);

    line-height: 30px;
    border: 1px solid;
    @include border_color(B1);
    @include background_color(BG2);
    border-radius: 4px 0 0 4px;
    text-align: left !important;
  }
}

.border-box {
  box-sizing: border-box;
}

.border-wrap {
  position: relative;
  box-sizing: border-box;
  padding-top: 40px;
  padding-left: 10px;
  padding-right: 10px;
  padding-bottom: 10px;
  height: 100%;
  border: 1px solid;
  @include border_color(B1);
  border-radius: 8px;
  @include background_color(BG1);
  .box-title {
    position: absolute;
    left: 10px;
    right: 0;
    top: 6px;
    padding: 0 25px;
    height: 30px;
    line-height: 30px;
    font-size: 16px;
  }
}

.title-sign::before {
  position: absolute;
  left: 10px;
  top: 10px;
  content: "";
  width: 10px;
  height: 10px;
  border-radius: 50%;
  @include background_color(BG4);
}

.require::before {
  content: "*";
  @include font_color(Sta3);
  margin-right: 4px;
}
.outset-scroll {
  margin-right: -20px !important;
  padding-right: 10px !important;
}

.menu-tab {
  .el-tabs__header {
    height: 32px;
    &.is-top {
      @include margin_bottom(J1, !important);
    }
  }
  & > .el-tabs__header .el-tabs__item {
    height: 32px !important;
    line-height: 32px !important;
    padding: 0 20px !important;
  }
  .el-tabs__nav-scroll {
    @include background_color(BG1);
  }
  .el-tabs__content {
    padding: 0;
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }
}

.card-data {
  display: flex;
  align-items: center;
  & > * {
    flex: 1;
  }
}

.cet-content-aside-sidebar {
  padding: 0 10px 0 0 !important;
}
.cet-content-aside-container {
  padding: 8px 0 0 10px !important;
}

// 主题色-文字
.color-theme {
  @include font_color(ZS);
}

// 主要文字色 标题和大数值展示的颜色
.color-primary {
  @include font_color(T1);
}
// 次要文字色#74788D  （辅助性的文字，例如数值指标，状态等）
.color-secondary {
  @include font_color(T2);
}
.color-regular {
  @include font_color(T3);
}

// 禁用文字颜色
.color-disabled {
  @include font_color(Sta6);
}
.color-unknow {
  @include font_color(T4);
}

.color-status-1 {
  @include font_color(Sta1);
}
.color-status-2 {
  @include font_color(Sta2);
}
.color-status-3 {
  @include font_color(Sta3);
}

.bg-status-1 {
  @include background_color(Sta1);
  @include border_color(Sta1);
  @include font_color(T5);
}
.bg-status-2 {
  @include background_color(Sta2);
  @include border_color(Sta2);
  @include font_color(T5);
}
.bg-status-3 {
  @include background_color(Sta3);
  @include border_color(Sta3);
  @include font_color(T5);
}

.bg-base {
  @include background_color(BG);
}

.bg-card {
  @include background_color(BG1);
}

.bg-data {
  @include background_color(BG2);
}
// 描边色--基础
.border-color-base {
  border: 1px solid;
  @include border_color(B1);
}
// 二级边框
.border-color-light {
  border: 1px solid;
  @include border_color(B2);
}
// 三级边框
.border-color-lighter {
  border: 1px solid;
  @include border_color(B2);
}
// 四级边框
.border-color-extra-light {
  border: 1px solid;
  @include border_color(B2);
}
.border-color-overview {
  border: 1px solid;
  @include border_color(B2);
}
.fs-title {
  font-size: 16px;
}
.fs-data {
  font-size: 18px;
}

.fs-content {
  font-size: 14px;
}
.fs-regular {
  font-size: 14px;
}
.fs-remark {
  font-size: 12px;
}
.fs-highlight {
  font-size: 28px;
}
.unconfirm {
  color: red;
  cursor: pointer;
}

.ztree li span.button.online_ico_open,
.ztree li span.button.online_ico_close,
.ztree li span.button.online_ico_docu {
  display: inline-block !important;
  width: 12px !important;
  height: 12px !important;
  padding: 1px !important;
  line-height: 10px;
  border-radius: 50%;
  background-color: #29b061;
}

.ztree li span.button.alarm_ico_open,
.ztree li span.button.alarm_ico_close,
.ztree li span.button.alarm_ico_docu {
  display: inline-block !important;
  width: 12px !important;
  height: 12px !important;
  padding: 1px !important;
  line-height: 10px;
  border-radius: 50%;
  background-color: #fcb92c;
}

.ztree li span.button.partAlarm_ico_open,
.ztree li span.button.partAlarm_ico_close,
.ztree li span.button.partAlarm_ico_docu {
  display: inline-block !important;
  width: 12px !important;
  height: 12px !important;
  padding: 1px !important;
  line-height: 10px;
  border-radius: 50%;
  background: linear-gradient(
    to right,
    #fcb92c 0%,
    #fcb92c 50%,
    #a9a9a9 50%,
    #a9a9a9 100%
  );
}

.ztree li span.button.partOnline_ico_open,
.ztree li span.button.partOnline_ico_close,
.ztree li span.button.partOnline_ico_docu {
  display: inline-block !important;
  width: 12px !important;
  height: 12px !important;
  padding: 1px !important;
  line-height: 10px;
  border-radius: 50%;
  background: linear-gradient(
    to right,
    #29b061 0%,
    #29b061 50%,
    #a9a9a9 50%,
    #a9a9a9 100%
  );
}

.ztree li span.button.offline_ico_open,
.ztree li span.button.offline_ico_close,
.ztree li span.button.offline_ico_docu {
  display: inline-block !important;
  width: 12px !important;
  height: 12px !important;
  padding: 1px !important;
  line-height: 10px;
  border-radius: 50%;
  background-color: #a9a9a9;
}
.device-Button {
  margin-left: 4px;
}

.middle {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.vxe-table--header-wrapper {
  @include background-color(BG1, !important);
  @include font_color(T3, !important);
  @include border_color(B1, !important);
}
.vxe-table--body-wrapper {
  @include background-color(BG1, !important);
  @include font_color(T3, !important);
}
.vxe-body--row {
  &.row--hover {
    @include background-color(BG1, !important);
  }
}

.vxe-table--render-default {
  .vxe-table--body-wrapper table {
    @include background_color(BG1, !important);
    @include font_color(T3, !important);
  }
  .vxe-table--footer-wrapper {
    @include border_color(B1, !important);
  }
}

.vxe-table {
  .vxe-table--header-wrapper {
    .vxe-table--header-border-line {
      @include border_color(B1, !important);
    }
  }
  &.border--inner {
    .vxe-header--column,
    .vxe-body--column,
    .vxe-footer--column {
      background-image: linear-gradient(var(--B1), var(--B1));
    }
  }
  &.border--full {
    .vxe-header--column,
    .vxe-body--column,
    .vxe-footer--column {
      background-image: linear-gradient(var(--B1), var(--B1)),
        linear-gradient(var(--B1), var(--B1)) !important;
    }
    .vxe-table--fixed-left-wrapper {
      .vxe-body--column {
        @include border_color(B1, !important);
      }
    }
  }
  &.border--default,
  &.border--full,
  &.border--outer,
  &.border--inner {
    .vxe-table--header-wrapper {
      .vxe-header--row {
        &:last-child {
          .vxe-header--gutter {
            background-image: linear-gradient(var(--B1), var(--B1));
          }
        }
      }
    }
  }

  /*边框线*/
  .vxe-table--border-line {
    @include border_color(B1, !important);
  }
  .vxe-body--expanded-column {
    @include border_color(B1, !important);
  }
}

/* 计划进度 */
// 未开始
.el-tag--dark.el-tag--notstart {
  background-color: #aab5ba;
  border-color: #aab5ba;
  color: #fff;
}
// 进行中
.el-tag--dark.el-tag--progressing {
  background: #00a2ff;
  border-color: #00a2ff;
  color: #fff;
}
// 已完成
.el-tag--dark.el-tag--completed {
  background: #29b061;
  border-color: #29b061;
  color: #fff;
}
// 超期完成
.el-tag--dark.el-tag--overcompleted {
  background: #ffc168;
  border-color: #ffc168;
  color: #fff;
}
// 已拒绝
.el-tag--dark.el-tag--rejected {
  background: #ff3f3f;
  border-color: #ff3f3f;
  color: #fff;
}

/* 工单状态 */
// 待接单
.el-tag--agent {
  @include border_color(Sta2);
  @include font_color(Sta2);
}
// 已接单
.el-tag--accept {
  @include border_color(Sta1);
  @include font_color(Sta1);
}
// 已处理
.el-tag--proccessed {
  @include border_color(Sta4);
  @include font_color(Sta4);
}
// 已拒绝
.el-tag--rejected {
  @include border_color(Sta3);
  @include font_color(Sta3);
}
// 已关闭
.el-tag--closed {
  @include border_color(Sta5);
  @include font_color(Sta5);
}

// 低代码大屏的z-index 层级设置
.iframe-fullscreen {
  z-index: 200 !important;
}

.selectIndex {
  z-index: 500 !important;
}

.adjustHeight {
  .el-cascader-menu__wrap {
    height: auto;
  }
}
