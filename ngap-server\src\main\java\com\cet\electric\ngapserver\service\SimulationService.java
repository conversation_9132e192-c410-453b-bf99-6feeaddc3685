package com.cet.electric.ngapserver.service;

import com.cet.electric.ngapserver.entity.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface SimulationService {
    /**
     * 分页查询仿真任务列表
     *
     * @param page      页码
     * @param size      每页大小
     * @param sortBy    排序字段
     * @param sortOrder 排序方向
     * @param projectId 项目ID（必填）
     * @param keyword   搜索关键词(可选)
     * @param startTime 起始时间(时间戳-毫秒)
     * @param endTime   结束时间(时间戳-毫秒)
     * @return 分页结果
     */
    List<Simulation> getSimulations(Integer page, Integer size, String sortBy, String sortOrder, Long projectId, String keyword, Long startTime, Long endTime);

    /**
     * 查询仿真任务总数
     *
     * @param projectId 项目ID（必填）
     * @param keyword   搜索关键词(可选)
     * @param startTime   开始时间(可选)
     * @param endTime     结束时间(可选)
     * @return 总数
     */
    Long countSimulations(Long projectId, String keyword, Long startTime, Long endTime);

    /**
     * 创建仿真任务
     *
     * @param simulation 仿真任务实体
     * @return 创建后的仿真任务
     */
    Simulation createSimulation(Simulation simulation);

    /**
     * 重命名仿真任务
     *
     * @param simulationId 仿真任务ID
     * @param newName 新的仿真名称
     * @return 更新后的仿真任务
     */
    Simulation renameSimulation(Long simulationId, String newName);

    /**
     * 更新仿真任务
     *
     * @param simulationId     仿真任务ID
     * @param simulationModel  仿真模型
     * @param simulationDraft  仿真模型草稿
     * @param simulationScript 仿真脚本
     * @param nodes            并网点
     * @param controlStrategy  控制策略参数
     * @return 更新后的仿真任务
     */
    Simulation updateSimulation(Long simulationId, String simulationModel, String simulationDraft, String simulationScript, String nodes, String controlStrategy);

    /**
     * 删除仿真任务
     *
     * @param simulationId 仿真任务ID
     */
    void deleteSimulation(Long simulationId);

    /**
     * 获取仿真任务详情
     *
     * @param simulationId 仿真任务ID
     * @return 仿真任务详情
     */
    Simulation getSimulationById(Long simulationId);

    /**
     * 复制仿真任务
     *
     * @param simulationId 原仿真任务ID
     * @return 复制后的新仿真任务
     */
    Simulation copySimulation(Long simulationId);

    /**
     * 上传文件到仿真任务
     *
     * @param simulationId 仿真任务ID
     * @param file 上传的文件
     * @param fileType 文件类型
     * @return 文件保存路径
     */
    String uploadFile(Long simulationId, MultipartFile file, String fileType);

    /**
     * 运行仿真任务
     *
     * @param simulationId 仿真任务ID
     */
    void runSimulation(Long simulationId);

    /**
     * 获取仿真策略列表
     *
     * @return 仿真策略列表，key为英文，value为中文
     */
    Map<String, String>  getSimulationStrategies();

    /**
     * 导出仿真配置文件
     *
     * @param simulationId 仿真任务ID
     * @param response  HTTP响应对象，用于写入文件流
     */
    void exportSimulation(Long simulationId, HttpServletResponse response);

    /**
     * 导入仿真配置文件
     *
     * @param projectId 项目ID
     * @param file 上传的仿真配置文件
     * @return 导入成功的仿真任务信息
     */
    Simulation importSimulation(Long projectId, MultipartFile file);

    /**
     * 获取仿真任务输出路径
     *
     * @param simulationId 仿真任务ID
     * @return 仿真任务的输出路径
     */
    String getSimulationOutputPath(Long simulationId);

    /**
     * 获取仿真任务指标列表
     *
     * @param simulationId 仿真任务ID
     * @return 指标列表
     */
    List<Metric> getSimulationMetrics(Long simulationId);

    /**
     * 获取特定仿真任务的指标数据
     *
     * @param simulationId   仿真任务ID，用于确定数据所在的目录
     * @param metricId       指标ID，格式为"文件名.列名"
     * @param startTimestamp 起始时间戳
     * @return 包含指标数据的{@link MetricData}对象，包括数据点列表和统计信息
     */
    List<MetricData> getSimulationMetricData(Long simulationId, String metricId, Long startTimestamp);

    /**
     * 批量获取特定仿真任务的指标数据
     *
     * @param simulationId   仿真任务ID，用于确定数据所在的目录
     * @param metricIds      指标ID列表，格式为"文件名.列名"
     * @param startTimestamp 仿真指标起始时间戳
     * @return 包含指标数据的{@link MetricData}对象列表，包括数据点列表和统计信息
     */
    List<MetricData> getMultipleSimulationMetricData(Long simulationId, List<String> metricIds, Long startTimestamp);

    /**
     *导出特定仿真任务的指标数据到Excel文件
     *
     *@param simulationId 仿真任务ID，用于确定数据所在的目录
     *@param pic 图表Base64字符串
     *@param metricIds 指标ID列表，格式为"文件名.列名"
     *@param startTimestamp 仿真指标起始时间戳
     *@param response HTTP响应对象，用于写入Excel文件流
     */
    void exportMultipleSimulationMetricData(Long simulationId, String pic, List<String> metricIds, Long startTimestamp, HttpServletResponse response);

    /**
     * 获取台区电压质量统计报表
     *
     * @param simulationId 仿真任务ID，用于确定数据所在的目录
     * @return 包含电压统计数据的{@link CourtsStatistics}对象，包括合格率、越限率等统计信息
     */
    CourtsStatistics getCourtsVoltageReport(Long simulationId);

    /**
     * 获取用户电压合格率统计报表（支持分页和关键字查询）
     *
     * @param simulationId 仿真任务ID，用于确定数据所在的目录
     * @param keyword 关键字，用于筛选用户名称
     * @param pageNum 当前页码（从1开始）
     * @param pageSize 每页显示条数
     * @return 包含用户电压统计数据的{@link List<UserStatistics>}对象，包括合格率、越限率等统计信息
     */
    List<UserStatistics> getUserVoltageReport(Long simulationId, String keyword,
                                                     Integer pageNum, Integer pageSize);

    /**
     * 获取用户电压合格率统计报表中符合条件的记录总数
     *
     * @param simulationId 仿真任务ID，用于确定数据所在的目录
     * @param keyword 关键字，用于筛选用户名称
     * @return 符合条件的记录总数
     */
    Long countUserVoltageReport(Long simulationId, String keyword);

    /**
     * 计算仿真数据的电压合格率统计
     *
     * @param simulationId 仿真任务ID
     * @return 电压合格率统计结果，包含总数据点数、合格数据点数、合格率百分比等信息
     */
    CourtsStatistics calculateSimulationVoltageQualityRate(Long simulationId);
}

