{"name": "omega-tpl", "version": "1.1.17", "private": true, "workspaces": ["packages/*"], "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "build:report": "vue-cli-service build --report", "update:omega": "npm update --save @omega/app @omega/auth @omega/layout @omega/http @omega/i18n @omega/theme @omega/widget @omega/icon @omega/cli-devserver cet-chart cet-common"}, "devDependencies": {"@babel/core": "7.11.0", "@babel/eslint-parser": "^7.16.0", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-service": "~5.0.8", "compression-webpack-plugin": "^10.0.0", "core-js": "^3.6.5", "css-loader": "3.6.0", "eslint": "^8.11.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-vue": "^9.3.0", "postcss-import": "^14.1.0", "prettier": "^2.4.1", "sass": "^1.54.0", "sass-loader": "^13.0.2", "svg-sprite-loader": "^6.0.11", "tailwindcss": "^3.1.6", "vue-demi": "^0.13.6", "vue-eslint-parser": "^8.0.1", "vue-loader": "15.10.0"}, "dependencies": {"@omega/admin": "^1.13.9", "@omega/app": "^1.4.0", "@omega/auth": "^1.13.2", "@omega/cli-codestd": "^0.0.2", "@omega/cli-devops": "^1.4.5", "@omega/cli-devserver": "^1.1.1", "@omega/dashboard": "^1.2.14", "@omega/http": "^1.7.0", "@omega/i18n": "^1.4.0", "@omega/icon": "^1.12.1", "@omega/layout": "^1.12.0", "@omega/theme": "^1.8.1", "@omega/tracking": "^1.0.1", "@omega/trend": "^1.2.13", "@omega/widget": "^1.0.2", "cet-chart": "^1.3.0", "cet-common": "^1.6.5", "cet-waveform": "^1.1.5", "echarts-liquidfill": "^3.1.0", "element-ui": "^2.15.6", "jquery": "^1.12.4", "lodash": "^4.17.21", "moment": "^2.29.1", "nprogress": "^0.2.0", "sm-crypto": "^0.3.13", "vue": "^2.7.8", "vue-router": "^3.5.3", "vuex": "^3.5.1", "vxe-table": "^3.6.13", "xe-utils": "^3.5.11"}}