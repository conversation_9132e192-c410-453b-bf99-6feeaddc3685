"use strict";

function isFunction(value) {
  return typeof value === "function";
}

/**
 * 任务类
 * @param {Function} mission 需要执行的任务
 * @param {Numebr} interval 时间间隔，毫秒
 */
function Task(mission, interval = 15000) {
  this.mission = mission;
  this.interval = interval;
  this._stopped = true;
  this._immediate = false;
  this._idle = true;
  this._timer = null;
  this._callbacks = [];
}

/**
 * 设置新的时间间隔
 * @param {Number} 设置时间间隔
 */
Task.prototype.setInterval = function (interval) {
  this.interval = interval;
  return this;
};

/**
 * 停止任务
 */
Task.prototype.stop = function () {
  this._stopped = true;
  this._immediate = false;
  this._timer && clearTimeout(this._timer);
  this._timer = null;
  return this;
};

/**
 * 任务是否已经停止
 */
Task.prototype.isStopped = function () {
  return this._stopped;
};

/**
 * 开始任务
 * @param {Boolean} immediate 是否在任务闲时立即执行方法
 */
Task.prototype.start = function (immediate = false) {
  this._immediate = !!immediate;

  // 刚启动时需要立即执行一次
  if (this._stopped) {
    this._stopped = false;
    this.__nextTick();
    return this;
  }
  // 如果需要马上执行，且在空闲状态
  else if (this._immediate && this._idle) {
    this.__nextTick();
  }

  return this;
};

/**
 * 开始任务
 * @param {Function} cb 回调函数
 */
Task.prototype.$nextTick = function (cb) {
  if (!isFunction(cb)) {
    return;
  }

  this._callbacks.push(() => {
    try {
      cb();
    } catch (e) {
      console.error(e);
    }
  });
};

/**
 * @private
 * 执行所有回调函数
 */
Task.prototype.__flushcallbacks = function () {
  // 要优先清除掉，防止可能的重复调用
  // 比如在回调函数中再次调用$nextTick函数
  const copies = this._callbacks.slice(0);
  this._callbacks.length = 0;

  for (let i = 0; i < copies.length; i++) {
    copies[i]();
  }
};

/**
 * @private
 * 关键时间点执行任务
 */
Task.prototype.__onTick = function () {
  const me = this;

  // 执行任务
  const startTime = new Date().getTime();
  const p = new Promise((resolve, reject) => {
    me._idle = false;
    me.mission(resolve, reject);
  });

  // 任务执行完成后
  p.finally(() => {
    me.__flushcallbacks();

    // 状态设为空闲
    me._idle = true;

    const endTime = new Date().getTime();
    const nextLeftTime = startTime + me.interval - endTime;

    // 若需要马上执行或没有剩余时间，则立即执行
    if (me._immediate || nextLeftTime <= 0) {
      me.__nextTick();
      return;
    }

    // 若还有剩余时间，则在剩余时间后执行
    me._timer = setTimeout(() => {
      me.__nextTick();
    }, nextLeftTime);
  });
};

/**
 * @private
 * 执行下一次任务
 */
Task.prototype.__nextTick = function () {
  // 如果已经停止了就不需要再执行
  if (this._stopped) {
    return;
  }

  this._immediate = false;
  this._timer && clearTimeout(this._timer);
  this._timer = null;
  this.__onTick();
};

export default Task;
