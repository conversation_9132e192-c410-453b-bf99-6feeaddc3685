package com.cet.electric.ngapserver.entity;

import com.cet.electric.ngapserver.enums.RunStatus;
import org.junit.Test;

import static org.junit.Assert.*;

public class SimulationTest {

    @Test
    public void testSimulationBuilder() {
        // 准备测试数据
        Long simulationId = 1L;
        Long projectId = 100L;
        String projectName = "测试项目";
        String simulationName = "测试仿真";
        String simulationModel = "测试模型";
        String simulationDraft = "测试草稿";
        String inputData = "输入数据";
        String outputData = "输出数据";
        String measuredData = "实测数据";
        String simulationScript = "仿真脚本";
        String nodes = "节点信息";
        String controlStrategy = "控制策略";
        RunStatus runStatus = RunStatus.READY;
        Long createdAt = System.currentTimeMillis();
        Long updatedAt = System.currentTimeMillis();
        Integer isDeleted = 0;

        // 使用Builder创建Simulation对象
        Simulation simulation = Simulation.builder()
                .simulationId(simulationId)
                .projectId(projectId)
                .projectName(projectName)
                .simulationName(simulationName)
                .simulationModel(simulationModel)
                .simulationDraft(simulationDraft)
                .inputData(inputData)
                .outputData(outputData)
                .measuredData(measuredData)
                .simulationScript(simulationScript)
                .nodes(nodes)
                .controlStrategy(controlStrategy)
                .runStatus(runStatus)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .isDeleted(isDeleted)
                .build();

        // 验证结果
        assertNotNull(simulation);
        assertEquals(simulationId, simulation.getSimulationId());
        assertEquals(projectId, simulation.getProjectId());
        assertEquals(projectName, simulation.getProjectName());
        assertEquals(simulationName, simulation.getSimulationName());
        assertEquals(simulationModel, simulation.getSimulationModel());
        assertEquals(simulationDraft, simulation.getSimulationDraft());
        assertEquals(inputData, simulation.getInputData());
        assertEquals(outputData, simulation.getOutputData());
        assertEquals(measuredData, simulation.getMeasuredData());
        assertEquals(simulationScript, simulation.getSimulationScript());
        assertEquals(nodes, simulation.getNodes());
        assertEquals(controlStrategy, simulation.getControlStrategy());
        assertEquals(runStatus, simulation.getRunStatus());
        assertEquals(createdAt, simulation.getCreatedAt());
        assertEquals(updatedAt, simulation.getUpdatedAt());
        assertEquals(isDeleted, simulation.getIsDeleted());
    }

    @Test
    public void testSimulationNoArgsConstructor() {
        // 使用无参构造函数创建Simulation对象
        Simulation simulation = new Simulation();

        // 验证结果
        assertNotNull(simulation);
        assertNull(simulation.getSimulationId());
        assertNull(simulation.getProjectId());
        assertNull(simulation.getProjectName());
        assertNull(simulation.getSimulationName());
        assertNull(simulation.getSimulationModel());
        assertNull(simulation.getSimulationDraft());
        assertNull(simulation.getInputData());
        assertNull(simulation.getOutputData());
        assertNull(simulation.getMeasuredData());
        assertNull(simulation.getSimulationScript());
        assertNull(simulation.getNodes());
        assertNull(simulation.getControlStrategy());
        assertNull(simulation.getRunStatus());
        assertNull(simulation.getCreatedAt());
        assertNull(simulation.getUpdatedAt());
        assertNull(simulation.getIsDeleted());
    }

    @Test
    public void testSimulationSettersAndGetters() {
        // 创建Simulation对象
        Simulation simulation = new Simulation();

        // 准备测试数据
        Long simulationId = 1L;
        Long projectId = 100L;
        String projectName = "测试项目";
        String simulationName = "测试仿真";
        RunStatus runStatus = RunStatus.RUNNING;
        Long createdAt = System.currentTimeMillis();
        Long updatedAt = System.currentTimeMillis();
        Integer isDeleted = 0;

        // 使用Setter设置值
        simulation.setSimulationId(simulationId);
        simulation.setProjectId(projectId);
        simulation.setProjectName(projectName);
        simulation.setSimulationName(simulationName);
        simulation.setRunStatus(runStatus);
        simulation.setCreatedAt(createdAt);
        simulation.setUpdatedAt(updatedAt);
        simulation.setIsDeleted(isDeleted);

        // 使用Getter验证值
        assertEquals(simulationId, simulation.getSimulationId());
        assertEquals(projectId, simulation.getProjectId());
        assertEquals(projectName, simulation.getProjectName());
        assertEquals(simulationName, simulation.getSimulationName());
        assertEquals(runStatus, simulation.getRunStatus());
        assertEquals(createdAt, simulation.getCreatedAt());
        assertEquals(updatedAt, simulation.getUpdatedAt());
        assertEquals(isDeleted, simulation.getIsDeleted());
    }

    @Test
    public void testSimulationEqualsAndHashCode() {
        // 创建两个相同的Simulation对象
        Simulation simulation1 = Simulation.builder()
                .simulationId(1L)
                .projectId(100L)
                .simulationName("测试仿真")
                .runStatus(RunStatus.READY)
                .createdAt(1000L)
                .updatedAt(2000L)
                .isDeleted(0)
                .build();

        Simulation simulation2 = Simulation.builder()
                .simulationId(1L)
                .projectId(100L)
                .simulationName("测试仿真")
                .runStatus(RunStatus.READY)
                .createdAt(1000L)
                .updatedAt(2000L)
                .isDeleted(0)
                .build();

        // 验证equals方法
        assertEquals(simulation1, simulation2);
        assertEquals(simulation2, simulation1);
        assertEquals(simulation1, simulation1);

        // 验证hashCode方法
        assertEquals(simulation1.hashCode(), simulation2.hashCode());
    }

    @Test
    public void testSimulationNotEquals() {
        // 创建两个不同的Simulation对象
        Simulation simulation1 = Simulation.builder()
                .simulationId(1L)
                .simulationName("仿真1")
                .runStatus(RunStatus.READY)
                .build();

        Simulation simulation2 = Simulation.builder()
                .simulationId(2L)
                .simulationName("仿真2")
                .runStatus(RunStatus.RUNNING)
                .build();

        // 验证不相等
        assertNotEquals(simulation1, simulation2);
        assertNotEquals(simulation2, simulation1);
        assertNotEquals(simulation1, null);
        assertNotEquals(simulation1, "not a simulation");
    }

    @Test
    public void testSimulationToString() {
        // 创建Simulation对象
        Simulation simulation = Simulation.builder()
                .simulationId(1L)
                .simulationName("测试仿真")
                .runStatus(RunStatus.READY)
                .build();

        // 验证toString方法
        String toString = simulation.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("Simulation"));
        assertTrue(toString.contains("simulationId=1"));
        assertTrue(toString.contains("simulationName=测试仿真"));
        assertTrue(toString.contains("runStatus=READY"));
    }

    @Test
    public void testSimulationWithNullValues() {
        // 创建包含null值的Simulation对象
        Simulation simulation = Simulation.builder()
                .simulationId(null)
                .projectId(null)
                .simulationName(null)
                .runStatus(null)
                .createdAt(null)
                .updatedAt(null)
                .isDeleted(null)
                .build();

        // 验证null值处理
        assertNotNull(simulation);
        assertNull(simulation.getSimulationId());
        assertNull(simulation.getProjectId());
        assertNull(simulation.getSimulationName());
        assertNull(simulation.getRunStatus());
        assertNull(simulation.getCreatedAt());
        assertNull(simulation.getUpdatedAt());
        assertNull(simulation.getIsDeleted());
    }

    @Test
    public void testSimulationRunStatusTransitions() {
        // 测试运行状态转换
        Simulation simulation = new Simulation();
        
        // 初始状态
        simulation.setRunStatus(RunStatus.READY);
        assertEquals(RunStatus.READY, simulation.getRunStatus());
        
        // 运行中
        simulation.setRunStatus(RunStatus.RUNNING);
        assertEquals(RunStatus.RUNNING, simulation.getRunStatus());
        
        // 成功完成
        simulation.setRunStatus(RunStatus.SUCCESS);
        assertEquals(RunStatus.SUCCESS, simulation.getRunStatus());
        
        // 失败
        simulation.setRunStatus(RunStatus.FAILED);
        assertEquals(RunStatus.FAILED, simulation.getRunStatus());
    }

    @Test
    public void testSimulationTimestampFields() {
        // 获取当前时间
        long currentTime = System.currentTimeMillis();
        
        // 创建Simulation对象
        Simulation simulation = Simulation.builder()
                .simulationId(1L)
                .simulationName("时间戳测试仿真")
                .createdAt(currentTime)
                .updatedAt(currentTime + 1000)
                .build();

        // 验证时间戳字段
        assertEquals(Long.valueOf(currentTime), simulation.getCreatedAt());
        assertEquals(Long.valueOf(currentTime + 1000), simulation.getUpdatedAt());
        assertTrue(simulation.getUpdatedAt() > simulation.getCreatedAt());
    }

    @Test
    public void testSimulationDeletedStatus() {
        // 测试未删除状态
        Simulation activeSimulation = Simulation.builder()
                .simulationId(1L)
                .simulationName("活跃仿真")
                .isDeleted(0)
                .build();

        assertEquals(Integer.valueOf(0), activeSimulation.getIsDeleted());

        // 测试已删除状态
        Simulation deletedSimulation = Simulation.builder()
                .simulationId(2L)
                .simulationName("已删除仿真")
                .isDeleted(1)
                .build();

        assertEquals(Integer.valueOf(1), deletedSimulation.getIsDeleted());
    }

    @Test
    public void testSimulationDataFields() {
        // 测试数据字段
        Simulation simulation = Simulation.builder()
                .simulationId(1L)
                .inputData("输入数据内容")
                .outputData("输出数据内容")
                .measuredData("实测数据内容")
                .simulationScript("仿真脚本内容")
                .nodes("节点配置")
                .controlStrategy("控制策略配置")
                .build();

        assertEquals("输入数据内容", simulation.getInputData());
        assertEquals("输出数据内容", simulation.getOutputData());
        assertEquals("实测数据内容", simulation.getMeasuredData());
        assertEquals("仿真脚本内容", simulation.getSimulationScript());
        assertEquals("节点配置", simulation.getNodes());
        assertEquals("控制策略配置", simulation.getControlStrategy());
    }
}
