{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true}, "source": ["import requests\n", "\n", "url = \"http://localhost:5000/run-dss-with-strategy\"\n", "payload = {\n", "    \"dssFilePath\": \"D:/CETWorkSpace/ngap-server/data/21/18/simulation_script/simulationScript.dss\",\n", "    \"json_data\":'''\n", "\n", "    {\"ACTIVE_POWER_VOLTAGE_CONTROL\":{\"REACTIVE_POWER_VOLTAGE_CONTROL\":[{\"name\":\"generator_1\",\"pv_s\":30,\"v_deadzone_ratio\":0.02,\"v_upper_limit\":242,\"v_lower_limit\":198},{\"name\":\"generator_2\",\"pv_s\":30,\"v_deadzone_ratio\":0.02,\"v_upper_limit\":242,\"v_lower_limit\":198},{\"name\":\"generator_3\",\"pv_s\":30,\"v_deadzone_ratio\":0.02,\"v_upper_limit\":242,\"v_lower_limit\":198},{\"name\":\"generator_4\",\"pv_s\":30,\"v_deadzone_ratio\":0.02,\"v_upper_limit\":242,\"v_lower_limit\":198},{\"name\":\"generator_5\",\"pv_s\":30,\"v_deadzone_ratio\":0.02,\"v_upper_limit\":242,\"v_lower_limit\":198},{\"name\":\"generator_6\",\"pv_s\":30,\"v_deadzone_ratio\":0.02,\"v_upper_limit\":242,\"v_lower_limit\":198},{\"name\":\"generator_7\",\"pv_s\":30,\"v_deadzone_ratio\":0.02,\"v_upper_limit\":242,\"v_lower_limit\":198},{\"name\":\"generator_8\",\"pv_s\":30,\"v_deadzone_ratio\":0.02,\"v_upper_limit\":242,\"v_lower_limit\":198},{\"name\":\"generator_9\",\"pv_s\":30,\"v_deadzone_ratio\":0.02,\"v_upper_limit\":242,\"v_lower_limit\":198},{\"name\":\"generator_10\",\"pv_s\":30,\"v_deadzone_ratio\":0.02,\"v_upper_limit\":242,\"v_lower_limit\":198}]}\n", "\n", "    '''\n", "\n", "}\n", "headers = {\n", "    \"Content-Type\": \"application/json\"\n", "}\n", "\n", "response = requests.post(url, json=payload, headers=headers)\n", "print(response.json())"], "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "26a66c37cd0d1d97"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}