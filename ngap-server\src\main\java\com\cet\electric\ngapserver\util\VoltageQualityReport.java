package com.cet.electric.ngapserver.util;

import com.cet.electric.ngapserver.entity.CourtsStatistics;
import com.cet.electric.ngapserver.entity.UserStatistics;
import com.cet.electric.ngapserver.util.excel.ExcelCacheManager;
import com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor;
import com.cet.electric.ngapserver.util.voltage.VoltageObjectPool;
import com.cet.electric.ngapserver.util.voltage.VoltageStatisticsCalculator;
import com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.*;

/**
 * <AUTHOR>
 */
public class VoltageQualityReport {
    private static final Logger log = LoggerFactory.getLogger(VoltageQualityReport.class);

    // 电压合格范围
    private static final double MIN_VOLTAGE = 198.0;
    private static final double MAX_VOLTAGE = 242.0;

    // 高性能数据处理器
    private static final VoltageDataProcessor dataProcessor = new VoltageDataProcessor();

    // 缓存管理器
    private static final ExcelCacheManager cacheManager = ExcelCacheManager.getInstance();

    public static void main(String[] args) {
        try {
            // 定义输入文件和输出文件路径
            String inputFilePath = "C:\\Users\\<USER>\\Desktop\\Cet\\工作记录\\2025.06\\2025-06-16~2025-06-20\\工作簿1.xlsx";
            // 调用方法生成电压合格率报表
            generateVoltageReport(inputFilePath);
        } catch (Exception e) {
            log.error("生成电压合格率报表时发生异常", e);
        }
    }

    /**
     * 生成电压合格率报表（优化版本）
     *
     * @param inputFilePath  输入文件路径
     */
    public static void generateVoltageReport(String inputFilePath)  {
        VoltagePerformanceMonitor monitor = VoltagePerformanceMonitor.getInstance();

        try (VoltagePerformanceMonitor.PerformanceTimer timer = monitor.startOperation("generateVoltageReport")) {
            String outputFilePath = new File(new File(inputFilePath).getParent(), "电压合格率报表.xlsx").getPath();

            // 检查缓存中是否有完整的报表数据
            CourtsStatistics cachedOverallStats = ExcelCacheManager.CacheOperations.getOverallStats(inputFilePath);
            List<UserStatistics> cachedUserStats = ExcelCacheManager.CacheOperations.getUserStats(inputFilePath);

            if (cachedOverallStats != null && cachedUserStats != null) {
                log.debug("使用缓存的统计数据生成报表: {}", inputFilePath);
                generateReport(outputFilePath, cachedUserStats, cachedOverallStats);
                return;
            }

            // 使用优化的数据处理器读取数据
            List<VoltageDataProcessor.VoltageRecord> records =
                VoltagePerformanceMonitor.timeOperation("readVoltageData",
                    () -> dataProcessor.readVoltageDataOptimized(inputFilePath));

            // 按用户名称分组
            Map<String, List<VoltageDataProcessor.VoltageRecord>> userRecords =
                VoltagePerformanceMonitor.timeOperation("groupByUser",
                    () -> groupByUserOptimized(records));

            // 使用优化的统计计算
            List<UserStatistics> userStatsList =
                VoltagePerformanceMonitor.timeOperation("calculateUserStats",
                    () -> dataProcessor.calculateUserStatsOptimized(userRecords));

            // 计算总体统计数据
            CourtsStatistics overallStats =
                VoltagePerformanceMonitor.timeOperation("calculateOverallStats",
                    () -> calculateOverallStatsOptimized(userStatsList));

            // 缓存计算结果
            ExcelCacheManager.CacheOperations.cacheUserStats(inputFilePath, userStatsList);
            ExcelCacheManager.CacheOperations.cacheOverallStats(inputFilePath, overallStats);

            // 生成报表
            VoltagePerformanceMonitor.timeOperation("generateReport",
                () -> generateReportOptimized(outputFilePath, userStatsList, overallStats));

            // 记录处理完成
            int totalRecords = records.size();
            monitor.recordFileProcessed(inputFilePath, timer.getDuration(), totalRecords);

            log.info("电压合格率报表生成完成: {} -> {}, 处理记录数: {}, 耗时: {}ms",
                    inputFilePath, outputFilePath, totalRecords, timer.getDuration());
        }
    }

    /**
     * 优化的用户分组方法（增强空值处理）
     */
    private static Map<String, List<VoltageDataProcessor.VoltageRecord>> groupByUserOptimized(
            List<VoltageDataProcessor.VoltageRecord> records) {
        Map<String, List<VoltageDataProcessor.VoltageRecord>> userRecords = new HashMap<>();

        if (records == null || records.isEmpty()) {
            return userRecords;
        }

        for (VoltageDataProcessor.VoltageRecord record : records) {
            // 增强空值检查
            if (record != null && record.userName != null && !record.userName.trim().isEmpty()) {
                userRecords.computeIfAbsent(record.userName, k -> new ArrayList<>()).add(record);
            }
        }

        return userRecords;
    }

    /**
     * 优化的总体统计计算
     */
    private static CourtsStatistics calculateOverallStatsOptimized(List<UserStatistics> userStatsList) {
        if (userStatsList.isEmpty()) {
            return CourtsStatistics.builder()
                    .totalUsers(0)
                    .qualifiedRate(0.0)
                    .aboveMaxRate(0.0)
                    .belowMinRate(0.0)
                    .maxVoltage(0.0)
                    .minVoltage(0.0)
                    .totalReadings(0)
                    .qualifiedReadings(0)
                    .aboveMaxReadings(0)
                    .belowMinReadings(0)
                    .build();
        }

        VoltageObjectPool objectPool = VoltageObjectPool.getInstance();
        VoltageStatisticsCalculator calculator = objectPool.borrowCalculator();

        try {
            // 合并所有用户的统计数据
            for (UserStatistics stats : userStatsList) {
                // 添加空值检查
                if (stats == null) {
                    continue;
                }

                VoltageStatisticsCalculator userCalculator = objectPool.borrowCalculator();
                try {
                    // 重建用户计算器状态 - 增强空值检查
                    Integer qualifiedReadings = stats.getQualifiedReadings();
                    Integer totalReadings = stats.getTotalReadings();
                    Integer aboveMaxReadings = stats.getAboveMaxReadings();
                    Integer belowMinReadings = stats.getBelowMinReadings();
                    Double maxVoltage = stats.getMaxVoltage();
                    Double minVoltage = stats.getMinVoltage();

                    // 安全地重建统计数据
                    if (totalReadings != null && totalReadings > 0) {
                        // 使用平均电压值来重建数据
                        double avgVoltage = 220.0; // 默认电压值
                        if (maxVoltage != null && minVoltage != null &&
                            !maxVoltage.equals(Double.MIN_VALUE) && !minVoltage.equals(Double.MAX_VALUE)) {
                            avgVoltage = (maxVoltage + minVoltage) / 2;
                        }

                        // 重建各类电压数据
                        if (qualifiedReadings != null && qualifiedReadings > 0) {
                            for (int i = 0; i < qualifiedReadings; i++) {
                                userCalculator.addVoltageValue(avgVoltage);
                            }
                        }
                        if (aboveMaxReadings != null && aboveMaxReadings > 0) {
                            for (int i = 0; i < aboveMaxReadings; i++) {
                                userCalculator.addVoltageValue(250.0); // 超上限电压
                            }
                        }
                        if (belowMinReadings != null && belowMinReadings > 0) {
                            for (int i = 0; i < belowMinReadings; i++) {
                                userCalculator.addVoltageValue(190.0); // 低于下限电压
                            }
                        }
                    }
                    calculator.merge(userCalculator);
                } finally {
                    objectPool.returnCalculator(userCalculator);
                }
            }

            // 计算汇总数据 - 增强空值处理
            int totalUsers = userStatsList.size();
            int totalReadings = userStatsList.stream()
                .filter(stats -> stats != null && stats.getTotalReadings() != null)
                .mapToInt(UserStatistics::getTotalReadings)
                .sum();
            int totalQualifiedReadings = userStatsList.stream()
                .filter(stats -> stats != null && stats.getQualifiedReadings() != null)
                .mapToInt(UserStatistics::getQualifiedReadings)
                .sum();
            int totalAboveMaxReadings = userStatsList.stream()
                .filter(stats -> stats != null && stats.getAboveMaxReadings() != null)
                .mapToInt(UserStatistics::getAboveMaxReadings)
                .sum();
            int totalBelowMinReadings = userStatsList.stream()
                .filter(stats -> stats != null && stats.getBelowMinReadings() != null)
                .mapToInt(UserStatistics::getBelowMinReadings)
                .sum();

            double overallMaxVoltage = userStatsList.stream()
                .filter(stats -> stats != null && stats.getMaxVoltage() != null &&
                        !stats.getMaxVoltage().equals(Double.MIN_VALUE))
                .mapToDouble(UserStatistics::getMaxVoltage)
                .max().orElse(0.0);
            double overallMinVoltage = userStatsList.stream()
                .filter(stats -> stats != null && stats.getMinVoltage() != null &&
                        !stats.getMinVoltage().equals(Double.MAX_VALUE))
                .mapToDouble(UserStatistics::getMinVoltage)
                .min().orElse(0.0);

            double overallQualifiedRate = totalReadings > 0 ? (double) totalQualifiedReadings / totalReadings * 100 : 0;
            double overallAboveMaxRate = totalReadings > 0 ? (double) totalAboveMaxReadings / totalReadings * 100 : 0;
            double overallBelowMinRate = totalReadings > 0 ? (double) totalBelowMinReadings / totalReadings * 100 : 0;

            return CourtsStatistics.builder()
                    .totalUsers(totalUsers)
                    .qualifiedRate(overallQualifiedRate)
                    .aboveMaxRate(overallAboveMaxRate)
                    .belowMinRate(overallBelowMinRate)
                    .maxVoltage(overallMaxVoltage)
                    .minVoltage(overallMinVoltage)
                    .totalReadings(totalReadings)
                    .qualifiedReadings(totalQualifiedReadings)
                    .aboveMaxReadings(totalAboveMaxReadings)
                    .belowMinReadings(totalBelowMinReadings)
                    .build();

        } finally {
            objectPool.returnCalculator(calculator);
        }
    }

    /**
     * 优化的报表生成方法
     */
    private static void generateReportOptimized(String filePath, List<UserStatistics> userStatsList,
                                              CourtsStatistics overallStats) {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("电压合格率报表");

            // 每次都创建新的样式对象，因为CellStyle不能跨工作簿使用
            ReportStyles styles = createReportStyles(workbook);

            // 批量创建报表内容
            createReportContent(sheet, userStatsList, overallStats, styles);

            // 批量调整列宽
            optimizeColumnWidths(sheet);

            // 写入文件
            try (FileOutputStream fileOut = new FileOutputStream(filePath)) {
                workbook.write(fileOut);
                log.debug("电压合格率报表生成完成: {}", filePath);
            }

        } catch (IOException e) {
            log.error("生成优化报表时发生异常: {}", filePath, e);
        }
    }

    private static List<VoltageRecord> readVoltageData(String filePath) {
        List<VoltageRecord> records = new ArrayList<>();

        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = WorkbookFactory.create(fis)) {

            Sheet sheet = workbook.getSheet("用户电压");
            if (sheet == null) {
                log.warn("Excel文件中未找到'用户电压'工作表，返回空记录列表");
                return records;
            }
            int rowCount = sheet.getPhysicalNumberOfRows();

            // 假设第一行是标题行
            Row headerRow = sheet.getRow(0);

            int userNameIndex = getColumnIndex(headerRow, "用户名称");
            int uIndex = getColumnIndex(headerRow, "u1");

            for (int i = 1; i < rowCount; i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                // 获取每个字段的值
                String userName = getCellValueAsString(row.getCell(userNameIndex));


                // 如果关键字段为空，则跳过这一行
                if (userName == null) {
                    continue; // 跳过空白行
                }

                // 读取u1到u96的所有电压值
                List<Double> voltageValues = new ArrayList<>();
                boolean hasValidVoltage = false;

                for (int j = uIndex; j < uIndex + 96 && j < row.getLastCellNum(); j++) {
                    Cell cell = row.getCell(j);

                    if (cell != null) {
                        // 检查单元格的类型，确保是数字类型
                        if (cell.getCellType() == CellType.NUMERIC) {
                            try {
                                voltageValues.add(cell.getNumericCellValue());
                                hasValidVoltage = true; // 有有效的电压值
                            } catch (Exception e) {
                                log.debug("提取数值型电压值时发生异常: 行={}, 列={}, 异常信息={}",
                                    cell.getRowIndex(), cell.getColumnIndex(), e.getMessage());
                                voltageValues.add(null);
                            }
                        } else if (cell.getCellType() == CellType.STRING) {
                            // 如果是字符串类型，可以尝试将其转为数字
                            try {
                                voltageValues.add(Double.parseDouble(cell.getStringCellValue()));
                                hasValidVoltage = true;
                            } catch (NumberFormatException e) {
                                // 处理无法转换为数字的情况
                                voltageValues.add(null); // 或者采取其他适当的错误处理方式
                            }
                        } else {
                            voltageValues.add(null); // 处理其他类型（比如公式、布尔值等）
                        }
                    } else {
                        voltageValues.add(null); // 处理空单元格
                    }
                }

                // 如果电压值都为空，则跳过这一行
                if (!hasValidVoltage) {
                    continue; // 跳过没有有效电压值的行
                }

                records.add(new VoltageRecord(userName, voltageValues));
            }
        } catch (IOException e) {
            log.error("读取实测文件过程中发生异常",  e);
        }

        return records;
    }

    private static int getColumnIndex(Row headerRow, String columnName) {
        for (int i = 0; i < headerRow.getPhysicalNumberOfCells(); i++) {
            Cell cell = headerRow.getCell(i);
            if (cell != null && cell.getCellType() == CellType.STRING && cell.getStringCellValue().equals(columnName)) {
                return i;
            }
        }
        throw new IllegalArgumentException("Column with name '" + columnName + "' not found.");
    }

    private static String getCellValueAsString(Cell cell) {
        if (cell == null) return "";

        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue();
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return cell.getLocalDateTimeCellValue().toString();
                    } else {
                        return String.valueOf(cell.getNumericCellValue());
                    }
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    return cell.getCellFormula();
                default:
                    return "";
            }
        } catch (Exception e) {
            log.debug("获取单元格值时发生异常: 行={}, 列={}, 类型={}, 异常信息={}",
                cell.getRowIndex(), cell.getColumnIndex(), cell.getCellType(), e.getMessage());
            return "";
        }
    }

    private static Map<String, List<VoltageRecord>> groupByUser(List<VoltageRecord> records) {
        Map<String, List<VoltageRecord>> userRecords = new HashMap<>();

        for (VoltageRecord record : records) {
            userRecords.computeIfAbsent(record.userName, k -> new ArrayList<>()).add(record);
        }

        return userRecords;
    }

    private static List<UserStatistics> calculateUserStats(Map<String, List<VoltageRecord>> userRecords) {
        List<UserStatistics> userStatsList = new ArrayList<>();

        for (Map.Entry<String, List<VoltageRecord>> entry : userRecords.entrySet()) {
            String userName = entry.getKey();
            List<VoltageRecord> records = entry.getValue();

            int totalReadings = 0;
            int qualifiedReadings = 0;
            int aboveMaxReadings = 0;
            int belowMinReadings = 0;
            double maxVoltage = Double.MIN_VALUE;
            double minVoltage = Double.MAX_VALUE;

            for (VoltageRecord record : records) {
                for (Double voltage : record.voltageValues) {
                    if (voltage == null) continue;

                    totalReadings++;

                    if (voltage >= MIN_VOLTAGE && voltage <= MAX_VOLTAGE) {
                        qualifiedReadings++;
                    } else if (voltage > MAX_VOLTAGE) {
                        aboveMaxReadings++;
                    } else if (voltage < MIN_VOLTAGE) {
                        belowMinReadings++;
                    }

                    if (voltage > maxVoltage) maxVoltage = voltage;
                    if (voltage < minVoltage) minVoltage = voltage;
                }
            }

            double qualifiedRate = totalReadings > 0 ? (double) qualifiedReadings / totalReadings * 100 : 0;
            double aboveMaxRate = totalReadings > 0 ? (double) aboveMaxReadings / totalReadings * 100 : 0;
            double belowMinRate = totalReadings > 0 ? (double) belowMinReadings / totalReadings * 100 : 0;

            userStatsList.add(UserStatistics.builder()
                    .userName(userName)
                    .qualifiedRate(qualifiedRate)
                    .aboveMaxRate(aboveMaxRate)
                    .belowMinRate(belowMinRate)
                    .maxVoltage(maxVoltage)
                    .minVoltage(minVoltage)
                    .totalReadings(totalReadings)
                    .qualifiedReadings(qualifiedReadings)
                    .aboveMaxReadings(aboveMaxReadings)
                    .belowMinReadings(belowMinReadings)
                    .build());
        }

        return userStatsList;
    }

    private static CourtsStatistics calculateOverallStats(List<UserStatistics> userStatsList) {
        int totalUsers = userStatsList.size();
        int totalReadings = 0;
        int totalQualifiedReadings = 0;
        int totalAboveMaxReadings = 0;
        int totalBelowMinReadings = 0;
        double overallMaxVoltage = Double.MIN_VALUE;
        double overallMinVoltage = Double.MAX_VALUE;

        for (UserStatistics stats : userStatsList) {
            totalReadings += stats.getTotalReadings();
            totalQualifiedReadings += stats.getQualifiedReadings();
            totalAboveMaxReadings += stats.getAboveMaxReadings();
            totalBelowMinReadings += stats.getBelowMinReadings();

            if (stats.getMaxVoltage() > overallMaxVoltage) overallMaxVoltage = stats.getMaxVoltage();
            if (stats.getMinVoltage() < overallMinVoltage) overallMinVoltage = stats.getMinVoltage();
        }

        double overallQualifiedRate = totalReadings > 0 ? (double) totalQualifiedReadings / totalReadings * 100 : 0;
        double overallAboveMaxRate = totalReadings > 0 ? (double) totalAboveMaxReadings / totalReadings * 100 : 0;
        double overallBelowMinRate = totalReadings > 0 ? (double) totalBelowMinReadings / totalReadings * 100 : 0;

        return CourtsStatistics.builder()
                .totalUsers(totalUsers)
                .qualifiedRate(overallQualifiedRate)
                .aboveMaxRate(overallAboveMaxRate)
                .belowMinRate(overallBelowMinRate)
                .maxVoltage(overallMaxVoltage)
                .minVoltage(overallMinVoltage)
                .totalReadings(totalReadings)
                .qualifiedReadings(totalQualifiedReadings)
                .aboveMaxReadings(totalAboveMaxReadings)
                .belowMinReadings(totalBelowMinReadings)
                .build();
    }

    private static void generateReport(String filePath, List<UserStatistics> userStatsList,
                                       CourtsStatistics overallStats) {
        // 空值检查，如果为null则创建默认统计对象
        if (overallStats == null) {
            overallStats = CourtsStatistics.builder()
                    .totalUsers(0)
                    .qualifiedRate(0.0)
                    .aboveMaxRate(0.0)
                    .belowMinRate(0.0)
                    .maxVoltage(0.0)
                    .minVoltage(0.0)
                    .totalReadings(0)
                    .qualifiedReadings(0)
                    .aboveMaxReadings(0)
                    .belowMinReadings(0)
                    .build();
        }

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("电压合格率报表");

            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);

            // 创建数据样式（百分比格式）
            CellStyle percentStyle = workbook.createCellStyle();
            percentStyle.setDataFormat(workbook.createDataFormat().getFormat("0.00%"));

            // 创建数据样式（小数格式）
            CellStyle decimalStyle = workbook.createCellStyle();
            decimalStyle.setDataFormat(workbook.createDataFormat().getFormat("0.00"));

            // 创建标题行
            Row titleRow = sheet.createRow(0);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue("电压合格率报表");
            titleCell.setCellStyle(headerStyle);

            // 合并标题单元格
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 6));

            // 创建整体统计行
            Row overallRow1 = sheet.createRow(2);
            overallRow1.createCell(0).setCellValue("整体统计");
            overallRow1.getCell(0).setCellStyle(headerStyle);

            Row overallRow2 = sheet.createRow(3);
            overallRow2.createCell(0).setCellValue("总用户数");
            overallRow2.createCell(1).setCellValue(overallStats.getTotalUsers() != null ? overallStats.getTotalUsers() : 0);

            Row overallRow3 = sheet.createRow(4);
            overallRow3.createCell(0).setCellValue("整体电压合格率");
            Cell qualifiedRateCell = overallRow3.createCell(1);
            qualifiedRateCell.setCellValue((overallStats.getQualifiedRate() != null ? overallStats.getQualifiedRate() : 0.0) / 100);
            qualifiedRateCell.setCellStyle(percentStyle);

            Row overallRow4 = sheet.createRow(5);
            overallRow4.createCell(0).setCellValue("整体电压越上限率");
            Cell aboveMaxRateCell = overallRow4.createCell(1);
            aboveMaxRateCell.setCellValue((overallStats.getAboveMaxRate() != null ? overallStats.getAboveMaxRate() : 0.0) / 100);
            aboveMaxRateCell.setCellStyle(percentStyle);

            Row overallRow5 = sheet.createRow(6);
            overallRow5.createCell(0).setCellValue("整体电压越下限率");
            Cell belowMinRateCell = overallRow5.createCell(1);
            belowMinRateCell.setCellValue((overallStats.getBelowMinRate() != null ? overallStats.getBelowMinRate() : 0.0) / 100);
            belowMinRateCell.setCellStyle(percentStyle);

            Row overallRow6 = sheet.createRow(7);
            overallRow6.createCell(0).setCellValue("整体最高电压(V)");
            Cell maxVoltageCell = overallRow6.createCell(1);
            maxVoltageCell.setCellValue(overallStats.getMaxVoltage() != null ? overallStats.getMaxVoltage() : 0.0);
            maxVoltageCell.setCellStyle(decimalStyle);

            Row overallRow7 = sheet.createRow(8);
            overallRow7.createCell(0).setCellValue("整体最低电压(V)");
            Cell minVoltageCell = overallRow7.createCell(1);
            minVoltageCell.setCellValue(overallStats.getMinVoltage() != null ? overallStats.getMinVoltage() : 0.0);
            minVoltageCell.setCellStyle(decimalStyle);

            // 创建用户详情表头
            Row headerRow = sheet.createRow(10);
            String[] headers = {"用户名称", "电压合格率", "电压越上限率", "电压越下限率", "最高电压(V)", "最低电压(V)"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // 填充用户详情数据
            int rowNum = 11;
            for (UserStatistics stats : userStatsList) {
                Row row = sheet.createRow(rowNum++);

                row.createCell(0).setCellValue(stats.getUserName());

                Cell cell1 = row.createCell(1);
                cell1.setCellValue(stats.getQualifiedRate() / 100);
                cell1.setCellStyle(percentStyle);

                Cell cell2 = row.createCell(2);
                cell2.setCellValue(stats.getAboveMaxRate() / 100);
                cell2.setCellStyle(percentStyle);

                Cell cell3 = row.createCell(3);
                cell3.setCellValue(stats.getBelowMinRate() / 100);
                cell3.setCellStyle(percentStyle);

                Cell cell4 = row.createCell(4);
                cell4.setCellValue(stats.getMaxVoltage());
                cell4.setCellStyle(decimalStyle);

                Cell cell5 = row.createCell(5);
                cell5.setCellValue(stats.getMinVoltage());
                cell5.setCellStyle(decimalStyle);
            }

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // 写入文件
            try (FileOutputStream fileOut = new FileOutputStream(filePath)) {
                workbook.write(fileOut);
            } catch (IOException e) {
                log.error("写入报表文件过程中发生异常",  e);
            }
        } catch (IOException e) {
            log.error("写入报表文件过程中发生异常",  e);
        }
    }

    /**
     * 创建报表样式
     */
    private static ReportStyles createReportStyles(Workbook workbook) {
        // 创建表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);

        // 创建百分比样式
        CellStyle percentStyle = workbook.createCellStyle();
        percentStyle.setDataFormat(workbook.createDataFormat().getFormat("0.00%"));

        // 创建小数样式
        CellStyle decimalStyle = workbook.createCellStyle();
        decimalStyle.setDataFormat(workbook.createDataFormat().getFormat("0.00"));

        return new ReportStyles(headerStyle, percentStyle, decimalStyle);
    }

    /**
     * 创建报表内容
     */
    private static void createReportContent(Sheet sheet, List<UserStatistics> userStatsList,
                                          CourtsStatistics overallStats, ReportStyles styles) {
        int rowNum = 0;

        // 创建标题
        Row titleRow = sheet.createRow(rowNum++);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("电压合格率报表");
        titleCell.setCellStyle(styles.headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 6));

        rowNum++; // 空行

        // 创建整体统计
        rowNum = createOverallStatistics(sheet, overallStats, styles, rowNum);

        rowNum++; // 空行

        // 创建用户详情表头
        Row headerRow = sheet.createRow(rowNum++);
        String[] headers = {"用户名称", "电压合格率", "电压越上限率", "电压越下限率", "最高电压(V)", "最低电压(V)"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(styles.headerStyle);
        }

        // 批量填充用户数据
        for (UserStatistics stats : userStatsList) {
            // 添加空值检查
            if (stats == null) {
                continue;
            }
            Row row = sheet.createRow(rowNum++);
            fillUserStatisticsRow(row, stats, styles);
        }
    }

    /**
     * 创建整体统计部分
     */
    private static int createOverallStatistics(Sheet sheet, CourtsStatistics overallStats,
                                             ReportStyles styles, int startRow) {
        int rowNum = startRow;

        // 空值检查，如果为null则创建默认统计对象
        if (overallStats == null) {
            overallStats = CourtsStatistics.builder()
                    .totalUsers(0)
                    .qualifiedRate(0.0)
                    .aboveMaxRate(0.0)
                    .belowMinRate(0.0)
                    .maxVoltage(0.0)
                    .minVoltage(0.0)
                    .totalReadings(0)
                    .qualifiedReadings(0)
                    .aboveMaxReadings(0)
                    .belowMinReadings(0)
                    .build();
        }

        // 整体统计标题
        Row titleRow = sheet.createRow(rowNum++);
        titleRow.createCell(0).setCellValue("整体统计");
        titleRow.getCell(0).setCellStyle(styles.headerStyle);

        // 统计数据行
        String[][] statsData = {
            {"总用户数", String.valueOf(overallStats.getTotalUsers() != null ? overallStats.getTotalUsers() : 0)},
            {"整体电压合格率", null}, // 特殊处理百分比
            {"整体电压越上限率", null},
            {"整体电压越下限率", null},
            {"整体最高电压(V)", null}, // 特殊处理小数
            {"整体最低电压(V)", null}
        };

        double[] percentValues = {
            (overallStats.getQualifiedRate() != null ? overallStats.getQualifiedRate() : 0.0) / 100,
            (overallStats.getAboveMaxRate() != null ? overallStats.getAboveMaxRate() : 0.0) / 100,
            (overallStats.getBelowMinRate() != null ? overallStats.getBelowMinRate() : 0.0) / 100
        };

        double[] decimalValues = {
            overallStats.getMaxVoltage() != null ? overallStats.getMaxVoltage() : 0.0,
            overallStats.getMinVoltage() != null ? overallStats.getMinVoltage() : 0.0
        };

        // 填充统计数据
        for (int i = 0; i < statsData.length; i++) {
            Row row = sheet.createRow(rowNum++);
            row.createCell(0).setCellValue(statsData[i][0]);

            Cell valueCell = row.createCell(1);
            if (i == 0) {
                // 总用户数
                valueCell.setCellValue(overallStats.getTotalUsers() != null ? overallStats.getTotalUsers() : 0);
            } else if (i >= 1 && i <= 3) {
                // 百分比数据
                valueCell.setCellValue(percentValues[i - 1]);
                valueCell.setCellStyle(styles.percentStyle);
            } else {
                // 小数数据
                valueCell.setCellValue(decimalValues[i - 4]);
                valueCell.setCellStyle(styles.decimalStyle);
            }
        }

        return rowNum;
    }

    /**
     * 填充用户统计行
     */
    private static void fillUserStatisticsRow(Row row, UserStatistics stats, ReportStyles styles) {
        row.createCell(0).setCellValue(stats.getUserName() != null ? stats.getUserName() : "");

        Cell cell1 = row.createCell(1);
        cell1.setCellValue((stats.getQualifiedRate() != null ? stats.getQualifiedRate() : 0.0) / 100);
        cell1.setCellStyle(styles.percentStyle);

        Cell cell2 = row.createCell(2);
        cell2.setCellValue((stats.getAboveMaxRate() != null ? stats.getAboveMaxRate() : 0.0) / 100);
        cell2.setCellStyle(styles.percentStyle);

        Cell cell3 = row.createCell(3);
        cell3.setCellValue((stats.getBelowMinRate() != null ? stats.getBelowMinRate() : 0.0) / 100);
        cell3.setCellStyle(styles.percentStyle);

        Cell cell4 = row.createCell(4);
        cell4.setCellValue(stats.getMaxVoltage() != null ? stats.getMaxVoltage() : 0.0);
        cell4.setCellStyle(styles.decimalStyle);

        Cell cell5 = row.createCell(5);
        cell5.setCellValue(stats.getMinVoltage() != null ? stats.getMinVoltage() : 0.0);
        cell5.setCellStyle(styles.decimalStyle);
    }

    /**
     * 优化列宽
     */
    private static void optimizeColumnWidths(Sheet sheet) {
        // 预设列宽，避免自动计算
        int[] columnWidths = {4000, 3000, 3500, 3500, 3000, 3000}; // 以1/256字符为单位

        for (int i = 0; i < columnWidths.length; i++) {
            sheet.setColumnWidth(i, columnWidths[i]);
        }
    }

    /**
     * 报表样式容器
     */
    private static class ReportStyles {
        final CellStyle headerStyle;
        final CellStyle percentStyle;
        final CellStyle decimalStyle;

        ReportStyles(CellStyle headerStyle, CellStyle percentStyle, CellStyle decimalStyle) {
            this.headerStyle = headerStyle;
            this.percentStyle = percentStyle;
            this.decimalStyle = decimalStyle;
        }
    }

    // 数据结构定义（保持向后兼容）
    static class VoltageRecord {
        String userName;
        List<Double> voltageValues;

        public VoltageRecord(String userName, List<Double> voltageValues) {
            this.userName = userName;
            this.voltageValues = voltageValues;
        }
    }

    /**
     * 从Excel文件中读取电压合格率统计数据
     *
     * @param reportFile Excel报表文件
     * @return CourtsStatistics 统计数据对象
     */
    public static CourtsStatistics readFromExcel(File reportFile) {
        if (reportFile == null || !reportFile.exists()) {
            log.warn("电压报表文件不存在");
            return null;
        }
        try (FileInputStream fis = new FileInputStream(reportFile);
             Workbook workbook = WorkbookFactory.create(fis)) {
            Sheet sheet = workbook.getSheet("电压合格率报表");
            if (sheet == null) {
                log.warn("报表文件中未找到'电压合格率报表'工作表");
                return null;
            }
            // 读取整体统计数据 - 添加空值检查
            int totalUsers = 0;
            double qualifiedRate = 0.0;
            double aboveMaxRate = 0.0;
            double belowMinRate = 0.0;
            double maxVoltage = 0.0;
            double minVoltage = 0.0;

            try {
                if (sheet.getRow(3) != null && sheet.getRow(3).getCell(1) != null) {
                    totalUsers = (int) sheet.getRow(3).getCell(1).getNumericCellValue();
                }
                if (sheet.getRow(4) != null && sheet.getRow(4).getCell(1) != null) {
                    qualifiedRate = sheet.getRow(4).getCell(1).getNumericCellValue() * 100;
                }
                if (sheet.getRow(5) != null && sheet.getRow(5).getCell(1) != null) {
                    aboveMaxRate = sheet.getRow(5).getCell(1).getNumericCellValue() * 100;
                }
                if (sheet.getRow(6) != null && sheet.getRow(6).getCell(1) != null) {
                    belowMinRate = sheet.getRow(6).getCell(1).getNumericCellValue() * 100;
                }
                if (sheet.getRow(7) != null && sheet.getRow(7).getCell(1) != null) {
                    maxVoltage = sheet.getRow(7).getCell(1).getNumericCellValue();
                }
                if (sheet.getRow(8) != null && sheet.getRow(8).getCell(1) != null) {
                    minVoltage = sheet.getRow(8).getCell(1).getNumericCellValue();
                }
            } catch (Exception e) {
                log.warn("读取报表统计数据时发生异常，使用默认值: {}", e.getMessage());
            }
            return CourtsStatistics.builder()
                    .totalUsers(totalUsers)
                    .qualifiedRate(qualifiedRate)
                    .aboveMaxRate(aboveMaxRate)
                    .belowMinRate(belowMinRate)
                    .maxVoltage(maxVoltage)
                    .minVoltage(minVoltage)
                    .build();
        } catch (IOException e) {
            log.error("读取电压报表文件时发生错误", e);
            return null;
        }
    }

    /**
     * 获取符合条件的用户记录，支持分页
     */
    public static List<UserStatistics> readUserStatisticsFromReport(File reportFile, String keyword,
                                                                    Integer pageNum, Integer pageSize) {
        List<UserStatistics> resultList = new ArrayList<>();
        if (reportFile == null || !reportFile.exists()) {
            log.warn("电压报表文件不存在");
            return resultList;
        }
        try (FileInputStream fis = new FileInputStream(reportFile);
             Workbook workbook = WorkbookFactory.create(fis)) {
            Sheet sheet = workbook.getSheet("电压合格率报表");
            if (sheet == null) {
                log.warn("报表中没有找到指定的工作表：电压合格率报表");
                return resultList;
            }
            // 获取所有符合关键字条件的记录
            List<UserStatistics> allMatchedRecords = new ArrayList<>();
            for (int rowNum = 11; rowNum <= sheet.getLastRowNum(); rowNum++) {
                Row row = sheet.getRow(rowNum);
                if (row == null) continue;
                String userName = row.getCell(0).getStringCellValue();
                if (userName == null || userName.trim().isEmpty()) continue;
                // 关键字过滤
                if (keyword != null && !keyword.trim().isEmpty() && !userName.contains(keyword.trim())) {
                    continue;
                }
                // 构建 UserStatistics 对象并加入符合条件的记录
                try {
                    UserStatistics stats = UserStatistics.builder()
                            .userName(userName)
                            .qualifiedRate(row.getCell(1).getNumericCellValue() * 100)
                            .aboveMaxRate(row.getCell(2).getNumericCellValue() * 100)
                            .belowMinRate(row.getCell(3).getNumericCellValue() * 100)
                            .maxVoltage(row.getCell(4).getNumericCellValue())
                            .minVoltage(row.getCell(5).getNumericCellValue())
                            .build();
                    allMatchedRecords.add(stats);
                } catch (Exception e) {
                    log.debug("读取用户统计数据时发生异常: 用户名={}, 行号={}, 异常信息={}",
                        userName, rowNum, e.getMessage());
                    // 跳过这条记录，继续处理下一条
                }
            }
            // 进行分页
            if (pageNum != null && pageSize != null && pageNum > 0 && pageSize > 0) {
                int startIndex = (pageNum - 1) * pageSize;
                int endIndex = Math.min(startIndex + pageSize, allMatchedRecords.size());
                resultList = allMatchedRecords.subList(startIndex, endIndex);
            } else {
                // 没有分页参数时，返回所有数据
                resultList = allMatchedRecords;
            }
        } catch (IOException e) {
            log.error("文件读取错误", e);
        } catch (Exception e) {
            log.error("读取用户电压统计数据时发生错误", e);
        }
        return resultList;
    }


    /**
     * 获取符合条件的用户数量
     */
    public static int getUserStatisticsCount(File reportFile, String keyword) {
        if (reportFile == null || !reportFile.exists()) {
            log.warn("电压报表文件不存在");
            return 0;
        }
        int count = 0;
        try (FileInputStream fis = new FileInputStream(reportFile);
             Workbook workbook = WorkbookFactory.create(fis)) {
            Sheet sheet = workbook.getSheet("电压合格率报表");
            if (sheet == null) {
                log.warn("报表中没有找到指定的工作表：电压合格率报表");
                return 0;
            }
            // 遍历所有行，统计符合条件的用户数量
            for (int rowNum = 11; rowNum <= sheet.getLastRowNum(); rowNum++) {
                Row row = sheet.getRow(rowNum);
                if (row == null) continue;
                String userName = row.getCell(0).getStringCellValue();
                if (userName == null || userName.trim().isEmpty()) continue;
                // 关键字过滤
                if (keyword != null && !keyword.trim().isEmpty() && !userName.contains(keyword.trim())) {
                    continue;
                }
                count++;
            }
        } catch (IOException e) {
            log.error("文件读取错误", e);
        } catch (Exception e) {
            log.error("读取用户电压统计数据时发生错误", e);
        }
        return count;
    }
}


