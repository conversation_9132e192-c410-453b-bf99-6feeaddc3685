package com.cet.electric.ngapserver.web.controller;

import com.cet.electric.ngapserver.dto.ErrorMsg;
import com.cet.electric.ngapserver.dto.LineCodeQueryDTO;
import com.cet.electric.ngapserver.dto.ResponseDTO;
import com.cet.electric.ngapserver.entity.LineCode;
import com.cet.electric.ngapserver.service.LineCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@Api(value = "LineCodeController API", tags = "LineCode")
@RequestMapping("/ngap-server/api/linecodes")
public class LineCodeController {

    private static final Logger log = LoggerFactory.getLogger(LineCodeController.class);

    @Autowired
    private LineCodeService lineCodeService;

    @PostMapping("/save")
    @ApiOperation(value = "批量保存线路代码(创建或更新)")
    public ResponseDTO<List<LineCode>> saveLineCode(@RequestBody List<LineCode> lineCodeEntities) {
        log.info("接收到批量保存线路代码请求，数量: {}", lineCodeEntities.size());

        List<LineCode> results = new ArrayList<>();
        List<String> messages = new ArrayList<>();
        boolean hasErrors = false;

        for (LineCode lineCodeEntity : lineCodeEntities) {
            boolean isUpdate = lineCodeEntity.getLineCodeId() != null;

            if (lineCodeEntity.getLineCode() != null) {
                lineCodeEntity.setLineCode(lineCodeEntity.getLineCode().trim());
            } else {
                lineCodeEntity.setLineCode("");
            }

            try {
                LineCode result;
                if (isUpdate) {
                    result = lineCodeService.updateLineCode(lineCodeEntity);
                    log.info("线路代码更新成功: {}", result);
                    results.add(result);
                } else {
                    result = lineCodeService.createLineCode(lineCodeEntity);
                    log.info("线路代码创建成功，ID: {}", result.getLineCodeId());
                    results.add(result);
                }
            } catch (ErrorMsg e) {
                hasErrors = true;
                log.error("线路代码操作失败: {}", e.getMessage());
                messages.add(e.getMessage());
            }
        }

        if (hasErrors) {
            return new ResponseDTO<>(-1, "部分线路代码操作失败: " + String.join("; ", messages), results);
        } else {
            return new ResponseDTO<>(0, "所有线路代码操作成功", results);
        }
    }


    @PostMapping("/query")
    @ApiOperation(value = "分页查询线路代码列表")
    public ResponseDTO<List<LineCode>> getLineCodes(@RequestBody LineCodeQueryDTO queryDTO) {
        log.info("接收到分页查询线路代码请求: {}", queryDTO);

        // 从DTO中提取参数
        Integer page = queryDTO.getPage();
        Integer size = queryDTO.getSize();
        String sortBy = queryDTO.getSortBy();
        String sortOrder = queryDTO.getSortOrder();
        String keyword = queryDTO.getKeyword();

        List<LineCode> result = lineCodeService.getLineCodes(page, size, sortBy, sortOrder, keyword);
        Long total = lineCodeService.countLineCodes(keyword);

        log.info("线路代码查询成功，总数量: {}", total);
        return new ResponseDTO<>(0, "查询成功", result, total);
    }

    @GetMapping("/detail")
    @ApiOperation(value = "根据ID查询线路代码")
    public ResponseDTO<LineCode> getLineCodeById(@RequestParam("lineCodeId") Long lineCodeId) {
        log.info("接收到查询线路代码请求: lineCodeId={}", lineCodeId);

        LineCode result = lineCodeService.getLineCodeById(lineCodeId);

        log.info("线路代码查询成功: {}", result);
        return new ResponseDTO<>(0, "查询成功", result);
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "删除线路代码")
    public ResponseDTO<Boolean> deleteLineCode(@RequestParam("lineCodeId") Long lineCodeId) {
        log.info("接收到删除线路代码请求: lineCodeId={}", lineCodeId);

        boolean result = lineCodeService.deleteLineCode(lineCodeId);

        log.info("线路代码删除结果: {}", result);
        return new ResponseDTO<>(0, result ? "线路代码删除成功" : "线路代码删除失败", result);
    }
}



