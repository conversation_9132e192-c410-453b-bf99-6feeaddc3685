<template>
  <div class="layout-user">
    <div class="layout-user-btn" @click="visible = true">
      <omega-icon class="icon-size-I2 icon-p5" symbolId="account-lin" />
      <span class="layout-user-name">{{ userName || "ROOT" }}</span>
    </div>
    <el-dialog
      width="450px"
      :center="true"
      :append-to-body="true"
      :visible.sync="visible"
      @open="onDialogOpen"
    >
      <el-form class="text-right" label-width="100px" label-position="left">
        <el-form-item v-for="item in items" :key="item.label">
          <template #label>
            {{ item.label }}
          </template>
          {{ item.value }}
        </el-form-item>
      </el-form>

      <template #title>
        <omega-icon class="I5" icon-class="layout_user" />
      </template>
      <template #footer>
        <div class="clearfix">
          <el-button class="fl" type="danger" @click="evLogoutClick">
            {{ $T("退出") }}
          </el-button>

          <!-- <el-button class="fr" @click="visible = false">
            {{ $T("修改资料") }}
          </el-button> -->
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { showOmegaDialog } from "@omega/widget";
import ResetPassword from "./ResetPassword";
import omegaAuth from "@omega/auth";
import util from "@/utils/util";
import { TokenStore } from "@omega/auth/tokenStore.js";
import fetch from "@/utils/fetch";

export default {
  name: "LayoutUser",
  data() {
    return {
      items: [],
      userName: omegaAuth.user.getUserName(),
      visible: false
    };
  },
  computed: {},
  methods: {
    evLogoutClick() {
      TokenStore.clear();
      return this.$router.push("/login");
    },
    evResetPassword() {
      const e = showOmegaDialog(ResetPassword);
      e.on("confirm", () => {
        TokenStore.clear();
        return this.$router.push("/login");
      });
    },
    // 清除所有cookies
    clearCookies() {
      let cookies = document.cookie.split("; ");
      for (let i = 0; i < cookies.length; i++) {
        let cookieName = cookies[i].split("=")[0];
        console.log(cookieName, "cookieName");
        document.cookie =
          cookieName +
          "=;expires=Thu, 01 Jan 1970 00:00:00 UTC;domain=.midea.com";
      }
    },
    onDialogOpen() {
      const _user = omegaAuth.user._user;
      const roleName = _user.roles?.[0]?.name;
      const items = [
        {
          label: $T("用户名："),
          value: _user.name || "ROOT"
        },
        {
          label: $T("角色："),
          value: roleName || "超级管理员"
        },
        {
          label: $T("电话："),
          value: _user.mobilePhone
        },
        {
          label: $T("邮箱："),
          value: _user.email
        }
      ];
      items.forEach(item => util.fillBlankObject(item));

      this.items = items;
    }
  }
};
</script>

<style lang="scss" scoped>
.layout-user {
  transition-duration: 0.3s;
  @include font_color(T2);
  @include font_size(Aa);
  &:hover {
    @include background_color(BG2);
  }
  &:active,
  &:focus {
    @include background_color(BG3);
  }
}
.layout-user-btn {
  display: flex;
  align-items: center;
  border-radius: 4px;
  cursor: pointer;
}
.layout-user-name {
  @include padding_right(J1);
}
</style>
