package com.cet.electric.ngapserver.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ResponseDTO<T>{
    private int code;
    private String msg;
    private T data;
    private Long total;
    @Override
    public String toString() {
        return "ResponseDTO [code=" + code + ", msg=" + msg + ", data=" + data + "]";
    }
    public ResponseDTO(int code, String msg, T data) {
        super();
        this.code = code;
        this.msg = msg;
        this.data = data;
    }
    public ResponseDTO(int code, String msg, T data,Long total) {
        super();
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.total = total;
    }
}
