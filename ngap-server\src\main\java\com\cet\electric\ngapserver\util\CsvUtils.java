package com.cet.electric.ngapserver.util;
import com.cet.electric.ngapserver.entity.DataPoint;
import com.cet.electric.ngapserver.entity.Metric;
import com.cet.electric.ngapserver.entity.MetricData;
import com.cet.electric.ngapserver.entity.MetricStatistics;
import com.cet.electric.ngapserver.util.voltage.VoltageStatisticsCalculator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static com.cet.electric.ngapserver.util.ExcelUtils.calculateStatistics;

/**
 * CSV文件处理工具类
 */
public class CsvUtils {
    private static final Logger log = LoggerFactory.getLogger(CsvUtils.class);
    /**
     * 从CSV文件所在目录提取指标结构
     *
     * @param directoryPath CSV文件所在目录路径
     * @param rootMetric 根指标节点，提取的CSV指标将添加到该节点下
     */
    public static void extractMetrics(String directoryPath, Metric rootMetric) {
        log.info("开始从目录提取CSV指标: {}", directoryPath);

        // 获取目录对象
        File directory = new File(directoryPath);

        // 检查目录是否存在且是否为目录
        if (!directory.exists() || !directory.isDirectory()) {
            log.warn("CSV目录不存在或不是有效目录: {}", directoryPath);
            return;
        }

        // 获取所有文件
        File[] files = directory.listFiles();
        if (files == null) {
            log.warn("无法列出目录中的文件: {}", directoryPath);
            return;
        }

        // 遍历目录中的所有CSV文件
        for (File file : files) {
            if (file.isFile() && file.getName().toLowerCase().endsWith(".csv")) {
                processCSVFile(file, rootMetric);
            }
        }

        log.info("成功从目录提取CSV指标: {}, 共 {} 个子指标", directoryPath, rootMetric.getChildren().size());
    }

    /**
     * 处理单个CSV文件，提取其中的指标
     *
     * @param file CSV文件
     * @param rootMetric 根指标节点
     */
    private static void processCSVFile(File file, Metric rootMetric) {
        String fileName = file.getName();
        String fileNameWithoutExt = fileName.substring(0, fileName.length() - 4);
        log.debug("处理CSV文件: {}", fileName);

        // 为该文件创建子Metric列表
        List<Metric> childMetrics = new ArrayList<>();

        // 读取CSV文件的第一行以获取列名
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String headerLine = reader.readLine();
            if (headerLine != null) {
                extractColumnsFromHeader(headerLine, fileNameWithoutExt, childMetrics);
            }
        } catch (IOException e) {
            log.warn("读取CSV文件头部失败: {}, 错误: {}", fileName, e.getMessage());
        }

        // 创建父级Metric并添加到结果列表中
        if (!childMetrics.isEmpty()) {
            Metric parentMetric = Metric.builder()
                    .metricId(fileNameWithoutExt)
                    .metricName(fileNameWithoutExt)
                    .children(childMetrics)
                    .build();
            rootMetric.getChildren().add(parentMetric);
        }
    }

    /**
     * 从CSV头部行提取列信息
     *
     * @param headerLine CSV头部行
     * @param fileNameWithoutExt 不含扩展名的文件名
     * @param childMetrics 子指标列表，用于存储结果
     */
    private static void extractColumnsFromHeader(String headerLine, String fileNameWithoutExt, List<Metric> childMetrics) {
        // 解析CSV头部行
        String[] headers = parseCSVLine(headerLine);

        // 跳过第一列和第二列（hour和t(sec)）
        for (int i = 2; i < headers.length; i++) {
            String columnName = headers[i].trim();

            // 去除可能存在的引号
            if (columnName.startsWith("\"") && columnName.endsWith("\"")) {
                columnName = columnName.substring(1, columnName.length() - 1);
            }

            // 创建子Metric对象 - 指标节点
            Metric childMetric = Metric.builder()
                    .metricId(fileNameWithoutExt + "." + columnName)
                    .metricName(columnName)
                    // 指标节点没有子节点
                    .children(null)
                    .build();

            childMetrics.add(childMetric);
        }
    }

    /**
     * 从CSV文件中获取指标数据
     *
     * @param filePath       CSV文件路径
     * @param metricId       指标ID，格式为"文件名.列名"
     * @param startTimestamp 起始时间戳
     * @return 指标数据列表
     */
    public static List<MetricData> getMetricData(String filePath, String metricId, Long startTimestamp) {
        log.info("开始从CSV文件获取指标数据: {}, 指标ID: {}", filePath, metricId);

        // 解析metricId，提取文件名和列名
        String[] parts = metricId.split("\\.", 2);
        if (parts.length != 2) {
            log.warn("指标ID格式不正确，无法从CSV提取数据: {}", metricId);
            return new ArrayList<>();
        }

        String fileName = parts[0];
        String columnName = parts[1];

        // 检查文件是否存在
        File file = new File(filePath);
        if (!file.exists() || !file.isFile()) {
            log.info("CSV文件不存在: {}", filePath);
            return new ArrayList<>();
        }

        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            List<MetricData> result = extractDataFromCSV(reader, metricId, columnName, startTimestamp);
            log.info("成功从CSV文件获取指标数据: {}, 指标: {}, 数据点数: {}",
                    filePath, metricId,
                    result.isEmpty() ? 0 : (result.get(0).getDataPoints() != null ? result.get(0).getDataPoints().size() : 0));
            return result;
        } catch (IOException e) {
            log.error("读取CSV文件失败: {}", e.getMessage());
            throw new RuntimeException("获取CSV指标数据失败：" + e.getMessage());
        }
    }

    /**
     * 从CSV文件内容中提取指标数据
     *
     * @param reader         文件读取器
     * @param metricId       完整的指标ID
     * @param columnName     列名
     * @param startTimestamp 起始时间戳
     * @return 指标数据列表
     * @throws IOException 如果读取文件时发生错误
     */
    private static List<MetricData> extractDataFromCSV(BufferedReader reader, String metricId, String columnName, Long startTimestamp) throws IOException {
        // 读取CSV文件的第一行获取列标题
        String headerLine = reader.readLine();
        if (headerLine == null) {
            log.error("CSV文件为空");
            throw new RuntimeException("获取指标数据失败：CSV文件为空");
        }

        // 解析CSV头部行
        String[] headers = parseCSVLine(headerLine);

        // 查找目标列的索引
        int columnIndex = findColumnIndex(headers, columnName);
        if (columnIndex == -1) {
            log.info("CSV文件中不存在列 '{}'", columnName);
            return new ArrayList<>();
        }

        // 假设第一列(索引0)为小时，第二列(索引1)为秒
        int hourColumnIndex = 0;
        int secColumnIndex = 1;

        // 读取数据并构建DataPoint列表
        List<DataPoint> dataPoints = readDataPoints(reader, columnIndex, hourColumnIndex, secColumnIndex, startTimestamp);

        // 创建Metric对象
        Metric metric = Metric.builder()
                .metricId(metricId)
                .metricName(columnName)
                .build();

        MetricStatistics statistics = calculateStatistics(dataPoints, metric);

        // 构建MetricData对象
        MetricData metricData = MetricData.builder()
                .metric(metric)
                // 根据格式需求设置为"小时"
                .xUnit(null)
                .yUnit(columnName != null && columnName.matches(".*\\(.*\\).*") ? columnName.replaceAll(".*\\((.*)\\).*", "$1") : null)
                .dataPoints(dataPoints)
                .statistics(statistics)
                .build();

        // 创建一个列表并将metricData放入其中
        List<MetricData> metricDataList = new ArrayList<>();
        metricDataList.add(metricData);
        return metricDataList;
    }

    /**
     * 查找列名对应的索引
     *
     * @param headers CSV头部数组
     * @param columnName 要查找的列名
     * @return 列索引，如果不存在则返回-1
     */
    private static int findColumnIndex(String[] headers, String columnName) {
        for (int i = 0; i < headers.length; i++) {
            String header = headers[i].trim();
            // 去除可能存在的引号
            if (header.startsWith("\"") && header.endsWith("\"")) {
                header = header.substring(1, header.length() - 1);
            }
            if (header.equals(columnName)) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 读取CSV文件中的数据点
     *
     * @param reader          文件读取器
     * @param columnIndex     目标数据列索引
     * @param hourColumnIndex 小时列索引
     * @param secColumnIndex  秒列索引
     * @param startTimestamp  起始时间戳
     * @return 数据点列表
     * @throws IOException 如果读取文件时发生错误
     */
    private static List<DataPoint> readDataPoints(BufferedReader reader, int columnIndex, int hourColumnIndex, int secColumnIndex, Long startTimestamp) throws IOException {
        List<DataPoint> dataPoints = new ArrayList<>();
        String line;

        // 创建日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        while ((line = reader.readLine()) != null) {
            String[] values = parseCSVLine(line);

            // 确保数组长度足够包含所有需要的列
            int maxRequiredIndex = Math.max(columnIndex, Math.max(hourColumnIndex, secColumnIndex));
            if (values.length > maxRequiredIndex) {
                try {
                    // 提取小时值
                    double hours = Double.parseDouble(values[hourColumnIndex].trim());
                    // 提取秒值并转换为小时
                    double seconds = Double.parseDouble(values[secColumnIndex].trim());
                    // 转换秒为小时
                    double secondsInHours = seconds / 3600.0;
                    // 计算总小时数
                    double totalHours = hours + secondsInHours;
                    // 提取数据值
                    double value = Double.parseDouble(values[columnIndex].trim());

                    if(startTimestamp == null){
                        // 获取当前日期的0点时间戳
                        LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
                        long todayMidnightTimestamp = today.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                        startTimestamp = todayMidnightTimestamp;
                    }

                    // 计算实际时间戳
                    long actualTimestamp = startTimestamp + (long)(totalHours * 3600 * 1000);

                    // 将时间戳转换为LocalDateTime
                    LocalDateTime dateTime = LocalDateTime.ofInstant(
                            Instant.ofEpochMilli(actualTimestamp),
                            ZoneId.systemDefault()
                    );

                    // 格式化为yyyy-MM-dd HH:mm格式
                    String formattedDate = dateTime.format(formatter);

                    // 创建DataPoint并添加到列表
                    DataPoint dataPoint = DataPoint.builder()
                            .timestamp(formattedDate)
                            .value(value)
                            .build();
                    dataPoints.add(dataPoint);
                } catch (NumberFormatException e) {
                    // 跳过无法解析的行
                    log.warn("跳过无法解析的数据行: {}", line);
                }
            }
        }

        // 按时间戳排序数据点
        dataPoints.sort((dp1, dp2) -> {
            try {
                // 使用已定义的formatter进行时间戳比较
                LocalDateTime time1 = LocalDateTime.parse(dp1.getTimestamp(), formatter);
                LocalDateTime time2 = LocalDateTime.parse(dp2.getTimestamp(), formatter);
                return time1.compareTo(time2);
            } catch (Exception e) {
                // 如果时间解析失败，按字符串排序
                log.debug("时间戳解析失败，使用字符串排序: {}, {}", dp1.getTimestamp(), dp2.getTimestamp());
                return dp1.getTimestamp().compareTo(dp2.getTimestamp());
            }
        });

        log.debug("CSV数据点按时间排序完成，共 {} 个数据点", dataPoints.size());
        return dataPoints;
    }

    /**
     * 解析CSV行，正确处理引号内的逗号
     *
     * @param line CSV行
     * @return 解析后的字段数组
     */
    public static String[] parseCSVLine(String line) {
        List<String> result = new ArrayList<>();
        boolean inQuotes = false;
        StringBuilder field = new StringBuilder();
        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);
            if (c == '"') {
                inQuotes = !inQuotes;
            } else if (c == ',' && !inQuotes) {
                result.add(field.toString());
                field = new StringBuilder();
            } else {
                field.append(c);
            }
        }
        // 添加最后一个字段
        result.add(field.toString());
        return result.toArray(new String[0]);
    }

    /**
     * 从CSV文件目录中提取所有电压数据并计算合格率统计
     *
     * @param directoryPath CSV文件所在目录路径
     * @return 电压统计计算器，包含所有电压数据的统计信息
     */
    public static VoltageStatisticsCalculator extractVoltageDataFromCSVDirectory(String directoryPath) {
        log.info("开始从目录提取电压数据: {}", directoryPath);

        VoltageStatisticsCalculator calculator = new VoltageStatisticsCalculator();

        // 获取目录对象
        File directory = new File(directoryPath);

        // 检查目录是否存在且是否为目录
        if (!directory.exists() || !directory.isDirectory()) {
            log.warn("CSV目录不存在或不是有效目录: {}", directoryPath);
            return calculator;
        }

        // 获取所有CSV文件，使用文件过滤器提高性能
        File[] csvFiles = directory.listFiles((dir, name) ->
            name.toLowerCase().endsWith(".csv"));

        if (csvFiles == null || csvFiles.length == 0) {
            log.info("目录中未找到CSV文件: {}", directoryPath);
            return calculator;
        }

        log.info("找到 {} 个CSV文件，开始处理", csvFiles.length);

        // 处理每个CSV文件
        int processedFiles = 0;
        for (File file : csvFiles) {
            try {
                extractVoltageDataFromCSVFile(file, calculator);
                processedFiles++;
            } catch (Exception e) {
                log.error("处理CSV文件时发生错误: {}, 错误: {}", file.getName(), e.getMessage());
                // 继续处理其他文件，不中断整个流程
            }
        }

        log.info("完成电压数据提取，处理文件数: {}/{}, 总数据点: {}, 合格数据点: {}, 合格率: {}%",
                processedFiles, csvFiles.length, calculator.getTotalReadings(),
                calculator.getQualifiedReadings(), String.format("%.2f", calculator.getQualifiedRate()));

        return calculator;
    }

    /**
     * 从单个CSV文件中提取电压数据
     *
     * @param file CSV文件
     * @param calculator 电压统计计算器
     */
    private static void extractVoltageDataFromCSVFile(File file, VoltageStatisticsCalculator calculator) {
        String fileName = file.getName();
        log.debug("处理CSV文件中的电压数据: {}", fileName);

        // 记录处理前的统计数据
        int initialTotalReadings = calculator.getTotalReadings();
        int initialQualifiedReadings = calculator.getQualifiedReadings();

        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            // 读取头部行
            String headerLine = reader.readLine();
            if (headerLine == null) {
                log.warn("CSV文件为空: {}", fileName);
                return;
            }

            // 解析头部行，找到电压相关列并过滤全为0的列
            String[] headers = parseCSVLine(headerLine);
            List<Integer> voltageColumnIndices = findVoltageColumns(headers, file);

            if (voltageColumnIndices.isEmpty()) {
                log.debug("处理文件: {}, 未找到有效电压列", fileName);
                return;
            }

            // 获取被统计的列名
            List<String> voltageColumnNames = new ArrayList<>();
            for (int index : voltageColumnIndices) {
                String columnName = headers[index].trim();
                // 去除可能存在的引号
                if (columnName.startsWith("\"") && columnName.endsWith("\"")) {
                    columnName = columnName.substring(1, columnName.length() - 1);
                }
                voltageColumnNames.add(columnName);
            }

            log.debug("在文件 {} 中找到 {} 个有效电压列: {}", fileName, voltageColumnIndices.size(), voltageColumnNames);

            // 读取数据行
            String line;
            int lineNumber = 1;
            while ((line = reader.readLine()) != null) {
                lineNumber++;
                try {
                    String[] values = parseCSVLine(line);

                    // 提取电压值并添加到统计器
                    for (int columnIndex : voltageColumnIndices) {
                        if (columnIndex < values.length) {
                            String valueStr = values[columnIndex].trim();
                            if (!valueStr.isEmpty()) {
                                try {
                                    double voltage = Double.parseDouble(valueStr);
                                    calculator.addVoltageValue(voltage);
                                } catch (NumberFormatException e) {
                                    log.debug("跳过无法解析的电压值: {} (文件: {}, 行: {})", valueStr, fileName, lineNumber);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("处理CSV文件行时发生错误: {} (文件: {}, 行: {})", e.getMessage(), fileName, lineNumber);
                }
            }

            // 计算该文件的统计数据
            int fileTotalReadings = calculator.getTotalReadings() - initialTotalReadings;
            int fileQualifiedReadings = calculator.getQualifiedReadings() - initialQualifiedReadings;
            double fileQualifiedRate = fileTotalReadings > 0 ? (double) fileQualifiedReadings / fileTotalReadings * 100 : 0;

            // 输出该文件的合格率信息
            if (fileTotalReadings > 0) {
                log.debug("处理文件: {}, 统计列: {}, 数据点: {}, 合格数据点: {}, 合格率: {}%",
                        fileName, voltageColumnNames, fileTotalReadings, fileQualifiedReadings, String.format("%.2f", fileQualifiedRate));
            } else {
                log.debug("处理文件: {}, 未找到有效电压数据", fileName);
            }

        } catch (IOException e) {
            log.error("读取CSV文件失败: {}, 错误: {}", fileName, e.getMessage());
        }
    }

    /**
     * 查找电压相关的列索引
     *
     * @param headers CSV文件头部数组
     * @return 电压列的索引列表
     */
    private static List<Integer> findVoltageColumns(String[] headers) {
        List<Integer> voltageColumns = new ArrayList<>();

        for (int i = 0; i < headers.length; i++) {
            String header = headers[i].trim();

            // 去除可能存在的引号
            if (header.startsWith("\"") && header.endsWith("\"")) {
                header = header.substring(1, header.length() - 1);
            }

            if (isVoltageColumn(header)) {
                voltageColumns.add(i);
            }
        }

        return voltageColumns;
    }

    /**
     * 查找电压相关的列索引，并过滤掉全为0的列
     *
     * @param headers CSV文件头部数组
     * @param file CSV文件对象，用于检查列数据
     * @return 过滤后的电压列索引列表
     */
    private static List<Integer> findVoltageColumns(String[] headers, File file) {
        List<Integer> voltageColumns = findVoltageColumns(headers);

        if (voltageColumns.isEmpty()) {
            return voltageColumns;
        }

        // 过滤掉全为0的列
        List<Integer> filteredColumns = filterZeroColumns(voltageColumns, file);

        log.debug("电压列过滤结果: 原始{}列 -> 过滤后{}列", voltageColumns.size(), filteredColumns.size());

        return filteredColumns;
    }

    /**
     * 过滤掉全为0的列
     *
     * @param columnIndices 要检查的列索引列表
     * @param file CSV文件对象
     * @return 过滤后的列索引列表
     */
    private static List<Integer> filterZeroColumns(List<Integer> columnIndices, File file) {
        List<Integer> filteredColumns = new ArrayList<>();

        for (int columnIndex : columnIndices) {
            if (!isAllZeroColumn(columnIndex, file)) {
                filteredColumns.add(columnIndex);
            } else {
                log.debug("过滤掉全为0的列，索引: {}", columnIndex);
            }
        }

        return filteredColumns;
    }

    /**
     * 检查指定列是否全为0
     *
     * @param columnIndex 列索引
     * @param file CSV文件对象
     * @return 如果列全为0返回true，否则返回false
     */
    private static boolean isAllZeroColumn(int columnIndex, File file) {
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            // 跳过头部行
            reader.readLine();

            String line;
            while ((line = reader.readLine()) != null) {
                String[] values = parseCSVLine(line);

                if (columnIndex < values.length) {
                    String valueStr = values[columnIndex].trim();
                    if (!valueStr.isEmpty()) {
                        try {
                            double value = Double.parseDouble(valueStr);
                            // 如果发现非0值，则该列不是全0列
                            if (value != 0.0) {
                                return false;
                            }
                        } catch (NumberFormatException e) {
                            // 无法解析的值视为非0
                            return false;
                        }
                    }
                }
            }

            // 所有值都是0或空
            return true;

        } catch (IOException e) {
            log.warn("检查全0列时读取文件失败: {}", file.getName(), e);
            // 出错时保守处理，不过滤该列
            return false;
        }
    }

    /**
     * 判断列名是否为电压相关列
     *
     * @param columnName 列名
     * @return 如果是电压列返回true，否则返回false
     */
    public static boolean isVoltageColumn(String columnName) {
        if (columnName == null || columnName.trim().isEmpty()) {
            return false;
        }

        String lowerColumnName = columnName.toLowerCase();

        // 排除角度列（VAngle、IAngle等）
        if (lowerColumnName.contains("angle") ||
            lowerColumnName.matches(".*\\bvangle\\d*\\b.*") ||
            lowerColumnName.matches(".*\\biangle\\d*\\b.*")) {
            return false;
        }

        // 排除功率相关列（P1, Q1, P2, Q2, S1等）
        if (lowerColumnName.matches(".*\\bp\\d+\\b.*") ||   // P后面跟数字，如P1, P2
            lowerColumnName.matches(".*\\bq\\d+\\b.*") ||   // Q后面跟数字，如Q1, Q2
            lowerColumnName.matches(".*\\bs\\d+\\b.*") ||   // S后面跟数字，如S1, S2（视在功率）
            lowerColumnName.matches("^p\\d+$") ||           // 完全匹配p后跟数字
            lowerColumnName.matches("^q\\d+$") ||           // 完全匹配q后跟数字
            lowerColumnName.matches("^s\\d+$") ||           // 完全匹配s后跟数字
            lowerColumnName.contains("(kw)") ||             // 包含功率单位
            lowerColumnName.contains("(kvar)") ||           // 包含无功功率单位
            lowerColumnName.contains("(kva)") ||            // 包含视在功率单位
            lowerColumnName.contains("power") ||            // 包含power关键词
            lowerColumnName.contains("功率")) {              // 包含中文功率
            return false;
        }

        // 检查是否包含电压相关关键词
        return lowerColumnName.contains("voltage") ||
               lowerColumnName.contains("volt") ||
               lowerColumnName.contains("电压") ||
               lowerColumnName.matches(".*\\bv\\d+\\b.*") ||   // V后面跟数字，如V1, V2
               lowerColumnName.matches(".*\\bu\\d+\\b.*") ||   // U后面跟数字，如U1, U2
               lowerColumnName.matches(".*\\bv\\b.*") ||       // 单独的V字母
               lowerColumnName.matches(".*\\bu\\b.*") ||       // 单独的U字母（电压符号）
               (lowerColumnName.contains("kv") && !lowerColumnName.contains("kva")) ||  // kV但不是kVA
               lowerColumnName.contains("mv") ||
               lowerColumnName.startsWith("v_") ||            // v_开头，如v_rms
               lowerColumnName.startsWith("u_") ||            // u_开头，如u_phase
               lowerColumnName.endsWith("_v") ||
               lowerColumnName.endsWith("_u") ||
               lowerColumnName.matches("^v\\d+$") ||          // 完全匹配v后跟数字，如v1, v2
               lowerColumnName.matches("^u\\d+$") ||          // 完全匹配u后跟数字，如u1, u2
               lowerColumnName.equals("v") ||                 // 完全匹配单个v
               lowerColumnName.equals("u");                   // 完全匹配单个u
    }
}