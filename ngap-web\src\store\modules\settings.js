const defaultSeetings = {
  // soundMode: "single",
  noticeViewMode: "noticeDraweVisible",
  twinkle: false,
  monitor: true,
  videoDialog: true,
  isMuted: false,
  volume: 100
};
// eslint-disable-next-line camelcase
const web_settings = localStorage.getItem("web_settings");

// eslint-disable-next-line camelcase
const state = web_settings ? JSON.parse(web_settings) : defaultSeetings;

const mutations = {
  CHANGE_SETTING: (state, { key, value }) => {
    state[key] = value;
    localStorage.setItem("web_settings", JSON.stringify(state));
  }
};

const actions = {
  changeSetting({ commit }, data) {
    commit("CHANGE_SETTING", data);
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
