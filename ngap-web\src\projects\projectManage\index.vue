<template>
  <div class="page">
    <el-container class="fullheight" v-if="!showSimulationTask">
      <el-header class="p0" style="height: 32px">
        <ElInput
          class="fl mr8"
          v-model="ElInput_keyword.value"
          v-bind="ElInput_keyword"
          v-on="ElInput_keyword.event"
        ></ElInput>
        <inputWrap class="fl mr8" label="场景类型">
          <ElSelect
            clearable
            v-model="ElSelect_sceneType.value"
            v-bind="ElSelect_sceneType"
            v-on="ElSelect_sceneType.event"
          >
            <ElOption
              v-for="item in ElOption_sceneType.options_in"
              :key="item[ElOption_sceneType.key]"
              :label="item[ElOption_sceneType.label]"
              :value="item[ElOption_sceneType.value]"
              :disabled="item[ElOption_sceneType.disabled]"
            ></ElOption>
          </ElSelect>
        </inputWrap>

        <div class="fr fullheight">
          <CetDateSelect
            v-bind="CetDateSelect_time"
            v-on="CetDateSelect_time.event"
          ></CetDateSelect>

          <!-- <el-button class="ml8" icon="el-icon-upload2" @click="uploadProject">
            导入
          </el-button>
          <el-button icon="el-icon-download" @click="downloadProject">
            导出
          </el-button> -->

          <el-button
            class="ml10"
            type="primary"
            icon="el-icon-plus"
            @click="newProject"
          >
            新建项目
          </el-button>
        </div>
      </el-header>
      <el-main
        class="mt10 p0"
        style="height: calc(100% - 32px); overflow-x: hidden"
      >
        <emptyTip v-show="_.isEmpty(allProjectData)">
          {{ empty }}
        </emptyTip>
        <el-row :gutter="8">
          <el-col
            :span="6"
            class="mb8"
            v-for="item in allProjectData"
            :key="`project_${item.projectId}`"
          >
            <ProjectCard
              :key="`project_card_${item.projectId}`"
              :projectData="item"
              :scenariosMap="scenariosMap"
              @editProject="editProject"
              @refreshList="queryProjectList"
              @handleDetailClick="handleDetailClick"
            ></ProjectCard>
          </el-col>
        </el-row>
      </el-main>
      <el-footer height="40px" class="p0">
        <el-pagination
          class="fr"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          :page-sizes="pageSizes"
          :page-size.sync="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </el-footer>
      <!-- 导入 -->
      <Export v-bind="Export" v-on="Export.event" />
      <!-- 新增或编辑 -->
      <NewOrEditProjectDialog
        :isEdit="isEdit"
        :scenariosOPtion="ElOption_sceneType.options_in"
        v-bind="NewOrEditProjectDialog"
        v-on="NewOrEditProjectDialog.event"
      ></NewOrEditProjectDialog>
    </el-container>

    <simulationTask
      v-if="showSimulationTask"
      :detailData="detailData"
      @back="backProjectManage"
    ></simulationTask>
  </div>
</template>

<script>
import ProjectCard from "./components/ProjectCard.vue";
import Export from "./components/Export.vue";
import NewOrEditProjectDialog from "./components/NewOrEditProjectDialog.vue";
import { mhCONST } from "@/config/const";
import simulationTask from "@/projects/simulationTask/index.vue";
import customApi from "@/api/custom";
import common from "@/utils/common";

export default {
  name: "projectManage",
  components: { ProjectCard, Export, NewOrEditProjectDialog, simulationTask },
  data() {
    return {
      isEdit: false,
      showSimulationTask: false,
      scenariosMap: {},
      detailData: {},
      timeParams: {
        startTime: common.initDateRange("Y")[0],
        endTime: common.initDateRange("Y")[1]
      },
      tableParams: {
        keyword: "",
        projectType: "",
        page: 1,
        size: 50,
        sortBy: "created_at",
        sortOrder: "desc"
      },
      allProjectData: [], //项目列表数据
      empty: "暂无数据",
      currentPage: 1,
      pageSizes: [10, 20, 50, 100],
      pageSize: 50,
      total: 0,
      // 关键字检索
      ElInput_keyword: {
        value: "",
        placeholder: "请输入关键字检索",
        prefixIcon: "el-icon-search",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElInput_keyword_change_out
        }
      },
      // sceneType组件
      ElSelect_sceneType: {
        value: null,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_sceneType_change_out
        }
      },
      // sceneType组件
      ElOption_sceneType: {
        options_in: null,
        key: "value",
        value: "value",
        label: "name",
        disabled: "disabled"
      },
      // time组件
      CetDateSelect_time: {
        value: { dateType: "5", value: new Date().getTime() }, //设置日期值, dateType 1 日 2 周 3月 4季 5年 6自定义
        typeList: ["day", "month", "year"],
        event: {
          date_out: this.CetDateSelect_time_date_out,
          dateType_out: this.CetDateSelect_time_dateType_out
        }
      },
      Export: {
        openTrigger_in: new Date().getTime(),
        uploadUrl: "",
        event: {
          importSuccess: this.queryProjectList
        }
      },
      // NewOrEditProjectDialog弹窗页面组件
      NewOrEditProjectDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        event: {
          saveData_out: this.NewOrEditProjectDialog_saveData_out
        }
      }
    };
  },
  watch: {},
  methods: {
    // 关键字搜索输出
    ElInput_keyword_change_out(val) {
      this.queryProjectList();
    },
    // 场景类型输出
    ElSelect_sceneType_change_out(val) {
      this.queryProjectList();
    },
    // time输出
    CetDateSelect_time_date_out(val) {
      this.timeParams.startTime = val[0];
      this.timeParams.endTime = val[1];
      this.queryProjectList();
    },
    CetDateSelect_time_dateType_out() {},
    // 打开项目详情
    handleDetailClick(val) {
      this.detailData = _.cloneDeep(val);
      this.showSimulationTask = true;
    },
    // 返回项目列表
    backProjectManage() {
      this.showSimulationTask = false;
    },
    // 上传项目
    uploadProject() {
      this.Export.openTrigger_in = Date.now();
    },
    // 导出项目
    downloadProject() {},
    // 每页条数改变时触发
    handleSizeChange(val) {
      this.queryProjectList();
    },
    // 当前页改变时触发
    handleCurrentChange(val) {
      this.queryProjectList();
    },
    // 新建项目
    newProject() {
      this.isEdit = false;
      this.NewOrEditProjectDialog.openTrigger_in = Date.now();
    },
    // 编辑项目
    editProject(val) {
      this.isEdit = true;
      this.NewOrEditProjectDialog.inputData_in = _.cloneDeep(val);
      this.NewOrEditProjectDialog.openTrigger_in = Date.now();
    },
    // 获取项目列表
    queryProjectList() {
      let params = {
        keyword: this.ElInput_keyword.value,
        projectType: this.ElSelect_sceneType.value,
        page: this.currentPage,
        size: this.pageSize,
        sortBy: "created_at",
        sortOrder: "desc",
        ...this.timeParams
      };
      customApi["queryProjectManageList"](params).then(res => {
        this.allProjectData = common.get(res, "data", []);
        this.total = common.get(res, "total", 0);
      });
    },
    // 获取项目场景类型
    getProjectSceneType() {
      customApi["queryProjectScenarios"](this.queryParmas).then(res => {
        this.scenariosMap = _.get(res, "data", {});
        this.ElOption_sceneType.options_in = Object.entries(
          _.get(res, "data", {})
        ).map(([value, key]) => ({
          name: key,
          value: value
        }));
      });
    },
    // 新建或者编辑完成
    NewOrEditProjectDialog_saveData_out(val) {
      this.queryProjectList();
    }
  },
  mounted() {
    this.getProjectSceneType();
    this.queryProjectList();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}

.datetimepicker {
  ::v-deep .el-select {
    width: 85px !important;
  }
}
</style>
