{
  // Place your V2.x 工作区 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  // "Print to console": {
  // 	"scope": "javascript,typescript",
  // 	"prefix": "log",
  // 	"body": [
  // 		"console.log('$1');",
  // 		"$2"
  // 	],
  // 	"description": "Log output to console"
  // }

  //cet-table的代码片段
  "cet-table-template": {
    "prefix": "cet-table-template",
    "body": [
      "   <CetTable                                                                 ",
      "    :data.sync=\"CetTable_$1.data\"                                      ",
      "    :dynamicInput.sync=\"CetTable_$1.dynamicInput\"                      ",
      "    v-bind=\"CetTable_$1\"                                                  ",
      "    v-on=\"CetTable_${1:请输入组件唯一识别字符串}.event\"                   ",
      "   ></CetTable>                                                            "
    ],
    "description": ""
  },
  "cet-table-data": {
    "prefix": "cet-table-data",
    "body": [
      "// ${1:设置组件唯一识别字段}表格组件       ",
      " CetTable_$1: {        ",
      "   //组件模式设置项 ",
      "   queryMode: \"trigger\", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff                                   ",
      "   dataMode: \"backendInterface\", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static                    ",
      "   //组件数据绑定设置项                                        ",
      "   dataConfig: {                                         ",
      "     queryFunc: \"\",                                   ",
      "     deleteFunc: \"\",                                   ",
      "     modelLabel: \"\",                                   ",
      "     dataIndex: [],                       ",
      "     modelList: [],                       ",
      "     filters: [],  // 指定属性的过滤方法 示例： { name: \"name_in\", operator: \"LIKE\", prop: \"name\" }, 小于 \"LT\"  小于等于 \"LE\" 大于 \"GT\" 大于等于\"GE\" 相等  \"EQ\"  不等于 \"NE\" 像\"LIKE\" 间于\"BETWEEN\"         ",
      "     hasQueryNode: true     // 是否有queryNode入参, 没有则需要配置为false                 ",
      "   },                                   ",
      "   //组件输入项                                    ",
      "   data: [],                                   ",
      "   dynamicInput: {},                                        ",
      "   queryNode_in: null,                                   ",
      "   queryTrigger_in: new Date().getTime(),                                   ",
      "   exportTrigger_in: new Date().getTime(),                                   ",
      "   deleteTrigger_in: new Date().getTime(),                                   ",
      "   localDeleteTrigger_in: new Date().getTime(),                                   ",
      "   refreshTrigger_in: new Date().getTime(),                                   ",
      "   addData_in: {},                                   ",
      "   editData_in: {},                                        ",
      "   showPagination: true,                                        ",
      "   paginationCfg: {},                                        ",
      "   exportFileName: \"\",                                        ",
      "   //defaultSort: { prop: \"code\"  order: \"descending\" },                                   ",
      "   event: {                                                      ",
      "     record_out:this.CetTable_$1_record_out,                                                ",
      "     outputData_out: this.CetTable_$1_outputData_out                                        ",
      "   }                                                               ",
      " },                                                          "
    ],
    "description": ""
  },
  "cet-table-method": {
    "prefix": "cet-table-method",
    "body": [
      "// ${1:设置组件唯一识别字段}表格输出",
      "    CetTable_$1_record_out(val) {},                                   ",
      "    CetTable_$1_outputData_out(val) {},                                   "
    ],
    "description": ""
  },

  //表格列的代码片段
  "cet-column-template": {
    "prefix": "cet-column-template",
    "body": [" <ElTableColumn", "  v-bind=\"ElTableColumn_${1:请输入组件唯一识别字符串}\"                  ", " ></ElTableColumn>"],
    "description": ""
  },
  "cet-column-data": {
    "prefix": "cet-column-data",
    "body": [
      "// ${1:设置组件唯一识别字段}组件                               ",
      " ElTableColumn_$1: {                                       ",
      "   //type: \"\",      // selection 勾选 index 序号                                             ",
      "   prop: \"\",      // 支持path a[0].b                                              ",
      "   label: \"\",     //列名                                                ",
      "   headerAlign: \"center\",                                                     ",
      "   align: \"center\",                                                     ",
      "   showOverflowTooltip: true,                                                     ",
      "   //minWidth: \"200\",  //该宽度会自适应                                                   ",
      "   //width: \"100\",     //绝对宽度                                                ",
      "   //sortable: true,  //true 前端排序  \"custom\" 后端排序                                                   ",
      "   //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn                                              ",
      " },                                                                "
    ],
    "description": ""
  },
  //v-for表格列的代码片段
  "cet-columns-template": {
    "prefix": "cet-columns-template",
    "body": [
      "  <template v-for=\"item in Columns_${1:请输入组件唯一识别字符串}\">                                             ",
      "    <ElTableColumn :key=\"item.label\" v-bind=\"item\"></ElTableColumn>                                             ",
      "  </template>                                             "
    ],
    "description": ""
  },
  "cet-columns-data": {
    "prefix": "cet-columns-data",
    "body": [
      "// ${1:设置组件唯一识别字段}组件                               ",
      " Columns_$1: [{                                       ",
      "   type: \"selection\",      // selection 勾选 index 序号                                             ",
      "   headerAlign: \"center\",                                                     ",
      "   align: \"center\",                                                     ",
      "   //width: \"100\",     //绝对宽度                                                ",
      " },{                                                                ",
      "   type: \"index\",      // selection 勾选 index 序号                                             ",
      "   label: \"序号\",     //列名                                                ",
      "   headerAlign: \"center\",                                                     ",
      "   align: \"center\",                                                     ",
      "   showOverflowTooltip: true,                                                     ",
      "   //minWidth: \"200\",  //该宽度会自适应                                                   ",
      "   width: \"100\",     //绝对宽度                                                ",
      "   //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn                                              ",
      " },{                                                                ",
      "   //type: \"\",      // selection 勾选 index 序号                                             ",
      "   prop: \"demo\",      // 支持path a[0].b                                              ",
      "   label: \"demo\",     //列名                                                ",
      "   headerAlign: \"center\",                                                     ",
      "   align: \"center\",                                                     ",
      "   showOverflowTooltip: true,                                                     ",
      "   //minWidth: \"200\",  //该宽度会自适应                                                   ",
      "   //width: \"100\",     //绝对宽度                                                ",
      "   //sortable: true,  //true 前端排序  \"custom\" 后端排序                                                   ",
      "   //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn                                              ",
      " }],                                                                "
    ],
    "description": ""
  }
}
