package com.cet.electric.ngapserver.util;

import com.cet.electric.ngapserver.entity.CourtsStatistics;
import com.cet.electric.ngapserver.entity.UserStatistics;
import com.cet.electric.ngapserver.util.excel.ExcelCacheManager;
import com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor;
import com.cet.electric.ngapserver.util.voltage.VoltageObjectPool;
import com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor;
import com.cet.electric.ngapserver.util.voltage.VoltageStatisticsCalculator;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.TemporaryFolder;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.*;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

/**
 * VoltageQualityReport工具类单元测试
 * 
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({VoltageQualityReport.class})
public class VoltageQualityReportTest {

    @Rule
    public TemporaryFolder tempFolder = new TemporaryFolder();

    private File testInputFile;
    private File testReportFile;

    @Before
    public void setUp() throws IOException {
        MockitoAnnotations.initMocks(this);

        // 创建测试输入Excel文件
        testInputFile = tempFolder.newFile("test_input.xlsx");
        createTestInputFile(testInputFile);

        // 创建测试报表文件
        testReportFile = tempFolder.newFile("test_report.xlsx");
        createTestReportFile(testReportFile);

        // 清空缓存和性能统计
        ExcelCacheManager.getInstance().clear();
        VoltagePerformanceMonitor.getInstance().reset();
    }

    @After
    public void tearDown() {
        // 清理缓存
        if (ExcelCacheManager.getInstance() != null) {
            ExcelCacheManager.getInstance().clear();
        }
        if (VoltagePerformanceMonitor.getInstance() != null) {
            VoltagePerformanceMonitor.getInstance().reset();
        }
    }

    /**
     * 创建测试用的输入Excel文件
     */
    private void createTestInputFile(File inputFile) throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("用户电压");
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("用户名称");
            headerRow.createCell(1).setCellValue("u1");
            headerRow.createCell(2).setCellValue("u2");
            headerRow.createCell(3).setCellValue("u3");
            
            // 创建测试数据
            Row row1 = sheet.createRow(1);
            row1.createCell(0).setCellValue("用户A");
            row1.createCell(1).setCellValue(220.5); // 合格
            row1.createCell(2).setCellValue(245.0); // 超上限
            row1.createCell(3).setCellValue(195.0); // 低于下限
            
            Row row2 = sheet.createRow(2);
            row2.createCell(0).setCellValue("用户B");
            row2.createCell(1).setCellValue(225.0); // 合格
            row2.createCell(2).setCellValue(230.0); // 合格
            row2.createCell(3).setCellValue(215.0); // 合格
            
            Row row3 = sheet.createRow(3);
            row3.createCell(0).setCellValue("用户C");
            row3.createCell(1).setCellValue(250.0); // 超上限
            row3.createCell(2).setCellValue(190.0); // 低于下限
            row3.createCell(3).setCellValue(220.0); // 合格
            
            // 写入文件
            try (FileOutputStream fos = new FileOutputStream(inputFile)) {
                workbook.write(fos);
            }
        }
    }

    /**
     * 创建测试用的报表Excel文件
     */
    private void createTestReportFile(File reportFile) throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("电压合格率报表");
            
            // 创建标题行
            Row titleRow = sheet.createRow(0);
            titleRow.createCell(0).setCellValue("电压合格率报表");
            
            // 创建整体统计数据
            sheet.createRow(2).createCell(0).setCellValue("整体统计");
            
            Row totalUsersRow = sheet.createRow(3);
            totalUsersRow.createCell(0).setCellValue("总用户数");
            totalUsersRow.createCell(1).setCellValue(3);
            
            Row qualifiedRateRow = sheet.createRow(4);
            qualifiedRateRow.createCell(0).setCellValue("电压合格率");
            qualifiedRateRow.createCell(1).setCellValue(0.6667); // 66.67%
            
            Row aboveMaxRateRow = sheet.createRow(5);
            aboveMaxRateRow.createCell(0).setCellValue("电压越上限率");
            aboveMaxRateRow.createCell(1).setCellValue(0.2222); // 22.22%
            
            Row belowMinRateRow = sheet.createRow(6);
            belowMinRateRow.createCell(0).setCellValue("电压越下限率");
            belowMinRateRow.createCell(1).setCellValue(0.1111); // 11.11%
            
            Row maxVoltageRow = sheet.createRow(7);
            maxVoltageRow.createCell(0).setCellValue("最高电压(V)");
            maxVoltageRow.createCell(1).setCellValue(250.0);
            
            Row minVoltageRow = sheet.createRow(8);
            minVoltageRow.createCell(0).setCellValue("最低电压(V)");
            minVoltageRow.createCell(1).setCellValue(190.0);
            
            // 创建用户详情表头
            Row headerRow = sheet.createRow(10);
            headerRow.createCell(0).setCellValue("用户名称");
            headerRow.createCell(1).setCellValue("电压合格率");
            headerRow.createCell(2).setCellValue("电压越上限率");
            headerRow.createCell(3).setCellValue("电压越下限率");
            headerRow.createCell(4).setCellValue("最高电压(V)");
            headerRow.createCell(5).setCellValue("最低电压(V)");
            
            // 创建用户详情数据
            Row userARow = sheet.createRow(11);
            userARow.createCell(0).setCellValue("用户A");
            userARow.createCell(1).setCellValue(0.3333); // 33.33%
            userARow.createCell(2).setCellValue(0.3333); // 33.33%
            userARow.createCell(3).setCellValue(0.3333); // 33.33%
            userARow.createCell(4).setCellValue(245.0);
            userARow.createCell(5).setCellValue(195.0);
            
            Row userBRow = sheet.createRow(12);
            userBRow.createCell(0).setCellValue("用户B");
            userBRow.createCell(1).setCellValue(1.0); // 100%
            userBRow.createCell(2).setCellValue(0.0); // 0%
            userBRow.createCell(3).setCellValue(0.0); // 0%
            userBRow.createCell(4).setCellValue(230.0);
            userBRow.createCell(5).setCellValue(215.0);
            
            Row userCRow = sheet.createRow(13);
            userCRow.createCell(0).setCellValue("用户C");
            userCRow.createCell(1).setCellValue(0.3333); // 33.33%
            userCRow.createCell(2).setCellValue(0.3333); // 33.33%
            userCRow.createCell(3).setCellValue(0.3333); // 33.33%
            userCRow.createCell(4).setCellValue(250.0);
            userCRow.createCell(5).setCellValue(190.0);
            
            // 写入文件
            try (FileOutputStream fos = new FileOutputStream(reportFile)) {
                workbook.write(fos);
            }
        }
    }

    @Test
    public void testGenerateVoltageReport_Success() {
        // 执行测试
        VoltageQualityReport.generateVoltageReport(testInputFile.getAbsolutePath());
        
        // 验证输出文件是否生成
        File outputFile = new File(testInputFile.getParent(), "电压合格率报表.xlsx");
        assertTrue("报表文件应该被生成", outputFile.exists());
        
        // 验证文件内容
        try (FileInputStream fis = new FileInputStream(outputFile);
             Workbook workbook = WorkbookFactory.create(fis)) {
            
            Sheet sheet = workbook.getSheet("电压合格率报表");
            assertNotNull("应该包含电压合格率报表工作表", sheet);
            
            // 验证标题
            Row titleRow = sheet.getRow(0);
            assertNotNull(titleRow);
            assertEquals("电压合格率报表", titleRow.getCell(0).getStringCellValue());
            
            // 验证用户详情表头
            Row headerRow = sheet.getRow(10);
            assertNotNull(headerRow);
            assertEquals("用户名称", headerRow.getCell(0).getStringCellValue());
            assertEquals("电压合格率", headerRow.getCell(1).getStringCellValue());
            
        } catch (IOException e) {
            fail("读取生成的报表文件失败: " + e.getMessage());
        }
    }

    @Test
    public void testGenerateVoltageReport_FileNotExists() {
        // 执行测试 - 输入文件不存在
        VoltageQualityReport.generateVoltageReport("/non/existing/file.xlsx");
        
        // 验证不会生成输出文件
        File outputFile = new File("/non/existing", "电压合格率报表.xlsx");
        assertFalse("不应该生成报表文件", outputFile.exists());
    }

    @Test
    public void testReadFromExcel_Success() {
        // 执行测试
        CourtsStatistics result = VoltageQualityReport.readFromExcel(testReportFile);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(Integer.valueOf(3), result.getTotalUsers());
        assertEquals(66.67, result.getQualifiedRate(), 0.01);
        assertEquals(22.22, result.getAboveMaxRate(), 0.01);
        assertEquals(11.11, result.getBelowMinRate(), 0.01);
        assertEquals(250.0, result.getMaxVoltage(), 0.01);
        assertEquals(190.0, result.getMinVoltage(), 0.01);
    }

    @Test
    public void testReadFromExcel_FileNotExists() {
        // 执行测试 - 文件不存在
        CourtsStatistics result = VoltageQualityReport.readFromExcel(new File("/non/existing/file.xlsx"));
        
        // 验证结果
        assertNull(result);
    }

    @Test
    public void testReadFromExcel_NullFile() {
        // 执行测试 - null文件
        CourtsStatistics result = VoltageQualityReport.readFromExcel(null);
        
        // 验证结果
        assertNull(result);
    }

    @Test
    public void testReadUserStatisticsFromReport_Success() {
        // 执行测试 - 无关键字，无分页
        List<UserStatistics> result = VoltageQualityReport.readUserStatisticsFromReport(
                testReportFile, null, null, null);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // 验证第一个用户
        UserStatistics userA = result.get(0);
        assertEquals("用户A", userA.getUserName());
        assertEquals(33.33, userA.getQualifiedRate(), 0.01);
        assertEquals(33.33, userA.getAboveMaxRate(), 0.01);
        assertEquals(33.33, userA.getBelowMinRate(), 0.01);
        assertEquals(245.0, userA.getMaxVoltage(), 0.01);
        assertEquals(195.0, userA.getMinVoltage(), 0.01);
    }

    @Test
    public void testReadUserStatisticsFromReport_WithKeyword() {
        // 执行测试 - 使用关键字过滤
        List<UserStatistics> result = VoltageQualityReport.readUserStatisticsFromReport(
                testReportFile, "用户A", null, null);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("用户A", result.get(0).getUserName());
    }

    @Test
    public void testReadUserStatisticsFromReport_WithPagination() {
        // 执行测试 - 使用分页
        List<UserStatistics> result = VoltageQualityReport.readUserStatisticsFromReport(
                testReportFile, null, 1, 2);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("用户A", result.get(0).getUserName());
        assertEquals("用户B", result.get(1).getUserName());
        
        // 测试第二页
        List<UserStatistics> page2 = VoltageQualityReport.readUserStatisticsFromReport(
                testReportFile, null, 2, 2);
        
        assertNotNull(page2);
        assertEquals(1, page2.size());
        assertEquals("用户C", page2.get(0).getUserName());
    }

    @Test
    public void testReadUserStatisticsFromReport_FileNotExists() {
        // 执行测试 - 文件不存在
        List<UserStatistics> result = VoltageQualityReport.readUserStatisticsFromReport(
                new File("/non/existing/file.xlsx"), null, null, null);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testReadUserStatisticsFromReport_NullFile() {
        // 执行测试 - null文件
        List<UserStatistics> result = VoltageQualityReport.readUserStatisticsFromReport(
                null, null, null, null);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetUserStatisticsCount_Success() {
        // 执行测试 - 无关键字
        int count = VoltageQualityReport.getUserStatisticsCount(testReportFile, null);
        
        // 验证结果
        assertEquals(3, count);
    }

    @Test
    public void testGetUserStatisticsCount_WithKeyword() {
        // 执行测试 - 使用关键字过滤
        int count = VoltageQualityReport.getUserStatisticsCount(testReportFile, "用户A");
        
        // 验证结果
        assertEquals(1, count);
    }

    @Test
    public void testGetUserStatisticsCount_NoMatch() {
        // 执行测试 - 无匹配的关键字
        int count = VoltageQualityReport.getUserStatisticsCount(testReportFile, "不存在的用户");
        
        // 验证结果
        assertEquals(0, count);
    }

    @Test
    public void testGetUserStatisticsCount_FileNotExists() {
        // 执行测试 - 文件不存在
        int count = VoltageQualityReport.getUserStatisticsCount(
                new File("/non/existing/file.xlsx"), null);
        
        // 验证结果
        assertEquals(0, count);
    }

    @Test
    public void testGetUserStatisticsCount_NullFile() {
        // 执行测试 - null文件
        int count = VoltageQualityReport.getUserStatisticsCount(null, null);

        // 验证结果
        assertEquals(0, count);
    }

    @Test
    public void testGenerateVoltageReport_EmptyInputFile() throws IOException {
        // 创建空的输入文件
        File emptyInputFile = tempFolder.newFile("empty_input.xlsx");
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("用户电压");
            // 只创建表头，没有数据
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("用户名称");
            headerRow.createCell(1).setCellValue("u1");

            try (FileOutputStream fos = new FileOutputStream(emptyInputFile)) {
                workbook.write(fos);
            }
        }

        // 执行测试
        VoltageQualityReport.generateVoltageReport(emptyInputFile.getAbsolutePath());

        // 验证输出文件是否生成
        File outputFile = new File(emptyInputFile.getParent(), "电压合格率报表.xlsx");
        assertTrue("即使输入为空，报表文件也应该被生成", outputFile.exists());
    }

    @Test
    public void testGenerateVoltageReport_MissingSheet() throws IOException {
        // 创建不包含"用户电压"工作表的文件
        File noSheetFile = tempFolder.newFile("no_sheet.xlsx");
        try (Workbook workbook = new XSSFWorkbook()) {
            workbook.createSheet("其他工作表");

            try (FileOutputStream fos = new FileOutputStream(noSheetFile)) {
                workbook.write(fos);
            }
        }

        // 执行测试
        VoltageQualityReport.generateVoltageReport(noSheetFile.getAbsolutePath());

        // 验证输出文件是否生成（应该生成空报表）
        File outputFile = new File(noSheetFile.getParent(), "电压合格率报表.xlsx");
        assertTrue("即使缺少工作表，报表文件也应该被生成", outputFile.exists());
    }

    @Test
    public void testGenerateVoltageReport_MissingColumns() throws IOException {
        // 创建缺少必要列的输入文件
        File missingColFile = tempFolder.newFile("missing_col.xlsx");
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("用户电压");

            // 创建表头，但缺少"u1"列
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("用户名称");
            headerRow.createCell(1).setCellValue("其他列");

            Row dataRow = sheet.createRow(1);
            dataRow.createCell(0).setCellValue("用户A");
            dataRow.createCell(1).setCellValue("其他数据");

            try (FileOutputStream fos = new FileOutputStream(missingColFile)) {
                workbook.write(fos);
            }
        }

        // 执行测试 - 应该抛出异常
        try {
            VoltageQualityReport.generateVoltageReport(missingColFile.getAbsolutePath());
            fail("应该抛出IllegalArgumentException");
        } catch (IllegalArgumentException e) {
            assertTrue(e.getMessage().contains("not found"));
        }
    }

    @Test
    public void testGenerateVoltageReport_WithNullVoltageValues() throws IOException {
        // 创建包含null电压值的输入文件
        File nullValueFile = tempFolder.newFile("null_values.xlsx");
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("用户电压");

            // 创建表头
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("用户名称");
            headerRow.createCell(1).setCellValue("u1");
            headerRow.createCell(2).setCellValue("u2");

            // 创建包含空值的数据行
            Row row1 = sheet.createRow(1);
            row1.createCell(0).setCellValue("用户A");
            row1.createCell(1).setCellValue(220.5);
            // u2列为空

            Row row2 = sheet.createRow(2);
            row2.createCell(0).setCellValue("用户B");
            // u1和u2列都为空

            try (FileOutputStream fos = new FileOutputStream(nullValueFile)) {
                workbook.write(fos);
            }
        }

        // 执行测试
        VoltageQualityReport.generateVoltageReport(nullValueFile.getAbsolutePath());

        // 验证输出文件是否生成
        File outputFile = new File(nullValueFile.getParent(), "电压合格率报表.xlsx");
        assertTrue("包含null值时，报表文件也应该被生成", outputFile.exists());

        // 验证报表内容
        try (FileInputStream fis = new FileInputStream(outputFile);
             Workbook workbook = WorkbookFactory.create(fis)) {

            Sheet sheet = workbook.getSheet("电压合格率报表");
            assertNotNull(sheet);

            // 验证只有用户A被包含（因为用户B没有有效电压值）
            Row userARow = sheet.getRow(11);
            assertNotNull(userARow);
            assertEquals("用户A", userARow.getCell(0).getStringCellValue());

        } catch (IOException e) {
            fail("读取生成的报表文件失败: " + e.getMessage());
        }
    }

    @Test
    public void testReadFromExcel_CorruptedFile() throws IOException {
        // 创建损坏的Excel文件（实际上是文本文件）
        File corruptedFile = tempFolder.newFile("corrupted.xlsx");
        try (PrintWriter writer = new PrintWriter(new FileWriter(corruptedFile))) {
            writer.println("这不是一个有效的Excel文件");
        }

        // 执行测试
        CourtsStatistics result = VoltageQualityReport.readFromExcel(corruptedFile);

        // 验证结果
        assertNull("损坏的文件应该返回null", result);
    }

    @Test
    public void testReadFromExcel_MissingSheet() throws IOException {
        // 创建不包含"电压合格率报表"工作表的文件
        File noReportSheetFile = tempFolder.newFile("no_report_sheet.xlsx");
        try (Workbook workbook = new XSSFWorkbook()) {
            workbook.createSheet("其他工作表");

            try (FileOutputStream fos = new FileOutputStream(noReportSheetFile)) {
                workbook.write(fos);
            }
        }

        // 执行测试
        CourtsStatistics result = VoltageQualityReport.readFromExcel(noReportSheetFile);

        // 验证结果
        assertNull("缺少报表工作表时应该返回null", result);
    }

    @Test
    public void testReadUserStatisticsFromReport_MissingSheet() throws IOException {
        // 创建不包含"电压合格率报表"工作表的文件
        File noSheetFile = tempFolder.newFile("no_sheet_user.xlsx");
        try (Workbook workbook = new XSSFWorkbook()) {
            workbook.createSheet("其他工作表");

            try (FileOutputStream fos = new FileOutputStream(noSheetFile)) {
                workbook.write(fos);
            }
        }

        // 执行测试
        List<UserStatistics> result = VoltageQualityReport.readUserStatisticsFromReport(
                noSheetFile, null, null, null);

        // 验证结果
        assertNotNull(result);
        assertTrue("缺少工作表时应该返回空列表", result.isEmpty());
    }

    @Test
    public void testReadUserStatisticsFromReport_EmptyKeyword() {
        // 执行测试 - 空关键字
        List<UserStatistics> result = VoltageQualityReport.readUserStatisticsFromReport(
                testReportFile, "", null, null);

        // 验证结果 - 空关键字应该返回所有用户
        assertNotNull(result);
        assertEquals(3, result.size());
    }

    @Test
    public void testReadUserStatisticsFromReport_InvalidPagination() {
        // 执行测试 - 无效的分页参数
        List<UserStatistics> result1 = VoltageQualityReport.readUserStatisticsFromReport(
                testReportFile, null, 0, 2); // pageNum为0

        // 验证结果 - 应该返回所有数据
        assertNotNull(result1);
        assertEquals(3, result1.size());

        // 执行测试 - 负数分页参数
        List<UserStatistics> result2 = VoltageQualityReport.readUserStatisticsFromReport(
                testReportFile, null, -1, -1);

        // 验证结果 - 应该返回所有数据
        assertNotNull(result2);
        assertEquals(3, result2.size());
    }

    @Test
    public void testGetUserStatisticsCount_EmptyKeyword() {
        // 执行测试 - 空关键字
        int count = VoltageQualityReport.getUserStatisticsCount(testReportFile, "");

        // 验证结果 - 空关键字应该返回所有用户数量
        assertEquals(3, count);
    }

    @Test
    public void testGetUserStatisticsCount_WhitespaceKeyword() {
        // 执行测试 - 只包含空格的关键字
        int count = VoltageQualityReport.getUserStatisticsCount(testReportFile, "   ");

        // 验证结果 - 应该返回所有用户数量
        assertEquals(3, count);
    }

    @Test
    public void testVoltageRecord_InnerClass() {
        // 测试VoltageRecord内部类
        List<Double> voltages = Arrays.asList(220.0, 230.0, 240.0);
        VoltageQualityReport.VoltageRecord record =
                new VoltageQualityReport.VoltageRecord("测试用户", voltages);

        // 验证结果
        assertEquals("测试用户", record.userName);
        assertEquals(voltages, record.voltageValues);
        assertEquals(3, record.voltageValues.size());
    }

    // ========== 优化功能测试 ==========

    @Test
    public void testOptimizedDataProcessor() {
        // 测试优化的数据处理器
        VoltageDataProcessor processor = new VoltageDataProcessor();

        // 第一次读取（无缓存）
        List<VoltageDataProcessor.VoltageRecord> records1 =
            processor.readVoltageDataOptimized(testInputFile.getAbsolutePath());

        assertNotNull("记录列表不应为空", records1);
        assertTrue("应该读取到数据", records1.size() > 0);

        // 第二次读取（有缓存）
        List<VoltageDataProcessor.VoltageRecord> records2 =
            processor.readVoltageDataOptimized(testInputFile.getAbsolutePath());

        assertNotNull("第二次读取结果不应为空", records2);
        assertEquals("两次读取结果应该相同", records1.size(), records2.size());

        // 验证缓存效果
        ExcelCacheManager.CacheStats stats = ExcelCacheManager.getInstance().getStats();
        assertTrue("应该有缓存命中", stats.hits > 0);
    }

    @Test
    public void testVoltageStatisticsCalculator() {
        VoltageStatisticsCalculator calculator = new VoltageStatisticsCalculator();

        // 添加测试数据
        List<Double> voltages = Arrays.asList(220.0, 200.0, 250.0, 190.0, 230.0);
        calculator.addVoltageValues(voltages);

        // 验证统计结果
        assertEquals("总读数应该正确", 5, calculator.getTotalReadings());
        assertEquals("合格读数应该正确", 3, calculator.getQualifiedReadings());
        assertEquals("超上限读数应该正确", 1, calculator.getAboveMaxReadings());
        assertEquals("低于下限读数应该正确", 1, calculator.getBelowMinReadings());

        // 验证比率计算
        assertEquals("合格率应该正确", 60.0, calculator.getQualifiedRate(), 0.01);
        assertEquals("超上限率应该正确", 20.0, calculator.getAboveMaxRate(), 0.01);
        assertEquals("低于下限率应该正确", 20.0, calculator.getBelowMinRate(), 0.01);

        // 验证最值
        assertEquals("最大值应该正确", 250.0, calculator.getMaxVoltage(), 0.01);
        assertEquals("最小值应该正确", 190.0, calculator.getMinVoltage(), 0.01);

        // 测试重置功能
        calculator.reset();
        assertEquals("重置后总读数应为0", 0, calculator.getTotalReadings());
        assertFalse("重置后应该未初始化", calculator.isInitialized());
    }

    @Test
    public void testVoltageObjectPool() {
        VoltageObjectPool objectPool = VoltageObjectPool.getInstance();

        // 测试Double列表池
        List<Double> list1 = objectPool.borrowDoubleList();
        List<Double> list2 = objectPool.borrowDoubleList();

        assertNotNull("借用的列表不应为空", list1);
        assertNotNull("借用的列表不应为空", list2);
        assertNotSame("应该是不同的对象", list1, list2);

        // 归还对象
        objectPool.returnDoubleList(list1);
        objectPool.returnDoubleList(list2);

        // 测试统计计算器池
        VoltageStatisticsCalculator calc1 = objectPool.borrowCalculator();
        VoltageStatisticsCalculator calc2 = objectPool.borrowCalculator();

        assertNotNull("借用的计算器不应为空", calc1);
        assertNotNull("借用的计算器不应为空", calc2);

        objectPool.returnCalculator(calc1);
        objectPool.returnCalculator(calc2);

        // 验证池状态
        VoltageObjectPool.PoolStats stats = objectPool.getStats();
        assertTrue("应该创建了对象", stats.doubleListCreated > 0);
        assertTrue("应该创建了计算器", stats.calculatorCreated > 0);
    }

    @Test
    public void testPerformanceMonitor() {
        VoltagePerformanceMonitor monitor = VoltagePerformanceMonitor.getInstance();

        // 测试操作计时
        try (VoltagePerformanceMonitor.PerformanceTimer timer = monitor.startOperation("testOperation")) {
            // 模拟一些工作
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            assertTrue("计时器应该记录时间", timer.getDuration() >= 0);
        }

        // 测试性能报告
        VoltagePerformanceMonitor.PerformanceReport report = monitor.getPerformanceReport();
        assertNotNull("性能报告不应为空", report);

        // 测试便捷方法
        String result = VoltagePerformanceMonitor.timeOperation("testSupplier", () -> "test result");
        assertEquals("便捷方法应该返回正确结果", "test result", result);

        VoltagePerformanceMonitor.timeOperation("testRunnable", () -> {
            // 模拟工作
        });

        // 验证操作被记录
        VoltagePerformanceMonitor.PerformanceReport finalReport = monitor.getPerformanceReport();
        assertTrue("应该记录了操作", finalReport.operationStats.size() > 0);
    }

    @Test
    public void testCacheManagerExtensions() {
        ExcelCacheManager cacheManager = ExcelCacheManager.getInstance();
        String testFilePath = testInputFile.getAbsolutePath();

        // 测试电压数据缓存
        List<String> testData = Arrays.asList("test1", "test2");
        ExcelCacheManager.CacheOperations.cacheVoltageData(testFilePath, testData);

        List<?> cachedData = ExcelCacheManager.CacheOperations.getVoltageData(testFilePath);
        assertNotNull("缓存的数据不应为空", cachedData);
        assertEquals("缓存的数据应该正确", 2, cachedData.size());

        // 测试用户统计缓存
        List<UserStatistics> userStats = Arrays.asList(
            UserStatistics.builder().userName("用户A").qualifiedRate(80.0).build(),
            UserStatistics.builder().userName("用户B").qualifiedRate(90.0).build()
        );
        ExcelCacheManager.CacheOperations.cacheUserStats(testFilePath, userStats);

        List<UserStatistics> cachedUserStats = ExcelCacheManager.CacheOperations.getUserStats(testFilePath);
        assertNotNull("缓存的用户统计不应为空", cachedUserStats);
        assertEquals("缓存的用户统计应该正确", 2, cachedUserStats.size());

        // 测试整体统计缓存
        CourtsStatistics overallStats = CourtsStatistics.builder()
            .totalUsers(2)
            .qualifiedRate(85.0)
            .build();
        ExcelCacheManager.CacheOperations.cacheOverallStats(testFilePath, overallStats);

        CourtsStatistics cachedOverallStats = ExcelCacheManager.CacheOperations.getOverallStats(testFilePath);
        assertNotNull("缓存的整体统计不应为空", cachedOverallStats);
        assertEquals("缓存的整体统计应该正确", Integer.valueOf(2), cachedOverallStats.getTotalUsers());

        // 测试缓存清理
        ExcelCacheManager.CacheOperations.clearFileCache(testFilePath);

        // 验证缓存已清理
        List<?> clearedData = ExcelCacheManager.CacheOperations.getVoltageData(testFilePath);
        assertNull("清理后缓存应为空", clearedData);
    }
}
