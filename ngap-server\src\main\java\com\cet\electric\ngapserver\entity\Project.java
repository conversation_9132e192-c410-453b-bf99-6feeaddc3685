package com.cet.electric.ngapserver.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 项目实体类
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Project {
    /**
     * 项目ID：主键，自增
     */
    private Long projectId;

    /**
     * 项目名称：非空
     */
    private String projectName;

    /**
     * 项目类型：通用或电压治理
     */
    private String projectType;

    /**
     * 创建时间：默认当前时间
     */
    private Long createdAt;

    /**
     * 修改时间：自动更新
     */
    private Long updatedAt;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer isDeleted;
}