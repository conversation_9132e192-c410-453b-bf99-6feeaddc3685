package com.cet.electric.ngapserver.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 指标数据实体类
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetricData {

    /**
     * 指标
     */
    private Metric metric;

    /**
     * 指标x轴单位
     */
    private String xUnit;

    /**
     * 指标y轴单位
     */
    private String yUnit;

    /**
     * 数据点列表
     */
    private List<DataPoint> dataPoints;

    /**
     * 数据的统计信息
     */
    private MetricStatistics statistics;
}
