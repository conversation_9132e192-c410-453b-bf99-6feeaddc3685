export default {
  seriesCnt: "5",
  backgroundColor: "rgba(0,0,0,0)",
  titleColor: "#343a40",
  subtitleColor: "#774477",
  textColorShow: false,
  textColor: "#333",
  markTextColor: "#ffffff",
  color: [
    "#5664d2",
    "#4aa3ff",
    "#1cbb8c",
    "#fb923c",
    "#f43f5e",
    "#14b8a6",
    "#d946ef",
    "#facc15",
    "#a3e635",
    "#22d3ee",
    "#818cf8",
    "#1720d1",
    "#2498d1",
    "#bbbde6",
    "#4045b2",
    "#21a97a",
    "#ff745a",
    "#007e99",
    "#ffa8a8",
    "#ffc328"
  ],
  borderColor: "#ccc",
  borderWidth: "0",
  visualMapColor: ["#e6450f", "#ff8c00", "#ffcb33"],
  radar: {
    itemStyle: {
      borderWidth: 1
    },
    lineStyle: {
      width: 2
    },
    symbolSize: 4,
    symbol: "emptyCircle",
    smooth: false
  },
  legendTextColor: "#74788d",
  kColor: "#ff3d60",
  kColor0: "#1cbb8c",
  kBorderColor: "#ff3d60",
  kBorderColor0: "#1cbb8c",
  kBorderWidth: 1,
  lineWidth: 2,
  symbolSize: 3,
  symbol: "emptyCircle",
  symbolBorderWidth: 1,
  lineSmooth: true,
  graphLineWidth: 1,
  graphLineColor: "#cccccc",
  mapLabelColor: "#d87a80",
  mapLabelColorE: "rgb(100,0,0)",
  mapBorderColor: "#eeeeee",
  mapBorderColorE: "#444",
  mapBorderWidth: 0.5,
  mapBorderWidthE: 1,
  mapAreaColor: "#dddddd",
  mapAreaColorE: "rgba(254,153,78,1)",
  axes: [
    {
      type: "all",
      name: "通用坐标轴",
      axisLineShow: true,
      axisLineColor: "#eeeeee",
      axisTickShow: true,
      axisTickColor: "#eeeeee",
      axisLabelShow: true,
      axisLabelColor: "#eeeeee",
      splitLineShow: true,
      splitLineColor: ["#aaaaaa"],
      splitAreaShow: false,
      splitAreaColor: ["#eeeeee"]
    },
    {
      type: "category",
      name: "类目坐标轴",
      axisLineShow: true,
      axisLineColor: "#cccccc",
      axisTickShow: true,
      axisTickColor: "#adb5bd",
      axisLabelShow: true,
      axisLabelColor: "#343a40",
      splitLineShow: false,
      splitLineColor: ["#eee"],
      splitAreaShow: false,
      splitAreaColor: ["rgba(250,250,250,0.3)", "rgba(200,200,200,0.3)"]
    },
    {
      type: "value",
      name: "数值坐标轴",
      axisLineShow: true,
      axisLineColor: "#cccccc",
      axisTickShow: true,
      axisTickColor: "#cccccc",
      axisLabelShow: true,
      axisLabelColor: "#74788d",
      splitLineShow: true,
      splitLineColor: ["#eee"],
      splitAreaShow: false,
      splitAreaColor: ["rgba(250,250,250,0.3)", "rgba(200,200,200,0.3)"]
    },
    {
      type: "log",
      name: "对数坐标轴",
      axisLineShow: true,
      axisLineColor: "#cccccc",
      axisTickShow: true,
      axisTickColor: "#cccccc",
      axisLabelShow: true,
      axisLabelColor: "#74788d",
      splitLineShow: true,
      splitLineColor: ["#eee"],
      splitAreaShow: false,
      splitAreaColor: ["rgba(250,250,250,0.3)", "rgba(200,200,200,0.3)"]
    },
    {
      type: "time",
      name: "时间坐标轴",
      axisLineShow: true,
      axisLineColor: "#cccccc",
      axisTickShow: true,
      axisTickColor: "#cccccc",
      axisLabelShow: true,
      axisLabelColor: "#74788d",
      splitLineShow: true,
      splitLineColor: ["#eee"],
      splitAreaShow: false,
      splitAreaColor: ["rgba(250,250,250,0.3)", "rgba(200,200,200,0.3)"]
    }
  ],
  axisSeperateSetting: true,
  toolboxColor: "#74788d",
  toolboxEmphasisColor: "#556666",
  tooltipAxisColor: "#6f7bd9",
  tooltipAxisWidth: "1",
  timelineLineColor: "#adb5bd",
  timelineLineWidth: 1,
  timelineItemColor: "#6f7bd9",
  timelineItemColorE: "#5664d2",
  timelineCheckColor: "#5664d2",
  timelineCheckBorderColor: "#5664d2",
  timelineItemBorderWidth: 1,
  timelineControlColor: "#6f7bd9",
  timelineControlBorderColor: "#6f7bd9",
  timelineControlBorderWidth: "0.5",
  timelineLabelColor: "#ffffff",
  datazoomBackgroundColor: "rgba(47,69,84,0)",
  datazoomDataColor: "#efefff",
  datazoomFillColor: "rgba(182,162,222,0.2)",
  datazoomHandleColor: "#008acd",
  datazoomHandleWidth: "100",
  datazoomLabelColor: "#333333",
  toolbox: {
    feature: {
      saveAsImage: {
        backgroundColor: "#ffffff"
      }
    }
  },
  bar: {
    label: {
      color: "#021325",
      textBorderWidth: "0"
    },
    barMaxWidth: 30
  }
};
