package com.cet.electric.ngapserver.util.voltage;

import com.cet.electric.ngapserver.entity.CourtsStatistics;
import com.cet.electric.ngapserver.entity.UserStatistics;
import com.cet.electric.ngapserver.util.excel.ExcelCacheManager;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.poi.util.IOUtils;

/**
 * 电压数据处理器 - 高性能版本
 * 实现流式处理、缓存优化和批量计算
 * 
 * <AUTHOR>
 */
public class VoltageDataProcessor {
    
    private static final Logger log = LoggerFactory.getLogger(VoltageDataProcessor.class);
    
    // 电压合格范围
    private static final double MIN_VOLTAGE = 198.0;
    private static final double MAX_VOLTAGE = 242.0;
    
    // 批处理大小
    private static final int BATCH_SIZE = 1000;

    // 并行处理阈值（根据CPU核心数动态调整）
    private static final int PARALLEL_THRESHOLD = Math.max(50, Runtime.getRuntime().availableProcessors() * 10);
    
    // 缓存管理器
    private final ExcelCacheManager cacheManager;
    
    // 对象池
    private final VoltageObjectPool objectPool;
    
    public VoltageDataProcessor() {
        this.cacheManager = ExcelCacheManager.getInstance();
        this.objectPool = VoltageObjectPool.getInstance();
    }
    
    /**
     * 高性能读取电压数据
     * 支持缓存和流式处理，针对大文件优化
     */
    public List<VoltageRecord> readVoltageDataOptimized(String filePath) {
        // 检查缓存
        String cacheKey = ExcelCacheManager.getVoltageDataKey(filePath);
        List<VoltageRecord> cachedData = cacheManager.get(cacheKey, List.class);
        if (cachedData != null) {
            log.debug("从缓存获取电压数据: {}", filePath);
            return cachedData;
        }

        List<VoltageRecord> records = new ArrayList<>();

        // 检查文件大小，决定使用哪种读取方式
        java.io.File file = new java.io.File(filePath);
        long fileSize = file.length();

        if (fileSize > 50 * 1024 * 1024) { // 50MB以上使用流式读取
            log.info("检测到大文件({}MB)，使用流式读取模式", String.format("%.2f", fileSize / 1024.0 / 1024.0));
            records = readLargeFileWithStreaming(filePath);
        } else {
            // 小文件使用传统方式，但增加POI限制处理
            records = readSmallFileWithWorkbook(filePath);
        }

        // 缓存结果
        if (!records.isEmpty()) {
            cacheManager.put(cacheKey, records);
            log.debug("电压数据已缓存: {}", filePath);
        }

        return records;
    }

    /**
     * 使用增强的内存限制处理大文件
     */
    private List<VoltageRecord> readLargeFileWithStreaming(String filePath) {
        List<VoltageRecord> records = new ArrayList<>();

        try {
            // 大幅增加POI的内存限制
            IOUtils.setByteArrayMaxOverride(500_000_000); // 500MB

            log.info("尝试使用增强内存限制读取大文件: {}", filePath);

            try (FileInputStream fis = new FileInputStream(filePath);
                 Workbook workbook = WorkbookFactory.create(fis)) {

                Sheet sheet = workbook.getSheet("用户电压");
                if (sheet == null) {
                    log.warn("Excel文件中未找到'用户电压'工作表: {}", filePath);
                    return records;
                }

                log.info("成功打开工作表，开始处理数据...");

                // 获取列索引
                Map<String, Integer> columnIndex = buildColumnIndexForLargeFile(sheet);

                // 分批处理数据行，减少内存压力
                records = processLargeFileInSmallBatches(sheet, columnIndex);

                log.info("大文件处理完成，共读取 {} 条记录", records.size());

            }
        } catch (IllegalArgumentException e) {
            // 列不存在的错误应该直接抛出
            throw e;
        } catch (Exception e) {
            log.error("增强内存限制读取大文件失败: {}", filePath, e);

            // 如果还是失败，尝试更激进的内存设置
            try {
                IOUtils.setByteArrayMaxOverride(1_000_000_000); // 1GB
                log.warn("尝试使用最大内存限制重新读取文件: {}", filePath);

                try (FileInputStream fis = new FileInputStream(filePath);
                     Workbook workbook = WorkbookFactory.create(fis)) {

                    Sheet sheet = workbook.getSheet("用户电压");
                    if (sheet != null) {
                        Map<String, Integer> columnIndex = buildColumnIndexForLargeFile(sheet);
                        records = processLargeFileInSmallBatches(sheet, columnIndex);
                        log.info("使用最大内存限制成功处理文件，共读取 {} 条记录", records.size());
                    }
                }
            } catch (IllegalArgumentException e2) {
                // 列不存在的错误应该直接抛出
                throw e2;
            } catch (Exception e2) {
                log.error("所有尝试都失败了: {}", filePath, e2);
            }
        }

        return records;
    }

    /**
     * 为大文件构建列索引（避免重复访问）
     */
    private Map<String, Integer> buildColumnIndexForLargeFile(Sheet sheet) {
        Map<String, Integer> columnIndex = new HashMap<>();
        Row headerRow = sheet.getRow(0);

        if (headerRow != null) {
            for (int i = 0; i < headerRow.getPhysicalNumberOfCells(); i++) {
                Cell cell = headerRow.getCell(i);
                if (cell != null && cell.getCellType() == CellType.STRING) {
                    String columnName = cell.getStringCellValue();
                    columnIndex.put(columnName, i);
                }
            }
        }

        return columnIndex;
    }

    /**
     * 分小批次处理大文件，减少内存压力
     */
    private List<VoltageRecord> processLargeFileInSmallBatches(Sheet sheet, Map<String, Integer> columnIndex) {
        List<VoltageRecord> allRecords = new ArrayList<>();
        int rowCount = sheet.getPhysicalNumberOfRows();

        Integer userNameIndex = columnIndex.get("用户名称");
        Integer uIndex = columnIndex.get("u1");

        if (userNameIndex == null) {
            throw new IllegalArgumentException("Column with name '用户名称' not found.");
        }
        if (uIndex == null) {
            throw new IllegalArgumentException("Column with name 'u1' not found.");
        }

        // 使用更小的批次大小处理大文件
        int smallBatchSize = 100; // 减小批次大小

        for (int startRow = 1; startRow < rowCount; startRow += smallBatchSize) {
            int endRow = Math.min(startRow + smallBatchSize, rowCount);

            log.debug("处理批次: {} - {}", startRow, endRow);

            List<VoltageRecord> batchRecords = processBatch(sheet, startRow, endRow, userNameIndex, uIndex);
            allRecords.addAll(batchRecords);

            // 强制垃圾回收，释放内存
            if (startRow % (smallBatchSize * 10) == 1) {
                System.gc();
                log.debug("已处理 {} 行，执行垃圾回收", startRow);
            }
        }

        return allRecords;
    }

    /**
     * 使用传统Workbook方式读取小文件
     */
    private List<VoltageRecord> readSmallFileWithWorkbook(String filePath) {
        List<VoltageRecord> records = new ArrayList<>();

        try {
            // 增加POI的内存限制
            IOUtils.setByteArrayMaxOverride(150_000_000); // 150MB

            try (FileInputStream fis = new FileInputStream(filePath);
                 Workbook workbook = WorkbookFactory.create(fis)) {

                Sheet sheet = workbook.getSheet("用户电压");
                if (sheet == null) {
                    log.warn("Excel文件中未找到'用户电压'工作表: {}", filePath);
                    return records;
                }

                // 获取或缓存列索引
                Map<String, Integer> columnIndex = getColumnIndexCached(sheet, filePath);

                // 流式处理数据行
                records = processRowsInBatches(sheet, columnIndex);

            }
        } catch (IllegalArgumentException e) {
            // 列不存在的错误应该直接抛出，不尝试其他方法
            throw e;
        } catch (Exception e) {
            log.error("读取小文件失败: {}", filePath, e);
            // 如果传统方式失败，尝试流式读取
            log.info("传统方式失败，尝试流式读取: {}", filePath);
            records = readLargeFileWithStreaming(filePath);
        }

        return records;
    }
    
    /**
     * 获取或缓存列索引
     */
    private Map<String, Integer> getColumnIndexCached(Sheet sheet, String filePath) {
        String indexCacheKey = ExcelCacheManager.getColumnIndexKey(filePath, "用户电压");
        Map<String, Integer> columnIndex = cacheManager.get(indexCacheKey, Map.class);
        
        if (columnIndex == null) {
            columnIndex = buildColumnIndex(sheet);
            cacheManager.put(indexCacheKey, columnIndex);
            log.debug("列索引已缓存: {}", filePath);
        }
        
        return columnIndex;
    }
    
    /**
     * 构建列索引映射
     */
    private Map<String, Integer> buildColumnIndex(Sheet sheet) {
        Map<String, Integer> columnIndex = new HashMap<>();
        Row headerRow = sheet.getRow(0);
        
        if (headerRow != null) {
            for (int i = 0; i < headerRow.getPhysicalNumberOfCells(); i++) {
                Cell cell = headerRow.getCell(i);
                if (cell != null && cell.getCellType() == CellType.STRING) {
                    String columnName = cell.getStringCellValue();
                    columnIndex.put(columnName, i);
                }
            }
        }
        
        return columnIndex;
    }
    
    /**
     * 批量处理数据行
     */
    private List<VoltageRecord> processRowsInBatches(Sheet sheet, Map<String, Integer> columnIndex) {
        List<VoltageRecord> allRecords = new ArrayList<>();
        int rowCount = sheet.getPhysicalNumberOfRows();
        
        Integer userNameIndex = columnIndex.get("用户名称");
        Integer uIndex = columnIndex.get("u1");
        
        if (userNameIndex == null) {
            throw new IllegalArgumentException("Column with name '用户名称' not found.");
        }
        if (uIndex == null) {
            throw new IllegalArgumentException("Column with name 'u1' not found.");
        }
        
        // 分批处理行数据
        for (int startRow = 1; startRow < rowCount; startRow += BATCH_SIZE) {
            int endRow = Math.min(startRow + BATCH_SIZE, rowCount);
            List<VoltageRecord> batchRecords = processBatch(sheet, startRow, endRow, userNameIndex, uIndex);
            allRecords.addAll(batchRecords);
        }
        
        return allRecords;
    }
    
    /**
     * 处理单个批次的数据
     */
    private List<VoltageRecord> processBatch(Sheet sheet, int startRow, int endRow, 
                                           int userNameIndex, int uIndex) {
        List<VoltageRecord> batchRecords = new ArrayList<>();
        
        for (int i = startRow; i < endRow; i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;
            
            VoltageRecord record = processRow(row, userNameIndex, uIndex);
            if (record != null) {
                batchRecords.add(record);
            }
        }
        
        return batchRecords;
    }
    
    /**
     * 处理单行数据
     */
    private VoltageRecord processRow(Row row, int userNameIndex, int uIndex) {
        String userName = getCellValueAsString(row.getCell(userNameIndex));
        if (userName == null || userName.trim().isEmpty()) {
            return null;
        }
        
        // 使用对象池获取电压值列表
        List<Double> voltageValues = objectPool.borrowDoubleList();
        boolean hasValidVoltage = false;
        
        try {
            // 读取u1到u96的电压值
            for (int j = uIndex; j < uIndex + 96 && j < row.getLastCellNum(); j++) {
                Cell cell = row.getCell(j);
                Double voltage = extractVoltageValue(cell);
                
                if (voltage != null) {
                    voltageValues.add(voltage);
                    hasValidVoltage = true;
                } else {
                    voltageValues.add(null);
                }
            }
            
            if (!hasValidVoltage) {
                return null;
            }
            
            // 创建记录（复制数据以避免对象池影响）
            return new VoltageRecord(userName, new ArrayList<>(voltageValues));
            
        } finally {
            // 归还对象到池中
            objectPool.returnDoubleList(voltageValues);
        }
    }
    
    /**
     * 提取电压值
     */
    private Double extractVoltageValue(Cell cell) {
        if (cell == null) return null;

        switch (cell.getCellType()) {
            case NUMERIC:
                try {
                    return cell.getNumericCellValue();
                } catch (Exception e) {
                    log.debug("提取数值型电压值时发生异常: 单元格位置=({},{}), 异常信息={}",
                        cell.getRowIndex(), cell.getColumnIndex(), e.getMessage());
                    return null;
                }
            case STRING:
                try {
                    return Double.parseDouble(cell.getStringCellValue());
                } catch (NumberFormatException e) {
                    log.debug("解析字符串型电压值时发生异常: 单元格位置=({},{}), 值='{}', 异常信息={}",
                        cell.getRowIndex(), cell.getColumnIndex(), cell.getStringCellValue(), e.getMessage());
                    return null;
                }
            default:
                log.debug("不支持的单元格类型: 位置=({},{}), 类型={}",
                    cell.getRowIndex(), cell.getColumnIndex(), cell.getCellType());
                return null;
        }
    }
    
    /**
     * 获取单元格字符串值
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) return null;

        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue();
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return cell.getLocalDateTimeCellValue().toString();
                    } else {
                        return String.valueOf(cell.getNumericCellValue());
                    }
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    return cell.getCellFormula();
                default:
                    return null;
            }
        } catch (Exception e) {
            log.debug("无法获取单元格值，返回null: 位置=({},{}), 类型={}, 异常信息={}",
                cell.getRowIndex(), cell.getColumnIndex(), cell.getCellType(), e.getMessage());
            return null;
        }
    }
    
    /**
     * 高性能计算用户统计
     */
    public List<UserStatistics> calculateUserStatsOptimized(Map<String, List<VoltageRecord>> userRecords) {
        // 检查缓存
        String cacheKey = generateUserStatsCacheKey(userRecords);
        List<UserStatistics> cachedStats = cacheManager.get(cacheKey, List.class);
        if (cachedStats != null) {
            log.debug("从缓存获取用户统计数据");
            return cachedStats;
        }
        
        List<UserStatistics> userStatsList = new ArrayList<>();
        
        // 并行处理用户数据（根据系统性能动态判断）
        if (userRecords.size() > PARALLEL_THRESHOLD) {
            List<UserStatistics> parallelResults = new ArrayList<>();
            userRecords.entrySet().parallelStream()
                .map(this::calculateSingleUserStats)
                .forEach(parallelResults::add);
            userStatsList = parallelResults;
        } else {
            for (Map.Entry<String, List<VoltageRecord>> entry : userRecords.entrySet()) {
                UserStatistics stats = calculateSingleUserStats(entry);
                userStatsList.add(stats);
            }
        }
        
        // 缓存结果
        cacheManager.put(cacheKey, userStatsList);
        
        return userStatsList;
    }
    
    /**
     * 计算单个用户的统计数据
     */
    private UserStatistics calculateSingleUserStats(Map.Entry<String, List<VoltageRecord>> entry) {
        String userName = entry.getKey();
        List<VoltageRecord> records = entry.getValue();
        
        // 使用对象池获取统计计算器
        VoltageStatisticsCalculator calculator = objectPool.borrowCalculator();
        
        try {
            // 批量计算统计数据
            for (VoltageRecord record : records) {
                calculator.addVoltageValues(record.voltageValues);
            }
            
            return calculator.buildUserStatistics(userName);
            
        } finally {
            // 归还计算器到对象池
            objectPool.returnCalculator(calculator);
        }
    }
    
    /**
     * 生成用户统计缓存键（优化版本）
     */
    private String generateUserStatsCacheKey(Map<String, List<VoltageRecord>> userRecords) {
        // 基于用户数量和数据特征生成更稳定的缓存键
        int userCount = userRecords.size();
        int totalRecords = userRecords.values().stream().mapToInt(List::size).sum();

        // 使用用户名排序后的字符串来生成更稳定的标识
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append("user_stats:").append(userCount).append(":").append(totalRecords).append(":");

        // 添加用户名的有序标识（避免hashCode的不稳定性）
        userRecords.keySet().stream()
            .sorted()
            .forEach(userName -> keyBuilder.append(userName.hashCode()).append(","));

        return keyBuilder.toString();
    }
    


    /**
     * 电压记录数据结构
     */
    public static class VoltageRecord {
        public final String userName;
        public final List<Double> voltageValues;

        public VoltageRecord(String userName, List<Double> voltageValues) {
            this.userName = userName;
            this.voltageValues = voltageValues;
        }
    }
}
