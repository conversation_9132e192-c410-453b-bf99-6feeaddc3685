<template>
  <div class="page">
    <cet-aside collapse>
      <template #aside>
        <CetGiantTree
          ref="metric"
          style="height: calc(100% - 10px)"
          class="ml10"
          v-bind="CetGiantTree_node"
          v-on="CetGiantTree_node.event"
          :selectNode.sync="CetGiantTree_node.selectNode"
        ></CetGiantTree>
      </template>
      <template #container>
        <el-container class="fullheight p0">
          <el-header height="25px" style="padding: 0px">
            <span class="fs14">
              仿真起始时间：
              <el-date-picker
                v-model="startTime"
                type="date"
                value-format="timestamp"
                :picker-options="pickerOptions"
                style="width: 150px"
                placeholder="选择日期"
                @change="startTimeChange"
              ></el-date-picker>
            </span>
            <span class="pl16 fs14" v-if="showRate">
              电压合格率：{{ rate }}%
            </span>
            <div class="fr">
              <el-button
                size="mini"
                type="primary"
                :loading="exportLoading"
                plain
                @click="downloadData"
              >
                导出
              </el-button>
              <el-button size="mini" type="primary" plain @click="cleanData">
                清空
              </el-button>

              <inputWrap class="fl mr8" label="展示类型">
                <el-select
                  v-model="showTableSelect"
                  style="width: 90px"
                  size="mini"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </inputWrap>
            </div>
          </el-header>
          <el-main
            style="position: relative; overflow-x: auto; white-space: nowrap"
            class="p0 pb10 pt10 fullheight"
          >
            <div
              :class="showTableSelect ? 'hide-chart' : 'trend-chart'"
              class="trend-chart"
            >
              <CetChart ref="chart" v-bind="CetChart_task"></CetChart>
            </div>

            <div v-if="showTableSelect" class="trend-table">
              <div
                v-for="(item, index) in tableData"
                :key="index"
                :style="tableDivStyle"
                class="fullheight"
              >
                <el-table
                  ref="cetTable"
                  :data="
                    item.data.slice(
                      (currentPage[index] - 1) * 100,
                      currentPage[index] * 100
                    )
                  "
                  tooltip-effect="light"
                  border
                  height="true"
                  style="height: calc(100% - 40px); width: 100%"
                >
                  <template v-for="(it, i) in item.header">
                    <el-table-column
                      :key="i"
                      :label="it.label"
                      :prop="it.prop"
                      header-align="center"
                      :width="it.width"
                      :show-overflow-tooltip="true"
                    ></el-table-column>
                  </template>
                </el-table>
                <el-pagination
                  class="mt8"
                  small
                  background
                  :current-page.sync="currentPage[index]"
                  :page-size="100"
                  layout="prev, pager, next"
                  :total="item.data.length"
                ></el-pagination>
              </div>
            </div>
          </el-main>
        </el-container>
      </template>
    </cet-aside>
  </div>
</template>

<script>
import common from "@/utils/common";
import customApi from "@/api/custom";
import { mhCONST } from "@/config/const";
import { download } from "@/utils/fetch";

export default {
  name: "ViewTaskResult",
  components: {},
  props: {
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      exportLoading: false,
      startTime: null,
      pickerOptions: {
        disabledDate(time) {
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          return time.getTime() > today.getTime(); // 禁用今天之后的日期
        }
      },
      showRate: false,
      rate: null,
      tableData: [{ data: [], header: [] }],
      currentPage: [],
      showTableSelect: false,
      header: [
        {
          prop: "timestamp",
          label: "时间",
          width: "190px"
        },
        {
          prop: "value",
          label: "指标数据"
        }
      ],
      options: [
        {
          value: false,
          label: $T("曲线")
        },
        {
          value: true,
          label: $T("表格")
        }
      ],
      // time组件
      CetDateSelect_time: {
        value: {
          dateType: "3",
          value: new Date().getTime()
        }, //设置日期值, dateType 1 日 2 周 3月 4季 5年 6自定义 7带时分秒选择的自定义
        typeList: ["day", "month", "year", "datetimerange"],
        event: {
          date_out: this.CetDateSelect_time_date_out
          //   dateType_out: this.CetDateSelect_time_dateType_out
        }
      },
      CetGiantTree_node: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: true, //多选，不配置则默认单选
            chkboxType: { Y: "", N: "" }
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "metricId"
            },
            key: {
              name: "metricName",
              children: "children"
            }
          },
          view: {
            addDiyDom: this.addDiyDom
          },
          callback: {
            beforeCheck: this.nodeTreeBeforeCheck,
            beforeClick: this.nodeTreeBeforeClick
          }
        },
        unCheckTrigger_in: Date.now(),
        event: {
          currentNode_out: this.CetGiantTree_node_currentNode_out, //选中单行输出
          checkedNodes_out: this.CetGiantTree_node_checkedNodes_out //勾选节点输出
        }
      },
      showButton: true,
      CetChart_task: {
        //组件输入项
        inputData_in: null,
        options: {
          color: [
            "#6764E0",
            "#d48265",
            "#91c7ae",
            "#749f83",
            "#61a0a8",
            "#ca8622",
            "#bda29a",
            "#6e7074",
            "#546570",
            "#c4ccd3"
          ],
          grid: {
            top: 70,
            right: 80,
            bottom: 50,
            left: 20,
            containLabel: true
          },
          legend: {
            // icon: "circle",
            type: "scroll",
            top: 20,
            left: 60,
            textStyle: {
              fontFamily: "Microsoft YaHei",
              fontWeight: "normal",
              fontSize: 12
            }
          },
          tooltip: {
            trigger: "axis",
            appendToBody: true,
            axisPointer: { type: "line" }
            // formatter: params => {
            //   let item = params[0];
            //   let arr = params.map((item, index) => {
            //     return (
            //       item.marker +
            //       item.seriesName +
            //       "：" +
            //       common.setNumFixed(item.data[1], 2) +
            //       `${
            //         (index + 1) % 3 === 0
            //           ? "<br/>"
            //           : "<span style='margin-right: 10px'></span>"
            //       }`
            //     );
            //   });
            //   let tip =
            //     common.formatDate(item.axisValue, "YYYY-MM-DD HH:mm:ss") +
            //     "</br>" +
            //     arr.join("");
            //   return tip;
            // }
          },
          xAxis: {
            name: "日期",
            type: "time",
            axisTick: {
              alignWithLabel: true
            },
            axisLabel: {
              textStyle: {
                fontSize: 12
              },
              formatter: value =>
                common.formatDate(value, "YYYY-MM-DD HH:mm").replace(" ", "\n")

              // formatter: value => value.replace(" ", "\n")
            }
          },
          yAxis: {
            type: "value",
            axisLabel: {
              formatter: function (value) {
                // 强制转为普通数值字符串（适用于 <= 1e+21）
                const strVal = value.toLocaleString("fullwide", {
                  useGrouping: false
                });

                // 如超出 1e+21（JavaScript 最大安全整数范围），可简化为科学记数法
                if (value > Number.MAX_SAFE_INTEGER) {
                  return value.toExponential(2); // 或者显示 3e25
                }

                return strVal;
              }
            }
            // nameTextStyle: {
            //   padding: [0, 0, 0, 30] // 四个数字分别为上右下左与原位置距离
            // },
          },
          dataZoom: [
            {
              type: "inside",
              start: 0,
              end: 100,
              xAxisIndex: 0
            },
            {
              xAxisIndex: 0,
              textStyle: false,
              start: 0,
              end: 100,
              handleIcon:
                "M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z",
              handleSize: "80%",
              handleStyle: {
                color: "#fff",
                shadowBlur: 3,
                shadowColor: "rgba(0, 0, 0, 0.6)",
                shadowOffsetX: 2,
                shadowOffsetY: 2
              },
              left: 150,
              right: 150
            },
            {
              type: "inside",
              start: 0,
              end: 100,
              yAxisIndex: 0
            },
            {
              yAxisIndex: 0,
              textStyle: false,
              start: 0,
              end: 100,
              handleIcon:
                "M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z",
              handleSize: "80%",
              handleStyle: {
                color: "#fff",
                shadowBlur: 3,
                shadowColor: "rgba(0, 0, 0, 0.6)",
                shadowOffsetX: 2,
                shadowOffsetY: 2
              },
              top: 100,
              bottom: 100
            }
          ],
          series: []
        }
      }
    };
  },
  computed: {
    tableDivStyle() {
      let style = {
        width: "100%"
      };
      if (!this.tableData || this.tableData.length == 1) {
        style.width = "100%";
      } else {
        style.width = `${100 / this.tableData.length}%`;
      }
      return style;
    }
  },
  watch: {
    inputData_in: {
      immediate: true,
      handler(val) {
        if (_.isEmpty(val)) return;
        this.cleanData();
        this.querySimulationMetrics();
      }
    }
  },
  methods: {
    // 仿真起始时间切换
    startTimeChange(val) {
      let ztreeObj = this.$refs.metric.ztreeObj;
      const checkedNodes = ztreeObj.getCheckedNodes(true);
      this.CetGiantTree_node_checkedNodes_out(checkedNodes);
    },

    // 清空数据
    cleanData() {
      let options = this._.cloneDeep(this.CetChart_task.options);
      options.series = [];
      this.CetChart_task.options = options;

      this.showTableSelect = false;
      this.tableData = [{ data: [], header: [] }];
      this.currentPage = [];
      this.showRate = false;
      this.CetGiantTree_node.unCheckTrigger_in = Date.now();
      this.startTime = null;
    },
    // 导出
    downloadData() {
      this.exportLoading = true;

      let ztreeObj = this.$refs.metric.ztreeObj;
      const checkedNodes = ztreeObj.getCheckedNodes(true);
      let picture = this.$refs.chart.getDataURL({
        backgroundColor: "#ffffff",
        excludeComponents: ["dataZoom"]
      });
      let simulationId = this.inputData_in?.simulationId;
      let data = {
        metricIds: checkedNodes.map(item => item.metricId),
        startTimestamp: this.startTime,
        pic: picture
      };

      download(
        `/ngap-server/api/simulations/${simulationId}/metrics/export`,
        data,
        null,
        "POST",
        () => {
          this.exportLoading = false;
        }
      );
    },
    // 获取通用指标
    querySimulationMetrics() {
      customApi["querySimulationMetrics"](this.inputData_in?.simulationId).then(
        res => {
          let data = common.get(res, "data", []);
          this.CetGiantTree_node.inputData_in = data;
        }
      );
    },
    //仿真数据的电压合格率统计
    querySimulationReport() {
      return customApi["querySimulationReport"](
        this.inputData_in?.simulationId
      ).then(res => {
        return common.get(res, "data", []);
      });
    },
    async addDiyDom(treeId, treeNode) {
      const nodeElement = document.getElementById(treeNode.tId + "_a"); // 获取节点元素
      if (!nodeElement || treeNode.metricId != "simulationOutputMetric") return;
      let data = await this.querySimulationReport();

      const span = document.createElement("span");
      span.innerHTML = `
    <i class="el-icon-question" 
       style="margin-left:5px;cursor:pointer;" 
       title="整体电压合格率：${common.setNumFixed(
         data?.qualifiedRate,
         2
       )}&#10;整体电压越上限率：${common.setNumFixed(
        data?.aboveMaxRate,
        2
      )}&#10;整体电压越下限率：${common.setNumFixed(
        data?.belowMinRate,
        2
      )}&#10;整体最高电压：${common.setNumFixed(
        data?.maxVoltage,
        2
      )}&#10;整体最低电压：${common.setNumFixed(data?.minVoltage, 2)}&#10;"></i>
  `;

      nodeElement.appendChild(span);
    },

    nodeTreeBeforeCheck(treeId, treeNode) {
      let maxChecked = 5; // 最大可选数量
      if (!treeNode.isMetric) {
        this.$message.warning("只能勾选指标节点");
        return false;
      }

      const treeObj = $.fn.zTree.getZTreeObj(treeId);
      // 2. 统计已勾选的节点数量（排除取消勾选时）
      const checkedNodes = treeObj.getCheckedNodes(true);
      const willBeChecked = !treeNode.checked;
      if (willBeChecked && checkedNodes.length >= maxChecked) {
        this.$message.warning(`最多只能选择 ${maxChecked} 个指标！`);
        return false;
      }

      return true;
    },
    nodeTreeBeforeClick(treeId, treeNode) {
      return treeNode.isMetric;
    },
    CetGiantTree_node_currentNode_out(val) {},
    //设置对标线数据
    setMarkLineData(mark) {
      let markLineData = [];
      let color = {
        max: "#ff3d60",
        min: "#fcb92c"
      };
      Object.entries(mark).forEach(([key, value]) => {
        markLineData.push({
          yAxis: value,
          label: {
            position: "end",
            formatter: (key == "max" ? "上限值" : "下限值") + "{c}", //"{c}" +,
            color: color[key], //设置文字颜色
            lineHeight: 1,
            verticalAlign: "end" //配置文字偏移
          },
          lineStyle: {
            color: color[key]
          }
        });
      });
      return markLineData;
    },
    CetGiantTree_node_checkedNodes_out(val) {
      let options = this._.cloneDeep(this.CetChart_task.options);
      if (!val.length) {
        options.series = [];
        this.CetChart_task.options = options;
        this.tableData = [{ data: [], header: [] }];
        return;
      }
      customApi["queryBatchSimulationMetricsResult"]({
        simulationId: this.inputData_in?.simulationId,
        params: {
          metricIds: val.map(item => item.metricId),
          startTimestamp: this.startTime
        }
      }).then(res => {
        let data = common.get(res, "data", []);
        // this.tableData = data;
        this.currentPage.length = data.length;
        this.currentPage.fill(1);
        let tableList = [];
        if (_.isEmpty(data)) {
          options.series = [];
          this.tableData = [{ data: [], header: [] }];
        } else {
          options.series = data.map(item => {
            tableList.push({
              data: item.dataPoints,
              header: [
                { label: "时间", prop: "timestamp", width: "180" },
                {
                  label: item.metric.metricName,
                  prop: "value"
                }
              ]
            });
            return {
              name: item.metric.metricName,
              type: "line",
              data: item.dataPoints.map((item, index) => [
                item.timestamp,
                common.roundNumber(item.value)
              ]),
              lineStyle: {
                width: 3
              },
              smooth: true,
              sampling: "lttb"
            };
          });
          this.tableData = tableList;
          if (!_.isEmpty(data[0]?.statistics)) {
            if (val.length > 1) this.showRate = false;
            else this.showRate = data[0]?.statistics?.hasRate;
            this.rate = data[0]?.statistics?.rate;

            let series0 = options.series[0];
            let mark = {
              max: data[0]?.statistics?.max,
              min: data[0]?.statistics?.min
            };
            series0.markLine = { data: this.setMarkLineData(mark) };
          } else {
            this.showRate = false;
          }
        }
        this.CetChart_task.options = options;
      });
    },
    CetDateSelect_time_date_out(val) {}
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
::v-deep .input-label {
  line-height: 25px;
}

.trend-table {
  display: inline-block;
  height: calc(100% - 40px);
  width: 100%;

  p {
    text-align: center;
  }

  > div {
    display: inline-block;
    height: 100%;
    padding-right: 12px;
    min-width: 312px;
    box-sizing: border-box;
  }
}

.trend-chart {
  width: 100%;
  height: 100%;
}

.hide-chart {
  position: absolute;
  left: -10000px;
  top: -10000px;
  width: calc(100% - 80px);
  height: 100%;
}
</style>
