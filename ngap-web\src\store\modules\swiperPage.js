const state = {
  pageShow: false,
  intervalTime: 3,
  titleStr: "轮播图",
  pageInfo: [],
  isPreview: false
};

const mutations = {
  CHANGE_SWIPER_INFO: (state, { key, value }) => {
    if (state.hasOwnProperty(key)) {
      state[key] = value;
    }
  }
};

const actions = {
  changeSwiperInfo({ commit }, data) {
    commit("CHANGE_SWIPER_INFO", data);
  },
  changeSwiperData({ commit }, data) {
    for (let sKey in data) {
      commit("CHANGE_SWIPER_INFO", { key: sKey, value: data[sKey] });
    }
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
