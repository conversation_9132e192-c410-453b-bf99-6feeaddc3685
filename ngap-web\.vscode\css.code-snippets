{
  // Place your V2.x 工作区 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  // "Print to console": {
  // 	"scope": "javascript,typescript",
  // 	"prefix": "log",
  // 	"body": [
  // 		"console.log('$1');",
  // 		"$2"
  // 	],
  // 	"description": "Log output to console"
  // }

  "@bg": {
    "prefix": "@bg",
    "body": ["@include background_color(BG1$1);"],
    "description": ""
  },
  "@border": {
    "prefix": "@border",
    "body": ["@include border_color(B1$1);"],
    "description": ""
  },
  "@fontsize": {
    "prefix": "@fontsize",
    "body": ["@include font_size(Aa$1);"],
    "description": ""
  },
  "@color": {
    "prefix": "@color",
    "body": ["@include font_color(T2$1);"],
    "description": ""
  },
  "@padding": {
    "prefix": "@padding",
    "body": ["@include padding(0 J3, !important);"],
    "description": ""
  },
  "@paddingtop": {
    "prefix": "@paddingtop",
    "body": ["@include padding_top(J1);"],
    "description": ""
  },
  "@paddingright": {
    "prefix": "@paddingright",
    "body": ["@include padding_right(J1);"],
    "description": ""
  },
  "@paddingbottom": {
    "prefix": "@paddingbottom",
    "body": ["@include padding_bottom(J1);"],
    "description": ""
  },
  "@paddingleft": {
    "prefix": "@paddingleft",
    "body": ["@include padding_left(J1);"],
    "description": ""
  },
  "@margin": {
    "prefix": "@margin",
    "body": ["@include margin(0 J3, !important);"],
    "description": ""
  },
  "@margintop": {
    "prefix": "@margintop",
    "body": ["@include margin_top(J1);"],
    "description": ""
  },
  "@marginright": {
    "prefix": "@marginright",
    "body": ["@include margin_right(J1);"],
    "description": ""
  },
  "@marginbottom": {
    "prefix": "@marginbottom",
    "body": ["@include margin_bottom(J1);"],
    "description": ""
  },
  "@marginleft": {
    "prefix": "@marginleft",
    "body": ["@include margin_left(J1);"],
    "description": ""
  },
  "@shadow": {
    "prefix": "@shadow",
    "body": ["@include box_shadow(S1);"],
    "description": ""
  }
}
