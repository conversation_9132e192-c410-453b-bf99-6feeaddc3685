/*
 * @Author: your name
 * @Date: 2020-12-25 15:30:12
 * @LastEditTime: 2021-04-22 18:14:40
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \frame\src\api\baseApi\model.js
 */
import fetch from "@/utils/fetch";
//模型版本
const modelVersion = "v1";

function processResponse(response) {
  //TODO 处理数据中模型和主模型是一对一关系时, 拉平为对象, 而不是数组, 通过接口给的一个字段来判断.

  return response;
}

function processRequest(data) {
  //TODO 这里主要是恢复为数组的关系,这部分为通用处理,同时data本身也要转为一个数组
  return [data];
}

export function queryModel(data, hideNotice) {
  return fetch({
    url: `/model/${modelVersion}/query`,
    method: "POST",
    headers: { hideNotice: hideNotice },
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    timeout: 60000,
    data
  });
}

export function queryTreeModel(data) {
  return fetch({
    url: `/model/${modelVersion}/query`,
    method: "POST",
    timeout: 60000,
    data
  });
}

export function deleteModelInstence(data) {
  return fetch({
    url: `/model/${modelVersion}/${data.modelLabel}`,
    method: "DELETE",
    data: data.idRange
  });
}

export function writeModel(data) {
  return fetch({
    url: `/model/${modelVersion}/write/hierachy`,
    method: "POST",
    data: processRequest(data)
  });
}

export function queryEnum(data) {
  return fetch({
    url: `/model/${modelVersion}/enumerations/${data.rootLabel}`,
    method: "GET",
    data
  });
}

export function queryAllModelMeta() {
  return fetch({
    url: `/model-meta/v1/models/`,
    method: "GET"
  });
}

export function queryModelMetaByLabel(data) {
  return fetch({
    url: `/model-meta/v1/models/label/${data}`,
    method: "GET"
  });
}
