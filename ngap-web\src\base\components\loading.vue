<script>
import { Loading } from "element-ui";

export class LoadingManger {
  constructor(options) {
    this.count = 0;
    this.loadingInstance = null;
    this.options = options;
  }

  showLoading(el) {
    if (this.count === 0) {
      this.loadingInstance = Loading.service(
        Object.assign(
          {
            target: el
          },
          this.options
        )
      );
    }
    this.count++;
  }

  hideLoading() {
    this.count--;
    if (this.count === 0) {
      this.loadingInstance.close();
      return true;
    }
    // 重置
    if (this.count < 0) {
      this.count = 0;
    }
  }
}

/**
 * 全屏loading
 */
let loadingManger = null;
if (!loadingManger) {
  loadingManger = new LoadingManger({
    lock: true,
    fullscreen: false,
    text: "加载中……",
    background: "rgba(255, 255, 255, 0)",
    customClass: "loading-class"
  });
}
export const showLoading = () => {
  loadingManger.showLoading();
};

export const hideLoading = () => {
  loadingManger.hideLoading();
};

export default {
  show: showLoading,
  hide: hideLoading
};

const loadingMangerSymbol = Symbol("loadingManger");
export const loadingMixin = {
  showLoading(el) {
    if (!this[loadingMangerSymbol]) {
      this[loadingMangerSymbol] = new LoadingManger({
        background: "rgba(255, 255, 255, 0)",
        customClass: "loading-component-class"
      });
    }
    this[loadingMangerSymbol].showLoading(el || this.$el);
  },
  hideLoading() {
    const isEnd = this[loadingMangerSymbol].hideLoading();
    if (isEnd) {
      delete this[loadingMangerSymbol];
    }
  }
};
</script>
<style lang="scss">
.loading-class {
  z-index: 10000;
}

.loading-component-class {
  z-index: 10000;
  .el-loading-spinner {
    position: absolute;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
    height: 100%;
    display: flex;
    align-items: center;
    // 重置 element-ui 的样式
    margin-top: initial;
    width: initial;
    text-align: initial;
    .circular {
      height: 100%;
      width: 100%;
      max-height: 42px;
      max-width: 42px;
    }
  }
}
</style>
