package com.cet.electric.ngapserver.util;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({FileUtils.class})
public class FileUtilsTest {

    @Mock
    private HttpServletResponse mockResponse;

    @Mock
    private ServletOutputStream mockOutputStream;

    private File tempDir;
    private File testFile;

    @Before
    public void setUp() throws IOException {
        MockitoAnnotations.initMocks(this);
        
        // 创建临时目录和文件用于测试
        tempDir = Files.createTempDirectory("fileutils_test").toFile();
        testFile = new File(tempDir, "test.txt");
        
        // 创建测试文件
        try (FileWriter writer = new FileWriter(testFile)) {
            writer.write("Test file content");
        }
    }

    @After
    public void tearDown() throws IOException {
        // 清理测试文件和目录
        if (testFile.exists()) {
            testFile.delete();
        }
        if (tempDir.exists()) {
            FileUtils.deleteDirectory(tempDir);
        }
    }

    @Test
    public void testGetCurrentPath() {
        // 执行测试
        String currentPath = FileUtils.getCurrentPath();

        // 验证结果
        assertNotNull(currentPath);
        assertEquals(System.getProperty("user.dir"), currentPath);
    }

    @Test
    public void testCheckFileExists_ExistingFile() {
        // 执行测试
        boolean exists = FileUtils.checkFileExists(testFile.getAbsolutePath());

        // 验证结果
        assertTrue(exists);
    }

    @Test
    public void testCheckFileExists_NonExistingFile() {
        // 执行测试
        boolean exists = FileUtils.checkFileExists("/non/existing/file.txt");

        // 验证结果
        assertFalse(exists);
    }

    @Test
    public void testCheckFileExists_NullPath() {
        // 执行测试
        boolean exists = FileUtils.checkFileExists(null);

        // 验证结果
        assertFalse(exists);
    }

    @Test
    public void testCheckFileExists_EmptyPath() {
        // 执行测试
        boolean exists = FileUtils.checkFileExists("");

        // 验证结果
        assertFalse(exists);
    }

    @Test
    public void testCheckFileExists_Directory() {
        // 执行测试 - 传入目录路径
        boolean exists = FileUtils.checkFileExists(tempDir.getAbsolutePath());

        // 验证结果 - 目录不算文件，应该返回false
        assertFalse(exists);
    }

    @Test
    public void testDownloadFile_Success() throws IOException {
        // Mock 响应对象
        when(mockResponse.getOutputStream()).thenReturn(mockOutputStream);

        // 执行测试
        FileUtils.downloadFile(testFile.getAbsolutePath(), mockResponse);

        // 验证响应设置
        verify(mockResponse).setContentType("application/octet-stream");
        verify(mockResponse).setHeader(eq("Content-Disposition"), anyString());
        verify(mockOutputStream, atLeastOnce()).write(any(byte[].class), anyInt(), anyInt());
        verify(mockOutputStream).flush();
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDownloadFile_NullPath() {
        // 执行测试 - 应该抛出异常
        FileUtils.downloadFile(null, mockResponse);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDownloadFile_EmptyPath() {
        // 执行测试 - 应该抛出异常
        FileUtils.downloadFile("", mockResponse);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testDownloadFile_NonExistingFile() {
        // 执行测试 - 应该抛出异常
        FileUtils.downloadFile("/non/existing/file.txt", mockResponse);
    }

    @Test
    public void testCopyDirectory_Success() throws IOException {
        // 准备测试数据
        File sourceDir = new File(tempDir, "source");
        File targetDir = new File(tempDir, "target");
        sourceDir.mkdirs();

        // 在源目录中创建测试文件
        File sourceFile = new File(sourceDir, "source_file.txt");
        try (FileWriter writer = new FileWriter(sourceFile)) {
            writer.write("Source file content");
        }

        // 创建子目录
        File subDir = new File(sourceDir, "subdir");
        subDir.mkdirs();
        File subFile = new File(subDir, "sub_file.txt");
        try (FileWriter writer = new FileWriter(subFile)) {
            writer.write("Sub file content");
        }

        // 执行测试
        FileUtils.copyDirectory(sourceDir, targetDir);

        // 验证结果
        assertTrue(targetDir.exists());
        assertTrue(targetDir.isDirectory());
        
        File copiedFile = new File(targetDir, "source_file.txt");
        assertTrue(copiedFile.exists());
        
        File copiedSubDir = new File(targetDir, "subdir");
        assertTrue(copiedSubDir.exists());
        assertTrue(copiedSubDir.isDirectory());
        
        File copiedSubFile = new File(copiedSubDir, "sub_file.txt");
        assertTrue(copiedSubFile.exists());
    }

    @Test(expected = IOException.class)
    public void testCopyDirectory_SourceNotExists() throws IOException {
        // 准备测试数据
        File sourceDir = new File(tempDir, "non_existing_source");
        File targetDir = new File(tempDir, "target");

        // 执行测试 - 应该抛出异常
        FileUtils.copyDirectory(sourceDir, targetDir);
    }

    @Test(expected = IOException.class)
    public void testCopyDirectory_SourceNotDirectory() throws IOException {
        // 准备测试数据
        File targetDir = new File(tempDir, "target");

        // 执行测试 - 传入文件而不是目录，应该抛出异常
        FileUtils.copyDirectory(testFile, targetDir);
    }

    @Test
    public void testDeleteDirectory_Success() throws IOException {
        // 准备测试数据
        File dirToDelete = new File(tempDir, "to_delete");
        dirToDelete.mkdirs();
        
        File fileInDir = new File(dirToDelete, "file.txt");
        try (FileWriter writer = new FileWriter(fileInDir)) {
            writer.write("Content");
        }

        File subDir = new File(dirToDelete, "subdir");
        subDir.mkdirs();

        // 执行测试
        FileUtils.deleteDirectory(dirToDelete);

        // 验证结果
        assertFalse(dirToDelete.exists());
    }

    @Test
    public void testDeleteFilesByExtension_Success() throws IOException {
        // 准备测试数据
        File csvFile1 = new File(tempDir, "file1.csv");
        File csvFile2 = new File(tempDir, "file2.csv");
        File txtFile = new File(tempDir, "file.txt");

        csvFile1.createNewFile();
        csvFile2.createNewFile();
        txtFile.createNewFile();

        // 执行测试
        int deletedCount = FileUtils.deleteFilesByExtension(tempDir.getAbsolutePath(), ".csv");

        // 验证结果
        assertEquals(2, deletedCount);
        assertFalse(csvFile1.exists());
        assertFalse(csvFile2.exists());
        assertTrue(txtFile.exists()); // txt文件应该还存在
    }

    @Test
    public void testDeleteFilesByExtension_NullParams() {
        // 执行测试
        int deletedCount1 = FileUtils.deleteFilesByExtension(null, ".csv");
        int deletedCount2 = FileUtils.deleteFilesByExtension(tempDir.getAbsolutePath(), null);

        // 验证结果
        assertEquals(0, deletedCount1);
        assertEquals(0, deletedCount2);
    }

    @Test
    public void testDeleteFilesByExtension_NonExistingDirectory() {
        // 执行测试
        int deletedCount = FileUtils.deleteFilesByExtension("/non/existing/dir", ".csv");

        // 验证结果
        assertEquals(0, deletedCount);
    }

    @Test
    public void testZipFolder_Success() throws IOException {
        // 准备测试数据
        File folderToZip = new File(tempDir, "zip_source");
        folderToZip.mkdirs();
        
        File fileInFolder = new File(folderToZip, "file.txt");
        try (FileWriter writer = new FileWriter(fileInFolder)) {
            writer.write("Content to zip");
        }

        File zipFile = new File(tempDir, "test.zip");
        
        // 执行测试
        try (ZipOutputStream zipOut = new ZipOutputStream(new FileOutputStream(zipFile))) {
            FileUtils.zipFolder(folderToZip, "root", zipOut);
        }

        // 验证结果
        assertTrue(zipFile.exists());
        assertTrue(zipFile.length() > 0);
    }

    @Test
    public void testUnzipFile_Success() throws IOException {
        // 准备测试数据 - 创建一个简单的ZIP文件
        File zipFile = new File(tempDir, "test.zip");
        File extractDir = new File(tempDir, "extracted");

        try (ZipOutputStream zipOut = new ZipOutputStream(new FileOutputStream(zipFile))) {
            ZipEntry entry = new ZipEntry("test_file.txt");
            zipOut.putNextEntry(entry);
            zipOut.write("Test content".getBytes());
            zipOut.closeEntry();
        }

        // 执行测试
        FileUtils.unzipFile(zipFile, extractDir);

        // 验证结果
        assertTrue(extractDir.exists());
        File extractedFile = new File(extractDir, "test_file.txt");
        assertTrue(extractedFile.exists());
    }

    // ==================== 新增安全性方法测试 ====================

    @Test
    public void testIsPathSafe_SafePath() {
        // 测试安全路径
        File safePath = new File(tempDir, "safe_directory");
        safePath.mkdirs();

        // 执行测试
        boolean isSafe = FileUtils.isPathSafe(safePath);

        // 验证结果
        assertTrue("临时目录应该是安全的", isSafe);
    }

    @Test
    public void testIsPathSafe_NullPath() {
        // 执行测试
        boolean isSafe = FileUtils.isPathSafe(null);

        // 验证结果
        assertFalse("null路径应该不安全", isSafe);
    }

    @Test
    public void testIsPathSafe_SystemPath() {
        // 测试系统路径（Linux）
        if (!System.getProperty("os.name").toLowerCase().contains("windows")) {
            File procPath = new File("/proc");
            File sysPath = new File("/sys");

            // 执行测试
            boolean isProcSafe = FileUtils.isPathSafe(procPath);
            boolean isSysSafe = FileUtils.isPathSafe(sysPath);

            // 验证结果
            assertFalse("/proc路径应该不安全", isProcSafe);
            assertFalse("/sys路径应该不安全", isSysSafe);
        }
    }

    @Test
    public void testIsPathSafe_WindowsSystemPath() {
        // 测试Windows系统路径
        if (System.getProperty("os.name").toLowerCase().contains("windows")) {
            File windowsPath = new File("C:\\Windows");
            File programFilesPath = new File("C:\\Program Files");

            // 执行测试
            boolean isWindowsSafe = FileUtils.isPathSafe(windowsPath);
            boolean isProgramFilesSafe = FileUtils.isPathSafe(programFilesPath);

            // 验证结果
            assertFalse("C:\\Windows路径应该不安全", isWindowsSafe);
            assertFalse("C:\\Program Files路径应该不安全", isProgramFilesSafe);
        }
    }

    @Test
    public void testIsWithinDirectory_ValidPath() throws IOException {
        // 准备测试数据
        File baseDir = new File(tempDir, "base");
        File subDir = new File(baseDir, "sub");
        baseDir.mkdirs();
        subDir.mkdirs();

        // 执行测试
        boolean isWithin = FileUtils.isWithinDirectory(subDir, baseDir);

        // 验证结果
        assertTrue("子目录应该在基础目录内", isWithin);
    }

    @Test
    public void testIsWithinDirectory_OutsidePath() throws IOException {
        // 准备测试数据
        File baseDir = new File(tempDir, "base");
        File outsideDir = new File(tempDir, "outside");
        baseDir.mkdirs();
        outsideDir.mkdirs();

        // 执行测试
        boolean isWithin = FileUtils.isWithinDirectory(outsideDir, baseDir);

        // 验证结果
        assertFalse("外部目录不应该在基础目录内", isWithin);
    }

    @Test
    public void testIsWithinDirectory_NullParams() {
        // 执行测试
        boolean isWithin1 = FileUtils.isWithinDirectory(null, tempDir);
        boolean isWithin2 = FileUtils.isWithinDirectory(tempDir, null);
        boolean isWithin3 = FileUtils.isWithinDirectory(null, null);

        // 验证结果
        assertFalse("null目标路径应该返回false", isWithin1);
        assertFalse("null基础目录应该返回false", isWithin2);
        assertFalse("两个null参数应该返回false", isWithin3);
    }

    @Test
    public void testIsSymbolicLink_RegularFile() {
        // 执行测试
        boolean isSymLink = FileUtils.isSymbolicLink(testFile);

        // 验证结果
        assertFalse("普通文件不应该是符号链接", isSymLink);
    }

    @Test
    public void testIsSymbolicLink_NullFile() {
        // 执行测试
        boolean isSymLink = FileUtils.isSymbolicLink(null);

        // 验证结果
        assertFalse("null文件应该返回false", isSymLink);
    }

    @Test
    public void testIsSymbolicLink_NonExistentFile() {
        // 准备测试数据
        File nonExistentFile = new File(tempDir, "non_existent_file.txt");

        // 执行测试
        boolean isSymLink = FileUtils.isSymbolicLink(nonExistentFile);

        // 验证结果
        // 不存在的文件可能被认为是符号链接（因为无法获取规范化路径）
        // 这是一种安全的默认行为
        assertTrue("不存在的文件应该被认为是符号链接（安全考虑）", isSymLink);
    }
}
