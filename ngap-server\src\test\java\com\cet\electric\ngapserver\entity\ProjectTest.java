package com.cet.electric.ngapserver.entity;

import org.junit.Test;

import static org.junit.Assert.*;

public class ProjectTest {

    @Test
    public void testProjectBuilder() {
        // 准备测试数据
        Long projectId = 1L;
        String projectName = "测试项目";
        String projectType = "General_scenario";
        Long createdAt = System.currentTimeMillis();
        Long updatedAt = System.currentTimeMillis();
        Integer isDeleted = 0;

        // 使用Builder创建Project对象
        Project project = Project.builder()
                .projectId(projectId)
                .projectName(projectName)
                .projectType(projectType)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .isDeleted(isDeleted)
                .build();

        // 验证结果
        assertNotNull(project);
        assertEquals(projectId, project.getProjectId());
        assertEquals(projectName, project.getProjectName());
        assertEquals(projectType, project.getProjectType());
        assertEquals(createdAt, project.getCreatedAt());
        assertEquals(updatedAt, project.getUpdatedAt());
        assertEquals(isDeleted, project.getIsDeleted());
    }

    @Test
    public void testProjectNoArgsConstructor() {
        // 使用无参构造函数创建Project对象
        Project project = new Project();

        // 验证结果
        assertNotNull(project);
        assertNull(project.getProjectId());
        assertNull(project.getProjectName());
        assertNull(project.getProjectType());
        assertNull(project.getCreatedAt());
        assertNull(project.getUpdatedAt());
        assertNull(project.getIsDeleted());
    }

    @Test
    public void testProjectAllArgsConstructor() {
        // 准备测试数据
        Long projectId = 1L;
        String projectName = "测试项目";
        String projectType = "General_scenario";
        Long createdAt = System.currentTimeMillis();
        Long updatedAt = System.currentTimeMillis();
        Integer isDeleted = 0;

        // 使用全参构造函数创建Project对象
        Project project = new Project(projectId, projectName, projectType, createdAt, updatedAt, isDeleted);

        // 验证结果
        assertNotNull(project);
        assertEquals(projectId, project.getProjectId());
        assertEquals(projectName, project.getProjectName());
        assertEquals(projectType, project.getProjectType());
        assertEquals(createdAt, project.getCreatedAt());
        assertEquals(updatedAt, project.getUpdatedAt());
        assertEquals(isDeleted, project.getIsDeleted());
    }

    @Test
    public void testProjectSettersAndGetters() {
        // 创建Project对象
        Project project = new Project();

        // 准备测试数据
        Long projectId = 1L;
        String projectName = "测试项目";
        String projectType = "General_scenario";
        Long createdAt = System.currentTimeMillis();
        Long updatedAt = System.currentTimeMillis();
        Integer isDeleted = 0;

        // 使用Setter设置值
        project.setProjectId(projectId);
        project.setProjectName(projectName);
        project.setProjectType(projectType);
        project.setCreatedAt(createdAt);
        project.setUpdatedAt(updatedAt);
        project.setIsDeleted(isDeleted);

        // 使用Getter验证值
        assertEquals(projectId, project.getProjectId());
        assertEquals(projectName, project.getProjectName());
        assertEquals(projectType, project.getProjectType());
        assertEquals(createdAt, project.getCreatedAt());
        assertEquals(updatedAt, project.getUpdatedAt());
        assertEquals(isDeleted, project.getIsDeleted());
    }

    @Test
    public void testProjectEqualsAndHashCode() {
        // 创建两个相同的Project对象
        Project project1 = Project.builder()
                .projectId(1L)
                .projectName("测试项目")
                .projectType("General_scenario")
                .createdAt(1000L)
                .updatedAt(2000L)
                .isDeleted(0)
                .build();

        Project project2 = Project.builder()
                .projectId(1L)
                .projectName("测试项目")
                .projectType("General_scenario")
                .createdAt(1000L)
                .updatedAt(2000L)
                .isDeleted(0)
                .build();

        // 验证equals方法
        assertEquals(project1, project2);
        assertEquals(project2, project1);
        assertEquals(project1, project1);

        // 验证hashCode方法
        assertEquals(project1.hashCode(), project2.hashCode());
    }

    @Test
    public void testProjectNotEquals() {
        // 创建两个不同的Project对象
        Project project1 = Project.builder()
                .projectId(1L)
                .projectName("测试项目1")
                .projectType("General_scenario")
                .build();

        Project project2 = Project.builder()
                .projectId(2L)
                .projectName("测试项目2")
                .projectType("Voltage_qualification_scenario")
                .build();

        // 验证不相等
        assertNotEquals(project1, project2);
        assertNotEquals(project2, project1);
        assertNotEquals(project1, null);
        assertNotEquals(project1, "not a project");
    }

    @Test
    public void testProjectToString() {
        // 创建Project对象
        Project project = Project.builder()
                .projectId(1L)
                .projectName("测试项目")
                .projectType("General_scenario")
                .createdAt(1000L)
                .updatedAt(2000L)
                .isDeleted(0)
                .build();

        // 验证toString方法
        String toString = project.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("Project"));
        assertTrue(toString.contains("projectId=1"));
        assertTrue(toString.contains("projectName=测试项目"));
        assertTrue(toString.contains("projectType=General_scenario"));
    }

    @Test
    public void testProjectWithNullValues() {
        // 创建包含null值的Project对象
        Project project = Project.builder()
                .projectId(null)
                .projectName(null)
                .projectType(null)
                .createdAt(null)
                .updatedAt(null)
                .isDeleted(null)
                .build();

        // 验证null值处理
        assertNotNull(project);
        assertNull(project.getProjectId());
        assertNull(project.getProjectName());
        assertNull(project.getProjectType());
        assertNull(project.getCreatedAt());
        assertNull(project.getUpdatedAt());
        assertNull(project.getIsDeleted());
    }

    @Test
    public void testProjectWithEmptyStrings() {
        // 创建包含空字符串的Project对象
        Project project = Project.builder()
                .projectId(1L)
                .projectName("")
                .projectType("")
                .createdAt(System.currentTimeMillis())
                .updatedAt(System.currentTimeMillis())
                .isDeleted(0)
                .build();

        // 验证空字符串处理
        assertNotNull(project);
        assertEquals("", project.getProjectName());
        assertEquals("", project.getProjectType());
    }

    @Test
    public void testProjectFieldModification() {
        // 创建Project对象
        Project project = Project.builder()
                .projectId(1L)
                .projectName("原始项目名")
                .projectType("General_scenario")
                .isDeleted(0)
                .build();

        // 修改字段值
        project.setProjectName("修改后的项目名");
        project.setProjectType("Voltage_qualification_scenario");
        project.setIsDeleted(1);

        // 验证修改结果
        assertEquals("修改后的项目名", project.getProjectName());
        assertEquals("Voltage_qualification_scenario", project.getProjectType());
        assertEquals(Integer.valueOf(1), project.getIsDeleted());
    }

    @Test
    public void testProjectTimestampFields() {
        // 获取当前时间
        long currentTime = System.currentTimeMillis();
        
        // 创建Project对象
        Project project = Project.builder()
                .projectId(1L)
                .projectName("时间戳测试项目")
                .createdAt(currentTime)
                .updatedAt(currentTime + 1000)
                .build();

        // 验证时间戳字段
        assertEquals(Long.valueOf(currentTime), project.getCreatedAt());
        assertEquals(Long.valueOf(currentTime + 1000), project.getUpdatedAt());
        assertTrue(project.getUpdatedAt() > project.getCreatedAt());
    }

    @Test
    public void testProjectDeletedStatus() {
        // 测试未删除状态
        Project activeProject = Project.builder()
                .projectId(1L)
                .projectName("活跃项目")
                .isDeleted(0)
                .build();

        assertEquals(Integer.valueOf(0), activeProject.getIsDeleted());

        // 测试已删除状态
        Project deletedProject = Project.builder()
                .projectId(2L)
                .projectName("已删除项目")
                .isDeleted(1)
                .build();

        assertEquals(Integer.valueOf(1), deletedProject.getIsDeleted());
    }
}
