package com.cet.electric.ngapserver.dao;

import com.cet.electric.ngapserver.entity.Simulation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Component
public interface SimulationDao {
    /**
     * 分页查询仿真任务
     *
     * @param offset     偏移量
     * @param size       每页大小
     * @param sortBy     排序字段
     * @param sortOrder  排序方向
     * @param projectId  项目ID（必填）
     * @param keyword    搜索关键词(可选)
     * @param startTime  起始时间(时间戳-毫秒，可选)
     * @param endTime    结束时间(时间戳-毫秒，可选)
     * @return 仿真任务列表
     */
    List<Simulation> getSimulations(@Param("offset") int offset,
                                    @Param("size") int size,
                                    @Param("sortBy") String sortBy,
                                    @Param("sortOrder") String sortOrder,
                                    @Param("projectId") Long projectId,
                                    @Param("keyword") String keyword,
                                    @Param("startTime") Long startTime,
                                    @Param("endTime") Long endTime);

    /**
     * 查询仿真任务总数
     *
     * @param projectId  项目ID（必填）
     * @param keyword    搜索关键词(可选)
     * @param startTime  开始时间(可选)
     * @param endTime    结束时间(可选)
     * @return 总数
     */
    Long countSimulations(@Param("projectId") Long projectId,
                          @Param("keyword") String keyword,
                          @Param("startTime") Long startTime,
                          @Param("endTime") Long endTime);

    /**
     * 创建新仿真任务
     *
     * @param simulation 仿真任务实体
     * @return 受影响的行数
     */
    int createSimulation(Simulation simulation);

    /**
     * 根据项目ID和仿真名称查询仿真任务
     *
     * @param projectId 项目ID
     * @param simulationName 仿真名称
     * @return 仿真任务实体，如果不存在返回null
     */
    Simulation findByProjectIdAndName(@Param("projectId") Long projectId,
                                      @Param("simulationName") String simulationName);

    /**
     * 根据ID查询仿真任务
     *
     * @param simulationId 仿真任务ID
     * @return 仿真任务实体，如果不存在返回null
     */
    Simulation findById(Long simulationId);

    /**
     * 更新仿真任务信息
     *
     * @param simulation 更新的仿真任务实体
     * @return 受影响的行数
     */
    int updateSimulation(Simulation simulation);

    /**
     * 获取所有已标记为删除的仿真任务
     *
     * @return 已标记为删除的仿真任务列表
     */
    List<Simulation> findAllDeleted();

    /**
     * 物理删除所有已标记为删除的仿真任务（物理删除）
     *
     * @return 受影响的行数
     */
    int deleteAllMarkedAsDeleted();

}

