<template>
  <div class="no-permission">
    <div class="no-permission-container">
      <p class="text-center">
        <el-alert :title="$T('该账号无相关页面权限，请联系管理员！')" type="warning" :closable="false" />
      </p>
      <p class="text-center">
        <el-button type="primary" @click="click">
          {{ $T("返回登录其他账号") }}
        </el-button>
      </p>
    </div>
  </div>
</template>

<script>
export default {
  name: "NoPermission",
  methods: {
    click() {
      window.sessionStorage.removeItem("token");
      window.location.reload();
    }
  }
};
</script>
<style lang="scss" scoped>
.no-permission {
  position: relative;
  width: 100%;
  height: 100%;
}
.no-permission-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
