{
  // Place your V2.x 工作区 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  // "Print to console": {
  // 	"scope": "javascript,typescript",
  // 	"prefix": "log",
  // 	"body": [
  // 		"console.log('$1');",
  // 		"$2"
  // 	],
  // 	"description": "Log output to console"
  // }
  //cet-dateselect的代码片段
  "cet-dateselect-template": {
    "prefix": "cet-dateselect-template",
    "body": [
      " <CetDateSelect",
      "  v-bind=\"CetDateSelect_$1\"                                                  ",
      "  v-on=\"CetDateSelect_${1:请输入组件唯一识别字符串}.event\"                  ",
      " ></CetDateSelect>"
    ],
    "description": ""
  },
  "cet-dateselect-data": {
    "prefix": "cet-dateselect-data",
    "body": [
      "// ${1:设置组件唯一识别字段}组件                                                   ",
      " CetDateSelect_$1: {                     ",
      "   value: { dateType: \"2\", value: new Date().getTime() - 34 * 3600 * 1000 * 24 }, //设置日期值, dateType 1 日 2 周 3月 4季 5年 6自定义 7带时分秒选择的自定义                      ",
      "   //自定义选项 typeList: [\"day\", \"week\", \"month\", \"season\",  \"year\", \"daterange\", \"datetimerange\"]                                     ",
      "   event: {                                                              ",
      "      date_out:this.CetDateSelect_$1_date_out,                                                ",
      "      dateType_out:this.CetDateSelect_$1_dateType_out                                                ",
      "   }                                                                           ",
      " },                                                         "
    ],
    "description": ""
  },
  "cet-dateselect-method": {
    "prefix": "cet-dateselect-method",
    "body": [
      "// ${1:设置组件唯一识别字段}输出,方法名要带_out后缀                                        ",
      "CetDateSelect_$1_date_out(val) {},                                  ",
      "CetDateSelect_$1_dateType_out(val,dateValue) {},                                  "
    ],
    "description": ""
  }
}
