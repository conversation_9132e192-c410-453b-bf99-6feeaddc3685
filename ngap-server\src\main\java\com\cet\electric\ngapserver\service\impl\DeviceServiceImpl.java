package com.cet.electric.ngapserver.service.impl;

import com.cet.electric.ngapserver.dao.DeviceDao;
import com.cet.electric.ngapserver.dto.ErrorMsg;
import com.cet.electric.ngapserver.entity.Device;
import com.cet.electric.ngapserver.service.DeviceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 设备服务实现类
 * <AUTHOR>
 */
@Service
public class DeviceServiceImpl implements DeviceService {

    private static final Logger log = LoggerFactory.getLogger(DeviceServiceImpl.class);

    private final Object deviceLock = new Object();

    @Autowired
    private DeviceDao deviceDao;

    @Override
    public Device createDevice(Device device) {
        synchronized (deviceLock) {
            // 参数校验
            validateDeviceForCreate(device);

            // 系统自动设置版本为1.0
            device.setVersion("1.0");

            // 检查参数代码是否已存在
            if (device.getParameterCode() != null && !device.getParameterCode().isEmpty()) {
                Device existingDevice = deviceDao.findByParameterCode(device.getParameterCode().trim());
                if (existingDevice != null) {
                    log.error("设备创建失败：参数代码已存在: {}", device.getParameterCode());
                    throw new ErrorMsg(-1, "设备创建失败：参数代码已存在.");
                }
            }

            // 保存设备
            int result = deviceDao.createDevice(device);
            if (result <= 0) {
                log.error("设备创建失败: {}", device);
                throw new ErrorMsg(-1, "数据库中设备创建失败.");
            }
            log.info("设备创建成功: {}", device);
            return device;
        }
    }

    @Override
    public List<Device> getDevices(Integer page, Integer size, String sortBy, String sortOrder, String keyword) {
        log.info("开始查询设备列表, page: {}, size: {}, sortBy: {}, sortOrder: {}, keyword: {}",
                page, size, sortBy, sortOrder, keyword);

        // 参数校验
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }

        // 验证排序字段
        List<String> validSortFields = Arrays.asList("parameterId", "deviceType", "parameterCode", "version");
        if (sortBy == null || !validSortFields.contains(sortBy)) {
            log.warn("无效的排序字段: {}，使用默认排序字段 parameterId", sortBy);
            sortBy = "parameterId";
        }

        // 验证排序方向
        if ((!"asc".equalsIgnoreCase(sortOrder) && !"desc".equalsIgnoreCase(sortOrder))) {
            log.warn("无效的排序方向: {}，使用默认排序方向 desc", sortOrder);
            sortOrder = "desc";
        }

        // 计算分页参数
        int offset = (page - 1) * size;

        // 查询数据列表
        List<Device> devices = deviceDao.getDevices(offset, size, sortBy, sortOrder, keyword);

        log.info("查询设备列表成功，查询到 {} 条数据", devices.size());
        return devices;
    }

    @Override
    public Long countDevices(String keyword) {
        log.info("开始查询设备总数, keyword: {}", keyword);
        Long count = deviceDao.countDevices(keyword);
        log.info("查询设备总数完成，共 {} 条记录", count);
        return count;
    }

    @Override
    public Device getDeviceById(Long parameterId) {
        if (parameterId == null) {
            log.error("查询设备失败：ID不能为空");
            throw new ErrorMsg(-1, "查询设备失败：ID不能为空.");
        }

        Device device = deviceDao.findById(parameterId);
        if (device == null) {
            log.error("查询设备失败：指定ID的设备不存在: {}", parameterId);
            throw new ErrorMsg(-1, "查询设备失败：指定ID的设备不存在.");
        }

        log.info("查询设备成功: {}", device);
        return device;
    }

    @Override
    public Device updateDevice(Device device) {
        synchronized (deviceLock) {
            if (device.getParameterId() == null) {
                log.error("设备更新失败：ID不能为空");
                throw new ErrorMsg(-1, "设备更新失败：ID不能为空.");
            }
            
            // 参数校验
            validateDeviceForUpdate(device);
            
            // 获取当前数据库中的设备信息
            Device existingDevice = deviceDao.findById(device.getParameterId());
            if (existingDevice == null) {
                log.error("设备更新失败：指定ID的设备不存在: {}", device.getParameterId());
                throw new ErrorMsg(-1, "设备更新失败：指定ID的设备不存在.");
            }

            // 检查参数代码是否与其他设备冲突（排除自身）
            if (!existingDevice.getParameterCode().equals(device.getParameterCode())) {
                Device conflictDevice = deviceDao.findByParameterCode(device.getParameterCode().trim());
                if (conflictDevice != null && !conflictDevice.getParameterId().equals(device.getParameterId())) {
                    log.error("设备更新失败：参数代码已被其他设备使用: {}", device.getParameterCode());
                    throw new ErrorMsg(-1, "设备更新失败：参数代码已被其他设备使用.");
                }
            }

            // 更新设备
            int result = deviceDao.updateDevice(device);
            if (result <= 0) {
                log.error("设备更新失败: {}", device);
                throw new ErrorMsg(-1, "数据库中设备更新失败.");
            }
            log.info("设备更新成功: {}", device);
            // 直接返回更新后的设备对象，避免重复查询
            return device;
        }
    }

    @Override
    public boolean deleteDevice(Long parameterId) {
        if (parameterId == null) {
            log.error("设备删除失败：ID不能为空");
            throw new ErrorMsg(-1, "设备删除失败：ID不能为空.");
        }

        // 获取当前数据库中的设备信息
        Device existingDevice = deviceDao.findById(parameterId);
        if (existingDevice == null) {
            log.error("设备删除失败：指定ID的设备不存在: {}", parameterId);
            throw new ErrorMsg(-1, "设备删除失败：指定ID的设备不存在.");
        }
        
        synchronized (deviceLock) {
            // 删除设备
            int result = deviceDao.deleteDevice(parameterId);
            boolean success = result > 0;

            if (success) {
                log.info("设备删除成功: {}", parameterId);
            } else {
                log.error("设备删除失败: {}", parameterId);
            }

            return success;
        }
    }

    /**
     * 创建设备时的参数校验
     */
    private void validateDeviceForCreate(Device device) {
        if (device == null) {
            throw new ErrorMsg(-1, "设备信息不能为空.");
        }
        if (device.getDeviceType() == null || device.getDeviceType().trim().isEmpty()) {
            throw new ErrorMsg(-1, "设备类型不能为空.");
        }
        if (device.getParameterCode() == null || device.getParameterCode().trim().isEmpty()) {
            throw new ErrorMsg(-1, "参数代码不能为空.");
        }
        // 创建时不校验版本，由系统自动设置
    }

    /**
     * 更新设备时的参数校验
     */
    private void validateDeviceForUpdate(Device device) {
        if (device == null) {
            throw new ErrorMsg(-1, "设备信息不能为空.");
        }
        if (device.getDeviceType() == null || device.getDeviceType().trim().isEmpty()) {
            throw new ErrorMsg(-1, "设备类型不能为空.");
        }
        if (device.getParameterCode() == null || device.getParameterCode().trim().isEmpty()) {
            throw new ErrorMsg(-1, "参数代码不能为空.");
        }
        if (device.getVersion() == null || device.getVersion().trim().isEmpty()) {
            throw new ErrorMsg(-1, "版本不能为空.");
        }
    }
}
