/**
 * 定时器函数
 *
 * @param {*} cb 定时回调
 * @param {*} time 每次执行过后多久再次执行
 */
export function timer(cb, interval = 4e3, { immediate = true } = {}) {
  let isRun = true;
  let timerId = null;

  const exec = (timeout) => {
    return new Promise((resolve, reject) => {
      timerId = setTimeout(() => {
        if (isRun) {
          resolve(cb(cancel));
        }
      }, timeout || interval);
    });
  };

  const run = async () => {
    try {
      if (immediate) {
        await exec(0);
      }
    } finally {
      // eslint-disable-next-line no-unmodified-loop-condition
      while (isRun) {
        try {
          await exec();
        } finally {
          await exec();
        }
      }
    }
  };

  const cancel = () => {
    isRun = false;
    if (timerId) {
      window.clearTimeout(timerId);
    }
  };

  run();
  return cancel;
}

/**
 * 空白值
 */
export function isBlankValue(value) {
  if (value === "" || value === undefined || value === null) {
    return true;
  }
  return false;
}

/**
 * 空白值的默认填充
 */
export function fillBlankValue(value, signal) {
  if (isBlankValue(value)) {
    return signal || "--";
  }
  return value;
}

export function fillBlankObject(obj, keys = []) {
  _.each(obj, (value, key) => {
    if (!keys.length || ~keys.indexOf(key)) {
      obj[key] = fillBlankValue(value);
    }
  });
}

export default {
  timer,
  fillBlankValue,
  fillBlankObject,
};
