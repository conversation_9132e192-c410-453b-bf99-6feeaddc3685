package com.cet.electric.ngapserver.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "线路代码查询参数")
public class LineCodeQueryDTO {
    @ApiModelProperty(value = "页码", example = "1")
    private Integer page = 1;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer size = 10;

    @ApiModelProperty(value = "排序字段", example = "lineCodeId")
    private String sortBy = "lineCodeId";

    @ApiModelProperty(value = "排序方式", example = "desc")
    private String sortOrder = "desc";

    @ApiModelProperty(value = "搜索关键词", required = false)
    private String keyword;

    @Override
    public String toString() {
        return "LineCodeQueryDTO [page=" + page + ", size=" + size + ", sortBy=" + sortBy +
                ", sortOrder=" + sortOrder + ", keyword=" + keyword + "]";
    }
}

