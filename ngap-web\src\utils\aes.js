import CryptoJS from "crypto-js";

const KEY = "8924534290ABCDEF1264147890ACAB56";
const IV = "2934577290ABCDEF1264147890ACAE75";

// 16进制字符串 —>WordArray对象
// CryptoJS.enc.Hex.parse(string);
// utf-8  -> —>WordArray对象
// CryptoJS.enc.Utf8.parse(string);

//CBC模式
export default {
  // 特别注意：此次是将密文写死到函数内部，也可以当成函数的参数进行动态绑定密文,列如：
  // 只是前两句不一样，外加两个形参.
  // encrypt(word,keyStr, ivStr){ keyStr = keyStr ? keyStr : "abcdefG1234567"; ivStr = ivStr ? ivStr : "abcdefG1234567"; ...........(之后内容与以下一样)}
  // 加密
  encrypt(word, keyStr, ivStr) {
    let key = keyStr ? keyStr : KEY;
    let iv = ivStr ? ivStr : IV;
    key = CryptoJS.enc.Utf8.parse(key);
    iv = CryptoJS.enc.Hex.parse(iv);
    // let srcs = CryptoJS.enc.Utf8.parse(word); /*  CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(word)) */

    let encrypted = CryptoJS.AES.encrypt(word, key, {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });

    return encrypted.toString();
  },
  // 解密
  decrypt(word, keyStr, ivStr) {
    let key = keyStr ? keyStr : KEY;
    let iv = ivStr ? ivStr : IV;
    key = CryptoJS.enc.Utf8.parse(key);
    iv = CryptoJS.enc.Hex.parse(iv);

    // let srcs = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Base64.parse(word));

    let decrypt = CryptoJS.AES.decrypt(word, key, {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    return decrypt.toString(CryptoJS.enc.Utf8);
  }
};

// ECB
// export default {
//   //随机生成指定数量的16进制key
//   generatekey(num) {
//     let library = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
//     let key = "";
//     for (var i = 0; i < num; i++) {
//       let randomPoz = Math.floor(Math.random() * library.length);
//       key += library.substring(randomPoz, randomPoz + 1);
//     }
//     return key;
//   },

//   //加密
//   encrypt(word, keyStr) {
// keyStr = keyStr ? keyStr : "8924534290ABCDEF1264147890ACAB56"; //判断是否存在ksy，不存在就用定义好的key
// var key = CryptoJS.enc.Utf8.parse(keyStr);
// var srcs = CryptoJS.enc.Utf8.parse(word);
// var encrypted = CryptoJS.AES.encrypt(srcs, key, { mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 });
// return encrypted.toString();
//   },
//   //解密
//   decrypt(word, keyStr) {
//     let key = keyStr ? keyStr : KEY;
// key = CryptoJS.enc.Utf8.parse(keyStr);
// let decrypt = CryptoJS.AES.decrypt(word, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 });
// return CryptoJS.enc.Utf8.stringify(decrypt).toString();

//   }
// };
