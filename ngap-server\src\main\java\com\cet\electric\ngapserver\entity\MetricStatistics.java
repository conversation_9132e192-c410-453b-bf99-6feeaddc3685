package com.cet.electric.ngapserver.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 指标统计信息实体类
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MetricStatistics {
    /**
     * 最小值
     */
    private Double min;

    /**
     * 最大值
     */
    private Double max;

    /**
     * 合格率
     */
    private Double rate;

    public Boolean getHasMin() {
        return min != null;
    }

    public Boolean getHasMax() {
        return max != null;
    }

    public Boolean getHasRate() {
        return rate != null;
    }
}

