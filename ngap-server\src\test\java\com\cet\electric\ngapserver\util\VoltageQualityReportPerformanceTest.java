package com.cet.electric.ngapserver.util;

import com.cet.electric.ngapserver.util.excel.ExcelCacheManager;
import com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.TemporaryFolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

import static org.junit.Assert.*;

/**
 * 电压合格率报表性能测试
 * 验证优化效果和性能提升
 * 
 * <AUTHOR>
 */
public class VoltageQualityReportPerformanceTest {
    
    private static final Logger log = LoggerFactory.getLogger(VoltageQualityReportPerformanceTest.class);
    
    @Rule
    public TemporaryFolder tempFolder = new TemporaryFolder();
    
    private File smallTestFile;
    private File mediumTestFile;
    private File largeTestFile;
    
    @Before
    public void setUp() throws IOException {
        // 创建不同大小的测试文件
        smallTestFile = createTestFile("small_test.xlsx", 10, 96);
        mediumTestFile = createTestFile("medium_test.xlsx", 100, 96);
        largeTestFile = createTestFile("large_test.xlsx", 1000, 96);
        
        // 清空缓存和性能统计
        ExcelCacheManager.getInstance().clear();
        VoltagePerformanceMonitor.getInstance().reset();
        
        log.info("性能测试环境准备完成");
    }
    
    @After
    public void tearDown() {
        // 输出性能报告
        VoltagePerformanceMonitor.PerformanceReport report = 
            VoltagePerformanceMonitor.getInstance().getPerformanceReport();
        log.info("性能测试报告:\n{}", report);
        
        // 清理
        ExcelCacheManager.getInstance().clear();
        VoltagePerformanceMonitor.getInstance().reset();
    }
    
    @Test
    public void testSmallFilePerformance() {
        log.info("=== 小文件性能测试 ===");
        
        // 第一次处理（无缓存）
        long startTime1 = System.currentTimeMillis();
        VoltageQualityReport.generateVoltageReport(smallTestFile.getAbsolutePath());
        long duration1 = System.currentTimeMillis() - startTime1;
        
        // 验证报表文件生成
        File reportFile = new File(smallTestFile.getParent(), "电压合格率报表.xlsx");
        assertTrue("报表文件应该存在", reportFile.exists());
        
        // 第二次处理（有缓存）
        long startTime2 = System.currentTimeMillis();
        VoltageQualityReport.generateVoltageReport(smallTestFile.getAbsolutePath());
        long duration2 = System.currentTimeMillis() - startTime2;
        
        log.info("小文件处理时间 - 第一次: {}ms, 第二次: {}ms, 提升: {}%",
                duration1, duration2, calculateImprovement(duration1, duration2));

        // 缓存应该提升性能或保持相当（允许200%的波动，因为小文件可能有JVM预热等因素）
        assertTrue("缓存应该提升性能或保持相当", duration2 <= duration1 * 3.0);

        // 验证缓存效果
        ExcelCacheManager.CacheStats cacheStats = ExcelCacheManager.getInstance().getStats();
        assertTrue("应该有缓存命中", cacheStats.hits > 0);
        assertTrue("缓存命中率应该大于0", cacheStats.hitRate > 0);
    }
    
    @Test
    public void testMediumFilePerformance() {
        log.info("=== 中等文件性能测试 ===");
        
        long startTime = System.currentTimeMillis();
        VoltageQualityReport.generateVoltageReport(mediumTestFile.getAbsolutePath());
        long duration = System.currentTimeMillis() - startTime;
        
        // 验证报表文件生成
        File reportFile = new File(mediumTestFile.getParent(), "电压合格率报表.xlsx");
        assertTrue("报表文件应该存在", reportFile.exists());
        assertTrue("报表文件应该有内容", reportFile.length() > 0);
        
        log.info("中等文件处理时间: {}ms", duration);
        
        // 性能应该在合理范围内（根据实际情况调整）
        assertTrue("处理时间应该在合理范围内", duration < 30000); // 30秒
    }
    
    @Test
    public void testLargeFilePerformance() {
        log.info("=== 大文件性能测试 ===");
        
        long startTime = System.currentTimeMillis();
        VoltageQualityReport.generateVoltageReport(largeTestFile.getAbsolutePath());
        long duration = System.currentTimeMillis() - startTime;
        
        // 验证报表文件生成
        File reportFile = new File(largeTestFile.getParent(), "电压合格率报表.xlsx");
        assertTrue("报表文件应该存在", reportFile.exists());
        assertTrue("报表文件应该有内容", reportFile.length() > 0);
        
        log.info("大文件处理时间: {}ms", duration);
        
        // 获取性能报告
        VoltagePerformanceMonitor.PerformanceReport report = 
            VoltagePerformanceMonitor.getInstance().getPerformanceReport();
        
        assertTrue("应该处理了文件", report.totalFiles > 0);
        assertTrue("应该处理了记录", report.totalRecords > 0);
        assertTrue("处理速度应该合理", report.getProcessingSpeed() > 0);
        
        log.info("处理速度: {} 记录/秒", String.format("%.2f", report.getProcessingSpeed()));
    }
    
    @Test
    public void testCacheEfficiency() {
        log.info("=== 缓存效率测试 ===");

        String filePath = mediumTestFile.getAbsolutePath();

        // 缓存预热 - 先处理一次以建立缓存
        VoltageQualityReport.generateVoltageReport(filePath);

        // 清空性能统计，重新开始计时
        VoltagePerformanceMonitor.getInstance().reset();

        // 多次处理同一文件
        long[] durations = new long[5];
        for (int i = 0; i < 5; i++) {
            long startTime = System.currentTimeMillis();
            VoltageQualityReport.generateVoltageReport(filePath);
            durations[i] = System.currentTimeMillis() - startTime;
            log.info("第{}次处理时间: {}ms", i + 1, durations[i]);
        }
        
        // 验证缓存效果
        ExcelCacheManager.CacheStats cacheStats = ExcelCacheManager.getInstance().getStats();
        log.info("缓存统计: {}", cacheStats);
        
        assertTrue("应该有缓存命中", cacheStats.hits > 0);
        assertTrue("缓存命中率应该合理", cacheStats.hitRate > 0.1); // 10%以上命中率（调整为更现实的期望）

        // 后续处理应该更快或保持相当
        for (int i = 1; i < durations.length; i++) {
            assertTrue("后续处理应该更快或相当", durations[i] <= durations[0] * 1.5); // 允许50%波动
        }
    }
    
    @Test
    public void testMemoryEfficiency() {
        log.info("=== 内存效率测试 ===");
        
        // 记录初始内存使用
        Runtime runtime = Runtime.getRuntime();
        runtime.gc(); // 建议垃圾回收
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 处理大文件
        VoltageQualityReport.generateVoltageReport(largeTestFile.getAbsolutePath());
        
        // 记录处理后内存使用
        runtime.gc(); // 建议垃圾回收
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        
        long memoryUsed = finalMemory - initialMemory;
        log.info("内存使用: 初始={}MB, 最终={}MB, 增加={}MB", 
                initialMemory / 1024 / 1024, finalMemory / 1024 / 1024, memoryUsed / 1024 / 1024);
        
        // 内存使用应该在合理范围内
        assertTrue("内存使用应该合理", memoryUsed < 500 * 1024 * 1024); // 500MB
    }
    
    /**
     * 创建测试文件
     */
    private File createTestFile(String fileName, int userCount, int voltageColumns) throws IOException {
        File file = tempFolder.newFile(fileName);
        
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("用户电压");
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("用户名称");
            for (int i = 1; i <= voltageColumns; i++) {
                headerRow.createCell(i).setCellValue("u" + i);
            }
            
            // 创建测试数据
            for (int i = 0; i < userCount; i++) {
                Row row = sheet.createRow(i + 1);
                row.createCell(0).setCellValue("用户" + (i + 1));
                
                // 生成随机电压数据
                for (int j = 1; j <= voltageColumns; j++) {
                    // 生成在合格范围内外的随机电压值
                    double voltage;
                    if (Math.random() < 0.8) {
                        // 80%概率生成合格电压
                        voltage = 198 + Math.random() * (242 - 198);
                    } else if (Math.random() < 0.5) {
                        // 10%概率生成超上限电压
                        voltage = 242 + Math.random() * 20;
                    } else {
                        // 10%概率生成低于下限电压
                        voltage = 180 + Math.random() * 18;
                    }
                    row.createCell(j).setCellValue(voltage);
                }
            }
            
            try (FileOutputStream fos = new FileOutputStream(file)) {
                workbook.write(fos);
            }
        }
        
        log.info("创建测试文件: {} - 用户数: {}, 电压列数: {}, 文件大小: {}KB", 
                fileName, userCount, voltageColumns, file.length() / 1024);
        
        return file;
    }
    
    /**
     * 计算性能提升百分比
     */
    private double calculateImprovement(long before, long after) {
        if (before == 0) return 0;
        return ((double) (before - after) / before) * 100;
    }
}
