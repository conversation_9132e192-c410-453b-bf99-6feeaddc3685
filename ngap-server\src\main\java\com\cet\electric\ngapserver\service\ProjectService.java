package com.cet.electric.ngapserver.service;

import com.cet.electric.ngapserver.entity.Project;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ProjectService {

    /**
     *
     * 创建项目
     *
     * @param project 待创建的项目对象，必须包含项目名称
     * @return 创建成功的项目对象，包含生成的项目ID
     */
    Project createProject(Project project);

    /**
     * 获取项目场景列表
     *
     * @return 项目场景列表，key为英文，value为中文
     */
    Map<String, String> getProjectScenarios();

    /**
     * 查询项目总数
     *
     * @param projectType 项目类型(可选)
     * @param keyword     搜索关键词(可选)
     * @param startTime   开始时间(可选)
     * @param endTime     结束时间(可选)
     * @return 项目总数
     */
    Long countProjects(String projectType, String keyword, Long startTime, Long endTime);

    /**
     * 分页查询项目
     *
     * @param page        页码
     * @param size        每页大小
     * @param sortBy      排序字段
     * @param sortOrder   排序方向
     * @param projectType 项目类型(可选)
     * @param keyword     搜索关键词(可选)
     * @param startTime   起始时间(时间戳-毫秒)
     * @param endTime     结束时间(时间戳-毫秒)
     * @return 分页结果
     */
    List<Project> getProjects(Integer page, Integer size, String sortBy, String sortOrder, String projectType, String keyword, Long startTime, Long endTime);

    /**
     * 复制项目
     *
     * @param projectId 原项目ID
     * @return 复制后的新项目
     */
    Project copyProject(Long projectId);

    /**
     * 重命名项目
     *
     * @param projectId 项目ID
     * @param newName 新的项目名称
     * @return 更新后的项目信息
     */
    Project renameProject(Long projectId, String newName);

    /**
     * 删除项目
     *
     * @param projectId 项目ID
     */
    void deleteProject(Long projectId);

    /**
     * 导出项目配置文件
     *
     * @param projectId 项目ID
     * @param response  HTTP响应对象，用于写入文件流
     */
    void exportProject(Long projectId, HttpServletResponse response);

    /**
     * 导入项目配置文件
     *
     * @param file 上传的项目配置文件
     * @return 导入成功的项目信息
     */
    Project importProject(MultipartFile file);

}
